# EventBus Package Documentation

This directory contains detailed documentation for all the main classes in the EventBus package.

## Class Documentation Index

### Core Components
- [EventEngine](EventEngine.md) - Core component for event processing and distribution
- [EventPublisher](EventPublisher.md) - Publishes events to different topics using ZeroMQ
- [EventSubscriber](EventSubscriber.md) - Subscribes to events from different sources
- [EventStore](EventStore.md) - Provides persistent storage for events
- [EventTracker](EventTracker.md) - Tracks the status and progress of events

### Event Routing
- [LocalEventRouter](LocalEventRouter.md) - Routes events to appropriate local handlers
- [RemoteEventRouter](RemoteEventRouter.md) - Routes events to appropriate remote handlers
- [RemoteEventForwarder](RemoteEventForwarder.md) - Forwards events to remote components
- [RemoteEventSubscriber](RemoteEventSubscriber.md) - Subscribes to events from remote publishers

### Event Handling
- [UIActionEventHandler](UIActionEventHandler.md) - Handles events triggered by user interface actions
- [EventBusConstants](EventBusConstants.md) - Defines constants used throughout the eventbus package

## Usage

Each class documentation file follows a consistent structure:

1. **Overview** - Purpose and role of the class
2. **Key Responsibilities** - Main functions and responsibilities
3. **Component Architecture** - Structure and relationships
4. **Operational Flow** - How the component works
5. **Configuration** - Configuration options
6. **Event Bus Messages** - Input and output events
7. **Error Handling** - Error handling mechanisms
8. **Performance Considerations** - Performance optimizations
9. **Best Practices** - Recommendations for optimal use
10. **Code Examples** - Sample code demonstrating usage
11. **Related Components** - Other components that interact with this one
12. **Future Enhancements** - Planned improvements

## Integration Points

The EventBus package integrates with several other packages in the Motadata platform:

- **NMS** - For monitoring events and notifications
- **Policy** - For policy-related events
- **Notification** - For alert and notification delivery
- **Visualization** - For real-time data updates
- **API** - For handling API requests and responses
- **Job** - For job scheduling and execution events
- **Flow** - For network flow events
- **Log** - For log events and processing
