# DiscoveryEngine Class Documentation

## Overview

The `DiscoveryEngine` is a core component of the NMS package responsible for discovering network devices and systems using various protocols (SNMP, WMI, SSH, etc.). It manages discovery profiles, schedules discovery jobs, and processes discovery results to create monitored objects in the system.

## Key Responsibilities

- Executing discovery operations based on configured profiles
- Managing worker allocation for discovery tasks
- Processing and validating discovery results
- Coordinating with ObjectManager for object creation
- Handling different discovery methods (IP range, subnet, individual IPs)
- Supporting multiple protocols for discovery (SNMP, WMI, SSH, etc.)
- Implementing batch processing for efficient discovery
- Managing timeouts and retries for discovery operations

## Component Architecture

The `DiscoveryEngine` is implemented as a Vert.x verticle that receives discovery requests through the event bus and executes them using appropriate worker pools. It communicates with other components through the Vert.x event bus.

### Class Diagram

```
+-------------------+
| DiscoveryEngine   |
+-------------------+
| - vertx: Vertx    |
| - logger: Logger  |
| - config: JsonObject |
| - workerPool: WorkerPool |
+-------------------+
| + start(): void   |
| + stop(): void    |
| - executeDiscovery(): void |
| - processResults(): void |
| - createObjects(): void |
| - handleTimeout(): void |
+-------------------+
```

### Dependencies

- **Plugin Engine**: For executing the actual discovery operations
- **Object Manager**: For creating and managing discovered objects
- **Response Processor**: For processing discovery results
- **Event Bus**: For communication with other components
- **Configuration Store**: For retrieving discovery profiles and credentials

## Operational Flow

1. The `DiscoveryEngine` receives a discovery request through the event bus
2. It validates the discovery profile and parameters
3. It breaks down the discovery scope into manageable batches
4. It allocates workers from the worker pool
5. It executes discovery operations through the plugin engine
6. It processes discovery results and creates objects
7. It notifies the requester of discovery completion

### Sequence Diagram

```
┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
│ API/UI          │ │ DiscoveryEngine │ │ Plugin Engine   │ │ Object Manager  │ │ Response        │
│                 │ │                 │ │                 │ │                 │ │ Processor       │
└────────┬────────┘ └────────┬────────┘ └────────┬────────┘ └────────┬────────┘ └────────┬────────┘
         │                    │                   │                   │                   │
         │ Discovery Request  │                   │                   │                   │
         ├────────────────────►                   │                   │                   │
         │                    │                   │                   │                   │
         │                    │ Validate Profile  │                   │                   │
         │                    ├─────────────────► │                   │                   │
         │                    │                   │                   │                   │
         │                    │ Create Batches    │                   │                   │
         │                    ├─────────────────► │                   │                   │
         │                    │                   │                   │                   │
         │                    │ Execute Discovery │                   │                   │
         │                    ├────────────────────►                  │                   │
         │                    │                   │                   │                   │
         │                    │ Set Timeout Timer │                   │                   │
         │                    ├─────────────────► │                   │                   │
         │                    │                   │                   │                   │
         │                    │                   │ Return Results    │                   │
         │                    │◄────────────────────                  │                   │
         │                    │                   │                   │                   │
         │                    │ Cancel Timeout    │                   │                   │
         │                    ├─────────────────► │                   │                   │
         │                    │                   │                   │                   │
         │                    │ Process Results   │                   │                   │
         │                    ├─────────────────► │                   │                   │
         │                    │                   │                   │                   │
         │                    │ Create Objects    │                   │                   │
         │                    ├──────────────────────────────────────►│                   │
         │                    │                   │                   │                   │
         │                    │ Forward Results   │                   │                   │
         │                    ├────────────────────────────────────────────────────────────►
         │                    │                   │                   │                   │
         │ Discovery Complete │                   │                   │                   │
         │◄────────────────────                   │                   │                   │
         │                    │                   │                   │                   │
```

## Discovery Methods

The `DiscoveryEngine` supports several discovery methods:

### IP Range Discovery

Discovers devices within a specified IP range:

```
***********-*************
```

### Subnet Discovery

Discovers devices within a specified subnet:

```
***********/24
```

### Individual IP Discovery

Discovers specific devices by IP address:

```
***********, ***********, ***********
```

### DNS Name Discovery

Discovers devices by DNS name:

```
server1.example.com, server2.example.com
```

### Mixed Discovery

Combines multiple discovery methods:

```
***********/24, ********-********0, server1.example.com
```

## Discovery Protocols

The `DiscoveryEngine` supports multiple protocols for discovery:

### SNMP Discovery

Uses SNMP to discover network devices:

- Supports SNMPv1, SNMPv2c, and SNMPv3
- Retrieves system information (sysDescr, sysObjectID, etc.)
- Identifies device type based on OID mappings
- Discovers interfaces and other components

### WMI Discovery

Uses WMI to discover Windows systems:

- Retrieves system information (OS, hardware, etc.)
- Identifies installed software and services
- Discovers running processes and performance metrics
- Requires Windows credentials

### SSH Discovery

Uses SSH to discover Unix/Linux systems:

- Retrieves system information (OS, hardware, etc.)
- Identifies installed software and services
- Discovers running processes and performance metrics
- Requires SSH credentials

### VMware Discovery

Uses VMware API to discover VMware environments:

- Discovers ESXi hosts and vCenter servers
- Identifies virtual machines and their configurations
- Retrieves resource allocation and usage information
- Requires VMware credentials

### Cloud Discovery

Uses cloud provider APIs to discover cloud resources:

- Supports AWS, Azure, and other cloud providers
- Discovers virtual machines, storage, and other resources
- Retrieves configuration and usage information
- Requires cloud provider credentials

## Configuration

The `DiscoveryEngine`'s behavior can be configured through various settings:

### System Configuration

| Parameter | Description | Default Value |
|-----------|-------------|---------------|
| `DISCOVERY_WORKER_POOL_SIZE` | Size of the discovery worker pool | 10 |
| `DISCOVERY_TIMEOUT_MS` | Default timeout for discovery operations (ms) | 60000 |
| `DISCOVERY_MAX_RETRIES` | Maximum number of retries for failed operations | 2 |
| `DISCOVERY_BATCH_SIZE` | Maximum number of IPs in a discovery batch | 256 |
| `DISCOVERY_CONCURRENT_BATCHES` | Maximum number of concurrent discovery batches | 4 |
| `DISCOVERY_THROTTLE_DELAY_MS` | Delay between discovery batches (ms) | 1000 |

### Discovery Profile Configuration

| Parameter | Description | Default Value |
|-----------|-------------|---------------|
| `name` | Name of the discovery profile | Required |
| `description` | Description of the discovery profile | Optional |
| `scope` | Discovery scope (IP range, subnet, etc.) | Required |
| `protocols` | Protocols to use for discovery | Required |
| `credentials` | Credentials to use for discovery | Required |
| `timeout` | Timeout for discovery operations (ms) | 60000 |
| `retries` | Number of retries for failed operations | 2 |
| `excludeIPs` | IPs to exclude from discovery | Empty |
| `schedule` | Schedule for recurring discovery | None |
| `options` | Protocol-specific options | Empty |

## Event Bus Messages

### Input Events

| Event Type | Description | Parameters |
|------------|-------------|------------|
| `EVENT_DISCOVERY_RUN` | Request to run a discovery | `profile`: JsonObject, `scope`: String (optional), `credentials`: JsonArray (optional) |
| `EVENT_DISCOVERY_ABORT` | Request to abort a discovery | `discoveryId`: String |
| `EVENT_DISCOVERY_STATUS` | Request for discovery status | `discoveryId`: String |

### Output Events

| Event Type | Description | Parameters |
|------------|-------------|------------|
| `EVENT_DISCOVERY_STARTED` | Notification that discovery has started | `discoveryId`: String, `profile`: JsonObject |
| `EVENT_DISCOVERY_PROGRESS` | Progress update for discovery | `discoveryId`: String, `progress`: Integer, `total`: Integer, `discovered`: Integer |
| `EVENT_DISCOVERY_COMPLETE` | Notification that discovery is complete | `discoveryId`: String, `results`: JsonObject |
| `EVENT_DISCOVERY_ABORTED` | Notification that discovery was aborted | `discoveryId`: String, `reason`: String |
| `EVENT_DISCOVERY_ERROR` | Notification of discovery error | `discoveryId`: String, `error`: String |

## Error Handling

The `DiscoveryEngine` implements several error handling mechanisms:

1. **Timeout Handling**:
   - Sets timeout timer for each discovery operation
   - Cancels operation if timeout occurs
   - Generates timeout error event
   - Implements configurable timeout values

2. **Retry Logic**:
   - Retries failed operations based on configuration
   - Implements exponential backoff
   - Tracks retry history
   - Limits maximum retries

3. **Error Classification**:
   - Categorizes errors (network, authentication, timeout, etc.)
   - Applies different handling based on error type
   - Provides detailed error information
   - Tracks error patterns

4. **Resource Protection**:
   - Prevents worker pool exhaustion
   - Implements circuit breaker pattern for problematic networks
   - Throttles discovery during high error rates
   - Protects against cascading failures

5. **Result Validation**:
   - Validates discovery results
   - Handles partial results
   - Detects and handles corrupt data
   - Ensures consistent result format

## Performance Considerations

The `DiscoveryEngine` is designed to be efficient and scalable:

1. **Batch Processing**:
   - Breaks down large discovery scopes into manageable batches
   - Processes batches concurrently for improved throughput
   - Implements throttling to prevent network congestion
   - Optimizes batch size based on network conditions

2. **Worker Pool Management**:
   - Dedicated pool for discovery operations
   - Dynamic sizing based on workload
   - Efficient worker allocation and release
   - Monitoring of pool utilization

3. **Protocol Optimization**:
   - Uses optimized protocol implementations
   - Minimizes network round-trips
   - Implements connection pooling where applicable
   - Prioritizes lightweight protocols when multiple are available

4. **Memory Management**:
   - Efficient handling of discovery results
   - Proper cleanup of temporary objects
   - Minimized object creation during processing
   - Streaming of large result sets

## Monitoring and Metrics

The `DiscoveryEngine` exposes several metrics for monitoring its performance:

| Metric | Description | Type |
|--------|-------------|------|
| `discovery.operations.total` | Total number of discovery operations | Counter |
| `discovery.operations.success` | Number of successful discovery operations | Counter |
| `discovery.operations.error` | Number of failed discovery operations | Counter |
| `discovery.operations.timeout` | Number of timed out discovery operations | Counter |
| `discovery.operations.retry` | Number of retried discovery operations | Counter |
| `discovery.batch.size` | Size of discovery batches | Histogram |
| `discovery.batch.count` | Number of batches processed | Counter |
| `discovery.duration` | Duration of discovery operations | Histogram |
| `discovery.objects.discovered` | Number of objects discovered | Counter |
| `discovery.workers.active` | Number of active discovery workers | Gauge |
| `discovery.workers.idle` | Number of idle discovery workers | Gauge |
| `discovery.queue.size` | Size of the discovery queue | Gauge |

## Best Practices

For optimal use of the `DiscoveryEngine`:

1. **Scope Management**:
   - Use appropriate discovery scopes to limit network impact
   - Break large networks into smaller discovery profiles
   - Exclude known problematic or irrelevant IP ranges
   - Use targeted discovery for specific device types

2. **Credential Management**:
   - Provide appropriate credentials for each protocol
   - Use least-privilege accounts for discovery
   - Rotate credentials regularly
   - Monitor failed authentication attempts

3. **Protocol Selection**:
   - Choose appropriate protocols for different device types
   - Use SNMP for network devices
   - Use WMI for Windows systems
   - Use SSH for Unix/Linux systems
   - Use specialized protocols for virtualization and cloud

4. **Scheduling**:
   - Schedule discoveries during low-traffic periods
   - Stagger discoveries to minimize network impact
   - Implement progressive discovery approaches
   - Adjust frequency based on network volatility

5. **Resource Management**:
   - Monitor system resources during discovery
   - Adjust worker pool size based on available resources
   - Implement throttling during high load
   - Balance between discovery speed and system impact

## Code Examples

### Running a Discovery

```java
// Create discovery profile
JsonObject profile = new JsonObject()
    .put("name", "Network Discovery")
    .put("description", "Discover network devices")
    .put("scope", "***********/24")
    .put("protocols", new JsonArray().add("SNMP"))
    .put("credentials", new JsonArray().add(
        new JsonObject()
            .put("id", 123)
            .put("type", "SNMP")
    ))
    .put("timeout", 60000)
    .put("retries", 2);

// Send discovery request
vertx.eventBus().request(
    NMSConstants.EVENT_DISCOVERY_RUN,
    profile,
    ar -> {
        if (ar.succeeded()) {
            JsonObject response = (JsonObject) ar.result().body();
            String discoveryId = response.getString("discoveryId");
            System.out.println("Discovery started with ID: " + discoveryId);
        } else {
            System.err.println("Failed to start discovery: " + ar.cause().getMessage());
        }
    }
);
```

### Processing Discovery Results

```java
private void processDiscoveryResults(JsonObject results) {
    JsonArray devices = results.getJsonArray("devices");
    
    for (int i = 0; i < devices.size(); i++) {
        JsonObject device = devices.getJsonObject(i);
        
        // Extract device information
        String ip = device.getString("ip");
        String type = device.getString("type");
        String name = device.getString("name", ip);
        
        // Create object configuration
        JsonObject objectConfig = new JsonObject()
            .put("name", name)
            .put("type", type)
            .put("ip", ip)
            .put("category", NMSConstants.getTypeCategory(type))
            .put("credentialId", device.getLong("credentialId"))
            .put("properties", device.getJsonObject("properties", new JsonObject()));
        
        // Create object
        createObject(objectConfig);
    }
}
```

### Handling Discovery Progress

```java
// Register for discovery progress events
vertx.eventBus().consumer(NMSConstants.EVENT_DISCOVERY_PROGRESS, message -> {
    JsonObject progress = (JsonObject) message.body();
    String discoveryId = progress.getString("discoveryId");
    int current = progress.getInteger("progress");
    int total = progress.getInteger("total");
    int discovered = progress.getInteger("discovered");
    
    // Calculate percentage
    int percentage = (total > 0) ? (current * 100 / total) : 0;
    
    // Update UI
    updateProgressBar(discoveryId, percentage);
    updateDiscoveredCount(discoveryId, discovered);
});
```

### Aborting a Discovery

```java
// Abort a running discovery
vertx.eventBus().request(
    NMSConstants.EVENT_DISCOVERY_ABORT,
    new JsonObject().put("discoveryId", discoveryId),
    ar -> {
        if (ar.succeeded()) {
            System.out.println("Discovery aborted successfully");
        } else {
            System.err.println("Failed to abort discovery: " + ar.cause().getMessage());
        }
    }
);
```

## Related Components

- **RediscoverEngine**: Performs rediscovery operations on existing objects
- **TopologyEngine**: Maps network topology based on discovery results
- **ObjectManager**: Creates and manages discovered objects
- **Plugin Engine**: Executes the actual discovery operations
- **Response Processor**: Processes discovery results

## Future Enhancements

Planned improvements for the `DiscoveryEngine`:

1. **Enhanced Discovery Algorithms**:
   - Adaptive discovery based on network topology
   - Progressive discovery with incremental scope expansion
   - Intelligent retry and timeout management
   - Improved device type identification

2. **Advanced Protocol Support**:
   - Enhanced SNMP discovery with MIB-based identification
   - Improved WMI discovery with deeper system inspection
   - Extended SSH discovery with script-based customization
   - Additional cloud provider support

3. **Performance Optimizations**:
   - Improved batch processing algorithms
   - Enhanced worker pool management
   - Reduced memory footprint
   - Optimized network utilization

4. **Integration Enhancements**:
   - Integration with external CMDB systems
   - Support for importing discovery data from other sources
   - Export of discovery results to external systems
   - API enhancements for programmatic discovery control

5. **User Experience Improvements**:
   - Real-time discovery visualization
   - Enhanced progress reporting
   - Detailed error reporting and troubleshooting
   - Improved discovery profile management
