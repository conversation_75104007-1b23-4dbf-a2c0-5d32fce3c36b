# NMS Package Class Documentation

This directory contains detailed documentation for all the main classes in the Network Management System (NMS) package.

## Class Documentation Index

### Discovery and Topology
- [DiscoveryEngine](DiscoveryEngine.md) - Discovers network devices using various protocols
- [RediscoverEngine](./RediscoverEngine.md) - Performs rediscovery operations on existing objects
- [TopologyEngine](TopologyEngine.md) - Maps network topology and device relationships

### Metric Collection and Processing
- [MetricScheduler](MetricScheduler.md) - Schedules metric collection based on configured intervals
- [MetricPoller](MetricPoller.md) - Executes metric polling operations against monitored devices
- [MetricEnricher](MetricEnricher.md) - Processes and enriches collected metric data
- [ResponseProcessor](ResponseProcessor.md) - Handles responses from various monitoring operations
- [AutoScaler](./AutoScaler.md) - Dynamically scales polling resources based on system load

### Status Management
- [ObjectStatusCalculator](ObjectStatusCalculator.md) - Calculates and tracks object status information
- [ObjectStatusChangeEventProcessor](./ObjectStatusChangeEventProcessor.md) - Processes status changes

### SNMP Trap Handling
- [SNMPTrapListener](SNMPTrapListener.md) - Listens for SNMP traps from network devices
- [SNMPTrapProcessor](./SNMPTrapProcessor.md) - Processes and correlates received SNMP traps

### Object Management
- [ObjectManager](./ObjectManager.md) - Manages the lifecycle of monitored objects and metrics

### Constants and Utilities
- [NMSConstants](NMSConstants.md) - Provides constants and utility methods

## Usage

Each class documentation file follows a consistent structure:

1. **Overview** - Purpose and role of the class
2. **Key Responsibilities** - Main functions and responsibilities
3. **Component Architecture** - Structure and relationships
4. **Operational Flow** - How the component works
5. **Configuration** - Configuration options
6. **Event Bus Messages** - Input and output events
7. **Error Handling** - Error handling mechanisms
8. **Performance Considerations** - Performance optimizations
9. **Best Practices** - Recommendations for optimal use
10. **Code Examples** - Sample code demonstrating usage
11. **Related Components** - Other components that interact with this one
12. **Future Enhancements** - Planned improvements

## Integration Points

The NMS package integrates with several other packages in the Motadata platform:

- **EventBus** - For communication between components
- **Plugin** - For executing monitoring operations
- **Datastore** - For persisting monitoring data
- **Notification** - For alerting users about status changes
- **Visualization** - For displaying monitoring data
- **Policy** - For applying business rules to monitoring data
