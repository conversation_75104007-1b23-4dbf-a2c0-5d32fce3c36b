# MetricEnricher Class Documentation

## Overview

The `MetricEnricher` is a core component of the NMS package responsible for processing and enriching raw metric data collected by the MetricPoller. It transforms raw data into a structured format, calculates derived metrics, adds context information, and prepares the data for storage and visualization.

## Key Responsibilities

- Processing raw metric data from various sources
- Calculating derived metrics (rates, percentages, etc.)
- Adding context information to metrics (object details, tags, etc.)
- Formatting metrics for storage in the datastore
- Handling special cases for different metric types
- Correlating metrics with other data sources
- Generating status information from metric data
- Implementing thresholds and baseline comparisons

## Component Architecture

The `MetricEnricher` is implemented as a Vert.x verticle that receives metric data from the ResponseProcessor through the event bus, processes it, and forwards it to the datastore and other components.

### Class Diagram

```
+-------------------+
| MetricEnricher    |
+-------------------+
| - vertx: Vertx    |
| - logger: Logger  |
| - config: JsonObject |
| - rateCache: Map  |
+-------------------+
| + start(): void   |
| + stop(): void    |
| - processMetric(): void |
| - calculateRate(): double |
| - addContext(): void |
| - formatMetric(): JsonObject |
+-------------------+
```

### Dependencies

- **Response Processor**: For receiving raw metric data
- **Object Manager**: For retrieving object information
- **Metric Config Store**: For retrieving metric configuration
- **Datastore Manager**: For storing processed metrics
- **Object Status Calculator**: For updating object status based on metrics
- **Event Bus**: For communication with other components

## Operational Flow

1. The `MetricEnricher` receives raw metric data from the ResponseProcessor
2. It validates and normalizes the data
3. It processes the data based on the metric type (scalar, instance, rate, etc.)
4. It calculates derived metrics if needed
5. It adds context information from various sources
6. It formats the data for storage
7. It forwards the processed data to the datastore and other components

### Sequence Diagram

```
┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐
│ Response        │ │ MetricEnricher  │ │ Object/Metric   │ │ Datastore       │ │ ObjectStatus    │
│ Processor       │ │                 │ │ Config Store    │ │ Manager         │ │ Calculator      │
└────────┬────────┘ └────────┬────────┘ └────────┬────────┘ └────────┬────────┘ └────────┬────────┘
         │                    │                   │                   │                   │
         │ Raw Metric Data    │                   │                   │                   │
         ├────────────────────►                   │                   │                   │
         │                    │                   │                   │                   │
         │                    │ Get Configuration │                   │                   │
         │                    ├────────────────────►                  │                   │
         │                    │                   │                   │                   │
         │                    │ Return Config     │                   │                   │
         │                    │◄────────────────────                  │                   │
         │                    │                   │                   │                   │
         │                    │ Process Metric    │                   │                   │
         │                    ├─────────────────► │                   │                   │
         │                    │                   │                   │                   │
         │                    │ Calculate Rate    │                   │                   │
         │                    ├─────────────────► │                   │                   │
         │                    │                   │                   │                   │
         │                    │ Add Context       │                   │                   │
         │                    ├─────────────────► │                   │                   │
         │                    │                   │                   │                   │
         │                    │ Format Metric     │                   │                   │
         │                    ├─────────────────► │                   │                   │
         │                    │                   │                   │                   │
         │                    │ Store Metric      │                   │                   │
         │                    ├──────────────────────────────────────►│                   │
         │                    │                   │                   │                   │
         │                    │ Update Status     │                   │                   │
         │                    ├────────────────────────────────────────────────────────────►
         │                    │                   │                   │                   │
```

## Metric Types

The `MetricEnricher` handles several types of metrics:

### Scalar Metrics

Single-value metrics like CPU utilization, memory usage, etc.:

```json
{
  "metricId": 123,
  "objectId": 456,
  "metricType": "cpu.utilization",
  "value": 75.5,
  "unit": "%",
  "timestamp": 1625097600000
}
```

### Instance Metrics

Multiple values for instances like interfaces, processes, etc.:

```json
{
  "metricId": 123,
  "objectId": 456,
  "metricType": "interface.traffic",
  "instances": [
    {
      "instanceId": "eth0",
      "values": {
        "inOctets": 1024000,
        "outOctets": 512000,
        "status": "UP"
      }
    },
    {
      "instanceId": "eth1",
      "values": {
        "inOctets": 2048000,
        "outOctets": 1024000,
        "status": "UP"
      }
    }
  ],
  "timestamp": 1625097600000
}
```

### Rate Metrics

Metrics that require rate calculation (bytes/sec, packets/sec, etc.):

```json
{
  "metricId": 123,
  "objectId": 456,
  "metricType": "interface.traffic",
  "instances": [
    {
      "instanceId": "eth0",
      "values": {
        "inOctets": 1024000,
        "outOctets": 512000,
        "inRate": 10240,
        "outRate": 5120
      }
    }
  ],
  "timestamp": 1625097600000
}
```

### Status Metrics

Metrics that indicate the status of objects or instances:

```json
{
  "metricId": 123,
  "objectId": 456,
  "metricType": "ping",
  "value": 10.5,
  "unit": "ms",
  "status": "UP",
  "timestamp": 1625097600000
}
```

## Enrichment Process

The `MetricEnricher` performs several enrichment steps:

### 1. Data Validation and Normalization

- Validates metric data structure
- Checks for required fields
- Normalizes values (units, formats, etc.)
- Handles missing or invalid data

### 2. Rate Calculation

For counter-based metrics:

1. Retrieve previous value and timestamp
2. Calculate rate: (current_value - previous_value) / (current_timestamp - previous_timestamp)
3. Handle counter resets (when current_value < previous_value)
4. Apply unit conversions (bytes to bits, seconds to minutes, etc.)
5. Store current value and timestamp for next calculation

### 3. Context Addition

Adds context information from various sources:

- Object details (name, type, category, etc.)
- Metric configuration (thresholds, baselines, etc.)
- Tags and groups
- Geographic information
- Business context (SLAs, criticality, etc.)

### 4. Threshold Comparison

Compares metric values with configured thresholds:

- Static thresholds (warning, critical, etc.)
- Dynamic thresholds (baseline, adaptive, etc.)
- Time-based thresholds (business hours, etc.)
- Compound thresholds (multiple conditions)

### 5. Status Determination

Determines status based on thresholds and other factors:

- UP: Value within normal range
- WARNING: Value exceeds warning threshold
- CRITICAL: Value exceeds critical threshold
- DOWN: Service or component is not responding
- UNKNOWN: Unable to determine status

### 6. Formatting for Storage

Formats the enriched metric for storage:

- Structures data according to datastore schema
- Applies compression if needed
- Adds metadata for indexing and querying
- Prepares for time-series storage

## Configuration

The `MetricEnricher`'s behavior can be configured through various settings:

### System Configuration

| Parameter | Description | Default Value |
|-----------|-------------|---------------|
| `RATE_CALCULATION_ENABLED` | Whether rate calculation is enabled | true |
| `CONTEXT_ADDITION_ENABLED` | Whether context addition is enabled | true |
| `THRESHOLD_COMPARISON_ENABLED` | Whether threshold comparison is enabled | true |
| `STATUS_DETERMINATION_ENABLED` | Whether status determination is enabled | true |
| `RATE_CACHE_SIZE` | Maximum number of entries in the rate cache | 100000 |
| `RATE_CACHE_EXPIRATION_MS` | Expiration time for rate cache entries (ms) | 3600000 |

### Metric-Specific Configuration

| Parameter | Description | Default Value |
|-----------|-------------|---------------|
| `rateCalculation` | Whether rate calculation is enabled for this metric | true |
| `counterReset` | How to handle counter resets | "IGNORE" |
| `unit` | Unit of measurement | "" |
| `conversionFactor` | Factor for unit conversion | 1.0 |
| `thresholds` | Threshold configuration | {} |
| `statusMapping` | Mapping of values to status | {} |

## Event Bus Messages

### Input Events

| Event Type | Description | Parameters |
|------------|-------------|------------|
| `EVENT_METRIC_ENRICH` | Request to enrich a metric | `metricId`: Long, `objectId`: Long, `metricType`: String, `data`: JsonObject, `timestamp`: Long |
| `EVENT_METRIC_BATCH_ENRICH` | Request to enrich a batch of metrics | `metrics`: JsonArray |
| `EVENT_RATE_CACHE_CLEAR` | Request to clear the rate cache | `metricId`: Long (optional) |

### Output Events

| Event Type | Description | Parameters |
|------------|-------------|------------|
| `EVENT_METRIC_ENRICHED` | Notification that a metric has been enriched | `metricId`: Long, `objectId`: Long, `metricType`: String, `data`: JsonObject |
| `EVENT_METRIC_STATUS` | Status information derived from a metric | `objectId`: Long, `metricId`: Long, `status`: String, `reason`: String |
| `EVENT_DATASTORE_WRITE` | Request to write metric data to the datastore | `metric`: JsonObject |

## Error Handling

The `MetricEnricher` implements several error handling mechanisms:

1. **Data Validation Errors**:
   - Logs warnings for invalid or missing data
   - Skips processing for invalid metrics
   - Provides detailed validation error messages
   - Continues processing other metrics in a batch

2. **Rate Calculation Errors**:
   - Handles missing previous values
   - Detects and handles counter resets
   - Implements bounds checking for calculated rates
   - Falls back to raw values when rate calculation fails

3. **Context Addition Errors**:
   - Handles missing object or metric configuration
   - Provides default context when sources are unavailable
   - Logs warnings for context retrieval failures
   - Continues with partial context when full context is unavailable

4. **Threshold Comparison Errors**:
   - Handles missing or invalid thresholds
   - Implements default threshold behavior
   - Logs warnings for threshold configuration issues
   - Continues with default status when threshold comparison fails

5. **Formatting Errors**:
   - Handles serialization failures
   - Implements fallback formatting
   - Logs errors for formatting issues
   - Skips storage for unformattable metrics

## Performance Considerations

The `MetricEnricher` is designed to be efficient and scalable:

1. **Caching**:
   - Implements efficient caching for rate calculation
   - Caches frequently accessed configuration
   - Uses time-based cache expiration
   - Implements size-limited caches to prevent memory issues

2. **Batch Processing**:
   - Processes metrics in batches
   - Optimizes context retrieval for batches
   - Implements bulk operations for datastore writes
   - Balances batch size for optimal throughput

3. **Memory Management**:
   - Uses efficient data structures
   - Implements proper cleanup of temporary objects
   - Minimizes object creation during processing
   - Uses primitive collections where appropriate

4. **Concurrency**:
   - Implements thread-safe operations
   - Uses concurrent collections for shared data
   - Avoids blocking operations
   - Implements proper synchronization

## Monitoring and Metrics

The `MetricEnricher` exposes several metrics for monitoring its performance:

| Metric | Description | Type |
|--------|-------------|------|
| `enricher.metrics.processed` | Number of metrics processed | Counter |
| `enricher.metrics.errors` | Number of processing errors | Counter |
| `enricher.rate.calculations` | Number of rate calculations performed | Counter |
| `enricher.threshold.comparisons` | Number of threshold comparisons performed | Counter |
| `enricher.processing.time` | Time taken to process metrics | Histogram |
| `enricher.rate.cache.size` | Size of the rate cache | Gauge |
| `enricher.rate.cache.hits` | Number of rate cache hits | Counter |
| `enricher.rate.cache.misses` | Number of rate cache misses | Counter |

## Best Practices

For optimal use of the `MetricEnricher`:

1. **Metric Configuration**:
   - Configure appropriate rate calculation settings
   - Define clear threshold values
   - Specify correct units and conversion factors
   - Document metric types and expected values

2. **Performance Tuning**:
   - Adjust cache sizes based on system resources
   - Optimize batch sizes for different metric types
   - Monitor processing times and adjust accordingly
   - Balance enrichment depth with performance requirements

3. **Error Handling**:
   - Implement proper validation in metric collectors
   - Handle counter resets appropriately
   - Define fallback behaviors for error conditions
   - Monitor error rates and address recurring issues

4. **Context Management**:
   - Define relevant context for different metric types
   - Avoid excessive context that isn't used
   - Optimize context retrieval for frequently used sources
   - Cache context information where appropriate

5. **Status Determination**:
   - Define clear status mapping rules
   - Implement appropriate thresholds for different metrics
   - Consider time-based variations in normal values
   - Document status determination logic

## Code Examples

### Processing a Scalar Metric

```java
private void processScalarMetric(JsonObject metric) {
    Long metricId = metric.getLong("metricId");
    Long objectId = metric.getLong("objectId");
    String metricType = metric.getString("metricType");
    Double value = metric.getDouble("value");
    String unit = metric.getString("unit", "");
    Long timestamp = metric.getLong("timestamp", System.currentTimeMillis());
    
    // Get metric configuration
    JsonObject config = metricConfigStore.getMetricConfig(metricId);
    if (config == null) {
        logger.warn("Metric configuration not found for metricId: " + metricId);
        return;
    }
    
    // Apply unit conversion if needed
    if (config.containsKey("conversionFactor")) {
        double factor = config.getDouble("conversionFactor");
        value = value * factor;
    }
    
    // Calculate rate if needed
    if (config.getBoolean("rateCalculation", false)) {
        value = calculateRate(metricId, value, timestamp);
    }
    
    // Compare with thresholds
    String status = determineStatus(value, config.getJsonObject("thresholds", new JsonObject()));
    
    // Add context
    JsonObject enriched = new JsonObject()
        .put("metricId", metricId)
        .put("objectId", objectId)
        .put("metricType", metricType)
        .put("value", value)
        .put("unit", unit)
        .put("timestamp", timestamp)
        .put("status", status);
    
    addContext(enriched, objectId, metricId);
    
    // Format for storage
    JsonObject formatted = formatMetric(enriched);
    
    // Store metric
    vertx.eventBus().send(NMSConstants.EVENT_DATASTORE_WRITE, formatted);
    
    // Update object status if needed
    if (status != null) {
        vertx.eventBus().send(NMSConstants.EVENT_METRIC_STATUS,
            new JsonObject()
                .put("objectId", objectId)
                .put("metricId", metricId)
                .put("status", status)
                .put("reason", "Metric " + metricType + " is " + status));
    }
}
```

### Calculating Rate

```java
private double calculateRate(Long metricId, double currentValue, long currentTimestamp) {
    String cacheKey = metricId.toString();
    JsonObject previous = rateCache.get(cacheKey);
    
    if (previous == null) {
        // First value, can't calculate rate yet
        rateCache.put(cacheKey, new JsonObject()
            .put("value", currentValue)
            .put("timestamp", currentTimestamp));
        return 0.0;
    }
    
    double previousValue = previous.getDouble("value");
    long previousTimestamp = previous.getLong("timestamp");
    
    // Update cache
    rateCache.put(cacheKey, new JsonObject()
        .put("value", currentValue)
        .put("timestamp", currentTimestamp));
    
    // Calculate time difference in seconds
    double timeDiff = (currentTimestamp - previousTimestamp) / 1000.0;
    if (timeDiff <= 0) {
        return 0.0;
    }
    
    // Handle counter reset
    if (currentValue < previousValue) {
        // Assume 32-bit counter reset
        return (currentValue + 4294967296.0 - previousValue) / timeDiff;
    } else {
        return (currentValue - previousValue) / timeDiff;
    }
}
```

### Processing Instance Metrics

```java
private void processInstanceMetric(JsonObject metric) {
    Long metricId = metric.getLong("metricId");
    Long objectId = metric.getLong("objectId");
    String metricType = metric.getString("metricType");
    JsonArray instances = metric.getJsonArray("instances", new JsonArray());
    Long timestamp = metric.getLong("timestamp", System.currentTimeMillis());
    
    // Get metric configuration
    JsonObject config = metricConfigStore.getMetricConfig(metricId);
    if (config == null) {
        logger.warn("Metric configuration not found for metricId: " + metricId);
        return;
    }
    
    // Process each instance
    for (int i = 0; i < instances.size(); i++) {
        JsonObject instance = instances.getJsonObject(i);
        String instanceId = instance.getString("instanceId");
        JsonObject values = instance.getJsonObject("values", new JsonObject());
        
        // Calculate rates for counter values
        if (config.getBoolean("rateCalculation", false)) {
            JsonObject rates = new JsonObject();
            for (String field : values.fieldNames()) {
                if (config.getJsonArray("counterFields", new JsonArray()).contains(field)) {
                    String rateKey = metricId + "." + instanceId + "." + field;
                    double value = values.getDouble(field);
                    double rate = calculateRate(rateKey, value, timestamp);
                    rates.put(field + "Rate", rate);
                }
            }
            values.mergeIn(rates);
        }
        
        // Determine instance status
        String status = determineInstanceStatus(values, config);
        if (status != null) {
            values.put("status", status);
        }
    }
    
    // Add context
    JsonObject enriched = new JsonObject()
        .put("metricId", metricId)
        .put("objectId", objectId)
        .put("metricType", metricType)
        .put("instances", instances)
        .put("timestamp", timestamp);
    
    addContext(enriched, objectId, metricId);
    
    // Format for storage
    JsonObject formatted = formatMetric(enriched);
    
    // Store metric
    vertx.eventBus().send(NMSConstants.EVENT_DATASTORE_WRITE, formatted);
    
    // Determine overall status from instances
    String overallStatus = determineOverallStatus(instances, config);
    if (overallStatus != null) {
        vertx.eventBus().send(NMSConstants.EVENT_METRIC_STATUS,
            new JsonObject()
                .put("objectId", objectId)
                .put("metricId", metricId)
                .put("status", overallStatus)
                .put("reason", "Metric " + metricType + " is " + overallStatus));
    }
}
```

### Adding Context

```java
private void addContext(JsonObject metric, Long objectId, Long metricId) {
    // Get object information
    JsonObject object = objectConfigStore.getObject(objectId);
    if (object != null) {
        metric.put("objectName", object.getString("name"))
            .put("objectType", object.getString("type"))
            .put("objectCategory", object.getString("category"))
            .put("ip", object.getString("ip"));
        
        // Add tags
        JsonArray tags = object.getJsonArray("tags", new JsonArray());
        if (!tags.isEmpty()) {
            metric.put("tags", tags);
        }
        
        // Add groups
        JsonArray groups = object.getJsonArray("groups", new JsonArray());
        if (!groups.isEmpty()) {
            metric.put("groups", groups);
        }
    }
    
    // Get metric configuration
    JsonObject config = metricConfigStore.getMetricConfig(metricId);
    if (config != null) {
        metric.put("metricName", config.getString("name"))
            .put("plugin", config.getString("plugin"));
        
        // Add thresholds
        JsonObject thresholds = config.getJsonObject("thresholds", new JsonObject());
        if (!thresholds.isEmpty()) {
            metric.put("thresholds", thresholds);
        }
    }
    
    // Add geographic information if available
    if (object != null && object.containsKey("location")) {
        metric.put("location", object.getJsonObject("location"));
    }
    
    // Add business context if available
    if (object != null && object.containsKey("business")) {
        metric.put("business", object.getJsonObject("business"));
    }
}
```

## Related Components

- **MetricScheduler**: Schedules metric collection
- **MetricPoller**: Executes the actual polling operations
- **ResponseProcessor**: Processes polling results before enrichment
- **ObjectStatusCalculator**: Updates object status based on metric status
- **DatastoreManager**: Stores enriched metrics
- **VisualizationEngine**: Displays metric data in dashboards and reports

## Future Enhancements

Planned improvements for the `MetricEnricher`:

1. **Advanced Rate Calculation**:
   - Support for different counter types (32-bit, 64-bit, etc.)
   - Improved counter reset detection
   - Adaptive rate calculation based on polling intervals
   - Support for non-linear rates

2. **Enhanced Threshold Management**:
   - Dynamic thresholds based on historical data
   - Machine learning-based anomaly detection
   - Time-of-day and day-of-week thresholds
   - Seasonal threshold adjustments

3. **Context Enrichment**:
   - Integration with external context sources
   - Real-time business impact assessment
   - User experience correlation
   - Service dependency context

4. **Performance Optimizations**:
   - Improved batch processing
   - Enhanced caching strategies
   - Reduced memory footprint
   - Parallel processing of independent metrics

5. **Advanced Analytics**:
   - Trend detection and forecasting
   - Pattern recognition
   - Correlation analysis
   - Root cause identification
