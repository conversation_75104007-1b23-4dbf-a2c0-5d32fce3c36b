<!doctype html>
<html>

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width;initial-scale=1;maximum-scale=1;user-scalable=no;">
    <meta name="x-apple-disable-message-reformatting">
    <title>Major</title>
    <style type="text/css">
        @media screen and (max-width:599px){.mobile-block{display:block}.mobile-100p{width:100%;height:auto}.mobile-hide{display:none}.mobile-space{padding-top:8px!important;padding-bottom:8px!important}.space16{height:16px}.side-spacing{padding-left:16px!important;padding-right:16px!important}.message-width{width:65%}}.major{color:#f58518}.critical{color:#f04e3e}.clear{color:#89c540}.down{color:#ed7c70}.warning{color:#f5bc18}.major.border{border-color:#f58518!important}.critical.border{border-color:#f04e3e!important}.clear.border{border-color:#89c540!important}.down.border{border-color:#ed7c70!important}.warning.border{border-color:#f5bc18!important}
    </style>
</head>

<body bgcolor="#F3F6F8" leftmargin="0" topmargin="0" marginwidth="0" marginheight="0">

    <#-- always declare your functions here -->

    <#function bytesPerSecToSize bps>
    <#-- Convert bits per second to kilobytes per second -->
        <#return (bps / 8) / 1000 + " KBps">
    </#function>

    <#function bitsPerSecToSize bps>
    <#-- Convert bits per second to kilobits per second -->
        <#return bps / 1000 + " kbps">
    </#function>

    <#function bitsToSize bits decimals=2>
    <#--    <#assign decimalFormat = "0." + ("0" * decimals)>-->
        <#assign decimalFormat = "0.00" >

    <#-- Handle null, undefined, or zero bits -->
        <#if bits == 0>
            <#return "0 Bits">
        <#elseif bits < 8>
            <#return bits?string("0.00") + " Bits">
        <#elseif bits < 8 * 1000>
            <#return (bits / 8)?string(decimalFormat) + " Bytes">
        <#elseif bits < 8 * 1000 * 1000>
            <#return (bits / (8 * 1000))?string(decimalFormat) + " KB">
        <#elseif bits < 8 * 1000 * 1000 * 1000>
            <#return (bits / (8 * 1000 * 1000))?string(decimalFormat) + " MB">
        <#else>
            <#return (bits / (8 * 1000 * 1000 * 1000))?string(decimalFormat) + " GB">
        </#if>
    </#function>

    <#function bytesToSize bytes decimals=2>
        <#assign decimalFormat = "0.00" >

    <#-- Handle null, undefined, or zero bytes -->
        <#if bytes == 0>
            <#return "0 bytes">
        <#elseif bytes < 1>
            <#return bytes?string("0.00") + " bytes">
        <#elseif bytes < 1000>
            <#return bytes?string(decimalFormat) + " bytes">
        <#elseif bytes < 1000 * 1000>
            <#return (bytes / 1000)?string(decimalFormat) + " KB">
        <#elseif bytes < 1000 * 1000 * 1000>
            <#return (bytes / (1000 * 1000))?string(decimalFormat) + " MB">
        <#else>
            <#return (bytes / (1000 * 1000 * 1000))?string(decimalFormat) + " GB">
        </#if>
    </#function>

    <#function calculatePercentage value policyThreshold>
        <#local diff = ((value - policyThreshold) * 100) / policyThreshold>
        <#return diff?abs?string("0.00")>
    </#function>

    <#function setPolicyMessage context>

        <#assign severity = context["severity"]>
        <#assign message = context["metric"] + " has entered into " + context["severity"] + " state, ">
        <#assign policySeverity = context["policy.severity"]>
        <#assign policyType = context["policy.type"]?lower_case>

        <#assign forecastOrBaseline = policyType?contains("forecast") ?string("forecasted value","auto-detected baseline")>

        <#if policyType?contains("forecast") || policyType?contains("baseline")>

            <#if severity == "clear" || severity == "CLEAR">
                <#assign conditionMessage = " falls below the " + forecastOrBaseline + " " + (unitConvertedValues(context["metric"], context["policy.threshold"]))>
            <#else>
                <#assign conditionMessage =  policyType?contains("baseline") ?string(" is ","") + policySeverity[severity]["policy.condition"] + " the " + forecastOrBaseline + " " + (unitConvertedValues(context["metric"], context["policy.threshold"]))>
            </#if>

            <#assign percentageChange>
                    <#if context["threshold.type"] == "relative">
                    by ${calculatePercentage(context["value"], context["policy.threshold"])}%
                    <#else>
                    by ${unitConvertedValues(context["metric"], (context["value"] - context["policy.threshold"])?abs)}
                    </#if>
            </#assign>

            <#return message + "as current metric value " + unitConvertedValues(context["metric"], context["value"]) + conditionMessage + percentageChange>
        <#else>
            <#if severity =="clear" || severity == "CLEAR">
                <#assign conditionMessage = "within the range of predicted value.">

                <#else>
                    <#assign conditionMessage = policySeverity[severity]["policy.condition"] + " predicted value">
            </#if>

            <#return message + " as " + context["value"] + "% of samples where " + conditionMessage>
        </#if>
    </#function>

    <#function unitConvertedValues counter value>

        <#assign statusMap = {"1": "Up","2": "Down","3": "Suspend","4": "Unknown","5": "Maintenance","6": "Disable","7": "Unreachable"} />

        <#if value?length == 0>
            <#if counter?matches(".*\\.(ms|time|duration|sec|seconds)\\..*") && !counter?matches("per\\.(?:ms|time|duration|sec|seconds)")>
                <#return 0>
            <#else>
                <#return value>
            </#if>
        </#if>

    <#-- Check if 'counter' matches the regular expression for '.status' with optional sub-patterns like avg, sum, etc. -->
        <#if counter?matches(".*status$")>
        <#-- Return the mapped 'value' from 'statusMap', or return the original 'value' if no mapping exists -->
            <#return statusMap[value?c]?default(value)>
        </#if>


    <#-- Check if 'counter' matches the regular expression pattern for percent (optional sub-patterns like avg, sum, etc.) -->
        <#if counter?matches(".*percent$")>

            <#if value?matches("^-?\\d+\\.\\d+")>
                <#return value?string("0.00") + "%">
            </#if>

            <#return (value + "%")>
        </#if>

    <#-- Check if 'counter' matches the regular expression pattern for dbm (optional sub-patterns like avg, sum, etc.) -->
        <#if counter?matches(".*dbm$")>
        <#-- Return 'value' or 0 if 'value' is null/undefined, and append "dBm" -->
            <#return value + " dBm">
        </#if>

    <#-- Check if 'counter' matches the regular expression pattern for '.days' (with an optional '.last' sub-pattern) -->
        <#if counter?matches(".*days$")>
        <#-- Return 'value' followed by ' days' -->
            <#return value?string + " days">
        </#if>

    <#-- Check if 'counter' matches the regular expression pattern for '.hz' with optional sub-patterns -->
        <#if counter?matches(".*hz$")>
        <#-- Call the 'hz' function with 'value' -->
            <#return value?string + " Hz">
        </#if>

        <#if counter?matches(".*\\.bytes\\.per\\.sec$")>
            <#return bytesPerSecToSize(value)>
        </#if>

        <#if counter?matches(".*\\.bits\\.per\\.sec$")>
            <#return bitsPerSecToSize(value)>
        </#if>

        <#if counter?matches(".*bytes$")>
            <#return bytesToSize(value)>
        </#if>

        <#if counter?matches(".*bits$")>
            <#return bitsToSize(value)>
        </#if>

            <#if counter?matches(".*(sec|seconds)$")>
            <#return value + " seconds">
        </#if>

        <#if counter?matches(".*ms$")>
            <#return value + " milliseconds">
        </#if>

        <#return value>

    </#function>

    <table width="100%" bgcolor="#F3F6F8" border="0" cellpadding="0" cellspacing="0" align="center" style="border-collapse:collapse;font-family:Arial,Helvetica,sans-serif;background:#f3f6f8">
        <tr style="margin:0;padding:0">
            <td style="margin:0;padding:0" height="40"></td>
        </tr>
        <tr style="margin:0;padding:0">
            <td style="margin:0;padding:0" class="mobile-100p" width="650" valign="top" align="center">
                <#assign lvalue = "cid:$\{logo}">
                <a href="#" target="_blank"><img width="85" height="25" style="width:85px;height:25px" src="${lvalue}" alt="motadata"></a>
            </td>
        </tr>
        <tr style="margin:0;padding:0">
            <td style="margin:0;padding:0" height="24" class="space16"></td>
        </tr>
        <tr style="margin:0;padding:0">
            <td style="margin:0;padding:0" valign="top" align="center">
                <table class="${severity} border mobile-100p side-spacing" border="0" width="600" border="0" cellspacing="0" cellpadding="0" bgcolor="#ffffff" style="padding:0 24px;border-top:4px solid;background:#fff">
                    <tbody>
                        <tr style="margin:0;padding:0">
                            <td style="margin:0;padding:0" height="24" class="space16"></td>
                        </tr>
                        <tr style="margin:0;padding:0">
                            <td style="margin:0;padding:0;text-align:center"><img width="36" height="36" style="width:36px;height:36px" src="cid:${severity}.png" alt="clear"></td>
                        </tr>
                        <tr style="margin:0;padding:0">
                            <td style="margin:0;padding:0" height="10"></td>
                        </tr>
                        <tr style="margin:0;padding:0">
                            <td style="margin:0;padding:0;text-align:center">
                                <h1 style="font-size:16px;color:#364658;font-family:Arial,Helvetica,sans-serif;margin:0;padding:0 0 5px">${.data_model["object.name"]} - ${.data_model["metric"]}</h1>
                                <p style="color:#9ba3ab;font-size:14px;text-align:center;margin:0;padding:3px 0 0 0;font-weight:700">${.data_model["timestamp"]}</p>
                            </td>
                        </tr>
                        <tr style="margin:0;padding:0">
                            <td style="margin:0;padding:0" height="32" class="space16"></td>
                        </tr>
                        <tr style="margin:0;padding:0">
                            <td style="margin:0;padding:0">
                                <hr style="border:1px solid #eef2f6;margin:0;padding:0">
                            </td>
                        </tr>
                        <tr style="margin:0;padding:0">
                            <td style="margin:0;padding:0" height="16"></td>
                        </tr>
                        <tr style="margin:0;padding:0">
                            <td style="margin:0;padding:0">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" style="border-collapse:collapse;margin:0;padding:0">
                                    <tbody>
                                        <tr style="margin:0;padding:0" valign="top">
                                            <td class="mobile-block mobile-100p mobile-space" width="33.333%" style="margin:0;padding:0 5px">
                                                <p style="color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px">Object Name</p>
                                                <p style="color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0">${.data_model["object.name"]}</p>
                                            </td>
                                            <td class="mobile-block mobile-100p mobile-space" width="33.333%" style="margin:0;padding:0 5px">
                                                <p style="color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px">IP / Host</p>
                                                <p style="color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0">${.data_model["object.target"]}</p>
                                            </td>
                                            <td class="mobile-block mobile-100p mobile-space" width="33.333%" style="margin:0;padding:0 5px">
                                                <p style="color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px">Object Type</p>
                                                <p style="color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0">${.data_model["object.type"]}</p>
                                            </td>
                                        </tr>
                                        <tr class="mobile-hide" style="margin:0;padding:0">
                                            <td colspan="3" style="margin:0;padding:0" height="16"></td>
                                        </tr>
                                        <tr style="margin:0;padding:0" valign="top">
                                            <td class="mobile-block mobile-100p mobile-space" width="33.333%" style="margin:0;padding:0 5px">
                                                <p style="color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px">Metric</p>
                                                <p style="color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0">${.data_model["metric"]}</p>
                                            </td>
                                            <td class="mobile-block mobile-100p mobile-space" width="33.333%" style="margin:0;padding:0 5px">
                                                <p style="color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px">Metric Value</p>
                                                <p style="color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0">${.data_model["value"]}</p>
                                            </td>
                                            <td class="mobile-block mobile-100p mobile-space" width="33.333%" style="margin:0;padding:0 5px">
                                                <p style="color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px">Severity</p>
                                                <p class="${severity}" style="font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0">${.data_model["severity"]}</p>
                                            </td>
                                        </tr>
                                        <tr class="mobile-hide" style="margin:0;padding:0">
                                            <td colspan="3" style="margin:0;padding:0" height="16"></td>
                                        </tr>
                                        <tr style="margin:0;padding:0" valign="top">
                                            <td class="mobile-block mobile-100p mobile-space" width="33.333%" style="margin:0;padding:0 5px">
                                                <p style="color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px">Policy Name</p>
                                                <p style="color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0">${.data_model["policy.name"]}</p>
                                            </td>
                                            <td class="mobile-block mobile-100p mobile-space" width="66.666%" colspan="2" style="margin:0;padding:0 5px">
                                                <p style="color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px">Policy Type</p>
                                                <p style="color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0">${.data_model["policy.type"]}</p>
                                            </td>
                                        </tr>
                                        <tr class="mobile-hide" style="margin:0;padding:0">
                                            <td colspan="3" style="margin:0;padding:0" height="16"></td>
                                        </tr>
                                        <tr style="margin:0;padding:0">
                                            <td colspan="3" style="margin:0;padding:0">
                                                <hr style="border:1px solid #eef2f6;margin:0;padding:0">
                                            </td>
                                        </tr>
                                        <tr style="margin:0;padding:0">
                                            <td colspan="3" style="margin:0;padding:0" height="16"></td>
                                        </tr>
                                        <tr style="margin:0;padding:0">
                                            <td colspan="3" style="margin:0;padding:0 5px">
                                                <p style="color:#7b8fa5;font-size:12px;line-height:14px;margin:0;padding:0 0 5px">Message</p>
                                                <p style="color:#364658;font-size:12px;font-weight:700;line-height:14px;margin:0;padding:0">${setPolicyMessage(.data_model)}</p>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr style="margin:0;padding:0">
                            <td style="margin:0;padding:0">
                                <table width="100%" border="0" cellspacing="0" cellpadding="0" style="border-collapse:collapse;margin:0;padding:0">
                                    <tbody>
                                        <tr style="margin:0;padding:0">
                                            <td colspan="3" style="margin:0;padding:0" height="24"></td>
                                        </tr>
                                        <tr style="margin:0;padding:0">
                                            <td colspan="3" style="margin:0;padding:0" height="16"></td>
                                        </tr>
                                        <tr style="margin:0;padding:0">
                                            <td colspan="3" style="margin:0;padding:0">
                                                <p style="color:#364658;font-size:10px;line-height:14px;text-align:center;margin:0;padding:0 0 5px"><strong>Disclaimer:</strong>This is an automated message generated by Motadata.Please do not reply to this email.
                                                    <br>For any queries, reach out to your System Admin</p>
                                            </td>
                                        </tr>
                                        <tr style="margin:0;padding:0">
                                            <td colspan="3" style="margin:0;padding:0" height="24"></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr style="margin:0;padding:0">
            <td style="margin:0;padding:0" height="24"></td>
        </tr>
        <tr align="center" ; style="margin:0;padding:0;text-align:center">
            <td style="margin:0;padding:0">
                <p style="font-size:10px;margin:0;padding:0;color:#7b8fa5">For more details, <a style="color:#009ddc;text-decoration:none" href="${.data_model["policy.url"]}" target="_blank">Click here</a>.</p>
            </td>
        </tr>
        <tr style="margin:0;padding:0">
            <td style="margin:0;padding:0" height="16"></td>
        </tr>
        <tr align="center" style="margin:0;padding:0">
            <td style="margin:0;padding:0">
                <table width="102" align="center" border="0" cellspacing="0" cellpadding="0" style="border-collapse:collapse;margin:0;padding:0">
                    <tbody>
                        <tr>
                            <td>
                                <a href="https://www.facebook.com/motadata" target="_blank"><img style="vertical-align:middle" src="cid:fb.png" alt="Facebook"></a>
                            </td>
                            <td>
                                <a href="https://twitter.com/MotadataSystems" target="_blank"><img style="vertical-align:middle" src="cid:twitter.png" alt="Twitter"></a>
                            </td>
                            <td>
                                <a href="https://www.linkedin.com/company/motadata/" target="_blank"><img style="vertical-align:middle" src="cid:linkedin.png" alt="Linkedin"></a>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr style="margin:0;padding:0">
            <td style="margin:0;padding:0" height="32"></td>
        </tr>
    </table>
</body>

</html>