/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.streaming;

import com.mindarray.*;
import com.mindarray.api.*;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.nms.NMSConstants;
import com.mindarray.notification.Notification;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.store.*;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.CronExpressionUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.*;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.api.Discovery.*;
import static com.mindarray.api.MailServerConfiguration.*;
import static com.mindarray.api.SMSGatewayConfiguration.SMS_SERVER_GATEWAY_URL;
import static com.mindarray.api.Scheduler.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.nms.NMSConstants.*;
import static com.mindarray.notification.Notification.*;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;


@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(70 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|SLAVE$")
public class TestNotification
{
    private static final JsonObject LINUX_DISCOVERY_PARAMETERS = TestConstants.prepareParams("linux.discovery.parameters");

    private static final JsonObject LINUX_CREDENTIAL_PROFILE = TestConstants.prepareParams("testRootLinuxDiscovery");

    private static final JsonObject NETWORK_CREDENTIAL_CONTEXT = TestConstants.prepareParams("testSNMPv1Discovery");

    private static final JsonObject NETWORK_DISCOVERY_PARAMETERS = TestConstants.prepareParams("snmp.discovery.v1.parameters");

    private static final String PATTERN = "dd-MM-yyyy";

    private static final JsonObject EMAIL_CONFIG = new JsonObject()
            .put(MAIL_SERVER_HOST, "smtp.office365.com")
            .put(MAIL_SERVER_PORT, 587)
            .put(MAIL_SERVER_PROTOCOL, MAIL_SERVER_PROTOCOL_TLS)
            .put(MAIL_SERVER_CREDENTIAL_PROFILE, CredentialProfile.DEFAULT_EMAIL_CREDENTIAL_PROFILE)
            .put(MAIL_SERVER_AUTH_STATUS, YES)
            .put(MAIL_SERVER_SENDER, "<EMAIL>")
            .put(MAIL_SERVER_USERNAME, "<EMAIL>")
            .put(MAIL_SERVER_PASSWORD, "Mind#@45Date!12");

    private static final JsonObject SMS_CONTEXT = new JsonObject().put(SMS_SERVER_GATEWAY_URL, "http://api.textlocal.in/send/?username=<EMAIL>&hash=73bcd13d06857dab63b42bee0d0437011577d31161f3f367cb7c89c48f101698&sender=TXTLCL&numbers=$$number$$&message=$$message$$")
            .put(TARGET, "7990311324")
            .put(MESSAGE, "Test message from motadata");
    private static final JsonObject DISCOVERY_IDS = new JsonObject();
    private static final Logger LOGGER = new Logger(TestNotification.class, "Test Notification", "Test Notification");
    private static MessageConsumer<JsonObject> messageConsumer;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        var promise = Promise.<Void>promise();

        TestAPIUtil.get(TestAPIConstants.EMAIL_CONFIG_API_ENDPOINT, testContext.succeeding(asyncResult ->
                testContext.verify(() ->
                {
                    var id = asyncResult.bodyAsJsonObject().getJsonArray(RESULT).getJsonObject(0).getLong(ID);

                    var context = new JsonObject().mergeIn(EMAIL_CONFIG).put(MAIL_SERVER_USERNAME, EMAIL_CONFIG.getString(MAIL_SERVER_USERNAME))
                            .put(MAIL_SERVER_PASSWORD, EMAIL_CONFIG.getString(MAIL_SERVER_PASSWORD));

                    TestAPIUtil.put(TestAPIConstants.EMAIL_CONFIG_API_ENDPOINT + "/" + id, context, testContext.succeeding(response ->
                            testContext.verify(() ->
                            {
                                try
                                {
                                    TimeUnit.SECONDS.sleep(1);
                                }
                                catch (Exception ignored)
                                {

                                }

                                promise.complete();
                            })));
                })));

        promise.future().onComplete(asyncResult ->
                TestAPIUtil.get(SMS_GATEWAY_CONFIGURATION_API_ENDPOINT + GlobalConstants.DEFAULT_ID, testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            var id = response.bodyAsJsonObject().getJsonObject(RESULT).getLong(ID);

                            TestAPIUtil.put(TestAPIConstants.SMS_GATEWAY_CONFIGURATION_API_ENDPOINT + "/" + id, SMS_CONTEXT, testContext.succeeding(result ->
                                    testContext.verify(() ->
                                    {
                                        try
                                        {
                                            TimeUnit.SECONDS.sleep(1);
                                        }
                                        catch (Exception ignored)
                                        {

                                        }

                                        testContext.completeNow();
                                    })));
                        }))));
    }

    private static void assertSMSServerTestResult(VertxTestContext testContext, TestInfo testInfo)
    {
        messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            try
            {
                var eventContext = new JsonObject();

                if (message.body().containsKey(EVENT_COMPRESSION_TYPE) && message.body().getInteger(EVENT_COMPRESSION_TYPE) == COMPRESSION_TYPE_ONE)
                {
                    eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));
                }
                else
                {
                    eventContext = message.body().getJsonObject(EventBusConstants.EVENT_CONTEXT);
                }

                if (eventContext.getString(EVENT_TYPE) != null && eventContext.getString(EVENT_TYPE).contains(NotificationType.SMS.getName()) && eventContext.getString(STATUS) != null)
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": eventContext: " + eventContext.encode());

                    assertEquals(STATUS_SUCCEED, eventContext.getString(STATUS));

                    var smsMessage = eventContext.getJsonObject(EVENT_CONTEXT).getString(SMS_NOTIFICATION_MESSAGE);

                    if (smsMessage.contains("Total Objects") && smsMessage.contains("Discovered Object") && smsMessage.contains("Failed Object") && smsMessage.contains("Discovery Progress"))
                    {
                        messageConsumer.unregister(result -> testContext.completeNow());
                    }
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();
    }

    @AfterEach
    void afterEach(VertxTestContext testContext)
    {
        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testDiscoveryEmailNotification(VertxTestContext context, TestInfo testInfo)
    {
        var credentialProfileName = "Linux/Unix-Test-" + System.currentTimeMillis();

        assertTestResult(NotificationType.EMAIL.getName(), context, LINUX_CREDENTIAL_PROFILE.put(CredentialProfile.CREDENTIAL_PROFILE_NAME, credentialProfileName),
                LINUX_DISCOVERY_PARAMETERS.copy().put(DISCOVERY_TARGET, "************").put(Discovery.DISCOVERY_NAME, "Linux/Unix-Test" + System.currentTimeMillis()).put(DISCOVERY_EMAIL_RECIPIENTS, new JsonArray().add("<EMAIL>")).put(DISCOVERY_EVENT_PROCESSORS, new JsonArray().add(RemoteEventProcessorConfigStore.getStore().getItemByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId()).getLong(ID))), testInfo).onComplete(result -> DISCOVERY_IDS.put(NMSConstants.Type.LINUX.getName(), result.result()));
    }

    @EnabledIfSystemProperty(named = "env.type", matches = "prod")
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testDiscoverySMSNotification(VertxTestContext context, TestInfo testInfo)
    {
        var credentialProfileName = "Linux/Unix-Test-" + System.currentTimeMillis();

        assertTestResult(NotificationType.SMS.getName(), context, LINUX_CREDENTIAL_PROFILE.put(CredentialProfile.CREDENTIAL_PROFILE_NAME, credentialProfileName),
                LINUX_DISCOVERY_PARAMETERS.copy().put(DISCOVERY_TARGET, "************").put(Discovery.DISCOVERY_NAME, "Linux/Unix-Test" + System.currentTimeMillis()).put(DISCOVERY_SMS_RECIPIENTS, new JsonArray().add("7990311324")).put(DISCOVERY_EVENT_PROCESSORS, new JsonArray().add(RemoteEventProcessorConfigStore.getStore().getItemByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId()).getLong(ID))), testInfo).onComplete(result -> DISCOVERY_IDS.put(NMSConstants.Type.LINUX.getName(), result.result()));
    }

    @EnabledIfSystemProperty(named = "env.type", matches = "prod")
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testDiscoverySchedulerEmailNotification0(VertxTestContext testContext, TestInfo testInfo)
    {
        var scheduler = new JsonObject().put(SCHEDULER_START_DATE, new SimpleDateFormat(PATTERN).format(new Date())).put(SCHEDULER_TIMES, new JsonArray().add("00:05")).put(SCHEDULER_SMS_RECIPIENTS, new JsonArray().add("7990311324")).put(SCHEDULER_TIMELINE, CronExpressionUtil.CRON_ONCE).put(SCHEDULER_JOB_TYPE, JobScheduler.JobType.DISCOVERY.getName()).put(SCHEDULER_CONTEXT, new JsonObject().put(AUTO_PROVISION_STATUS, NO).put(OBJECTS, new JsonArray().add(CommonUtil.getLong(DISCOVERY_IDS.getLong(NMSConstants.Type.LINUX.getName())))));

        TestAPIUtil.post(SCHEDULER_API_ENDPOINT, scheduler,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            LOGGER.info("testDiscoverySchedulerEmailNotification0: response: " + response.bodyAsJsonObject().encode());

                            assertSMSServerTestResult(testContext, testInfo);

                            Bootstrap.vertx().eventBus().send(EventBusConstants.UI_ACTION_SCHEDULER_RUN, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER).put(ID, response.bodyAsJsonObject().getJsonArray(ID).getLong(0)));

                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testDiscoverySchedulerEmailNotification1(VertxTestContext testContext, TestInfo testInfo)
    {

        String discoveryName = "Network-Test" + System.currentTimeMillis();

        var range = "***********-50";

        var credential = NETWORK_CREDENTIAL_CONTEXT.put(CredentialProfile.CREDENTIAL_PROFILE_NAME, "Network-Test-" + System.currentTimeMillis());

        var discoveryParameters = NETWORK_DISCOVERY_PARAMETERS.copy().put(DISCOVERY_TYPE, DISCOVERY_TYPE_IP_ADDRESS_RANGE).put(Discovery.DISCOVERY_TARGET, range).put(Discovery.DISCOVERY_NAME, discoveryName).put(DISCOVERY_EVENT_PROCESSORS, new JsonArray().add(RemoteEventProcessorConfigStore.getStore().getItemByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId()).getLong(ID)));

        TestAPIUtil.createCredentialProfile(credential, testContext).onComplete(result ->
        {
            if (result.succeeded())
            {
                var credentialId = result.result();

                if (credentialId != null && credentialId != 0)
                {
                    discoveryParameters.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(credentialId));
                }

                discoveryParameters.put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

                TestAPIUtil.post(TestAPIConstants.DISCOVERY_API_ENDPOINT, discoveryParameters, testContext.succeeding(discoveryResponse ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + discoveryResponse.bodyAsJsonObject().encode());

                    assertEquals(SC_OK, discoveryResponse.statusCode());

                    DISCOVERY_IDS.put(NMSConstants.Type.SNMP_DEVICE.getName(), discoveryResponse.bodyAsJsonObject().getLong(ID));

                    testContext.completeNow();

                }));
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    @Timeout(value = 180, timeUnit = TimeUnit.SECONDS)
    void testDiscoverySchedulerEmailNotification2(VertxTestContext testContext, TestInfo testInfo)
    {
        var scheduler = new JsonObject().put(SCHEDULER_START_DATE, new SimpleDateFormat(PATTERN).format(new Date())).put(SCHEDULER_TIMES, new JsonArray().add("00:00")).put(SCHEDULER_EMAIL_RECIPIENTS, new JsonArray().add("<EMAIL>")).put(SCHEDULER_TIMELINE, CronExpressionUtil.CRON_ONCE).put(SCHEDULER_JOB_TYPE, JobScheduler.JobType.DISCOVERY.getName()).put(SCHEDULER_CONTEXT, new JsonObject().put(AUTO_PROVISION_STATUS, NO).put(OBJECTS, new JsonArray().add(CommonUtil.getLong(DISCOVERY_IDS.getLong(NMSConstants.Type.SNMP_DEVICE.getName())))));

        TestAPIUtil.post(SCHEDULER_API_ENDPOINT, scheduler,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                                TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), id ->
                                {
                                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

                                    LOGGER.info("SchedulerConfigStore :" + SchedulerConfigStore.getStore().getItem(response.bodyAsJsonObject().getJsonArray(ID).getLong(0)));

                                    SchedulerCacheStore.getStore().updatePendingProbes(response.bodyAsJsonObject().getJsonArray(ID).getLong(0), 0);

                                    messageConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_NOTIFICATION, message ->
                                    {

                                        var notification = message.body();

                                        if (!notification.isEmpty() && notification.getJsonArray(Notification.EMAIL_NOTIFICATION_RECIPIENTS).contains("<EMAIL>"))
                                        {
                                            LOGGER.info(testInfo.getTestMethod().get().getName() + ": eventContext: " + notification.encode());

                                            Assertions.assertEquals(NotificationType.EMAIL.getName(), message.body().getString(NOTIFICATION_TYPE));

                                            var emailMessage = notification.getString(EMAIL_NOTIFICATION_CONTENT);

                                            assertNotNull(emailMessage);

                                            messageConsumer.unregister(result -> testContext.completeNow());
                                        }
                                    });

                                    Bootstrap.vertx().eventBus().send(EventBusConstants.UI_ACTION_SCHEDULER_RUN, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER).put(ID, response.bodyAsJsonObject().getJsonArray(ID).getLong(0)));
                                }))));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
        // bug - 20104
    void testAbortDiscoverySchedulerEmailNotification(VertxTestContext testContext, TestInfo testInfo)
    {
        var scheduler = new JsonObject().put(SCHEDULER_START_DATE, new SimpleDateFormat(PATTERN).format(new Date())).put(SCHEDULER_TIMES, new JsonArray().add("00:05")).put(SCHEDULER_EMAIL_RECIPIENTS, new JsonArray().add("<EMAIL>")).put(SCHEDULER_TIMELINE, CronExpressionUtil.CRON_ONCE).put(SCHEDULER_JOB_TYPE, JobScheduler.JobType.DISCOVERY.getName()).put(SCHEDULER_CONTEXT, new JsonObject().put(AUTO_PROVISION_STATUS, NO).put(OBJECTS, new JsonArray().add(CommonUtil.getLong(DISCOVERY_IDS.getLong(NMSConstants.Type.SNMP_DEVICE.getName())))));

        TestAPIUtil.post(SCHEDULER_API_ENDPOINT, scheduler,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

                            SchedulerCacheStore.getStore().updatePendingProbes(response.bodyAsJsonObject().getJsonArray(ID).getLong(0), 0);

                            messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
                            {
                                try
                                {
                                    var eventContext = new JsonObject();

                                    if (message.body().containsKey(EVENT_COMPRESSION_TYPE) && message.body().getInteger(EVENT_COMPRESSION_TYPE) == COMPRESSION_TYPE_ONE)
                                    {
                                        eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));
                                    }
                                    else
                                    {
                                        eventContext = message.body().getJsonObject(EventBusConstants.EVENT_CONTEXT);
                                    }

                                    if (eventContext.getString(EVENT_TYPE) != null && eventContext.getString(EVENT_TYPE).contains(NotificationType.EMAIL.getName()) && eventContext.getString(STATUS) != null)
                                    {
                                        messageConsumer.unregister();

                                        testContext.failNow(new Exception("for aborted discovery email notification is not allowed.."));
                                    }
                                }
                                catch (Exception exception)
                                {
                                    testContext.failNow(exception);
                                }
                            });

                            Bootstrap.vertx().eventBus().send(EventBusConstants.UI_ACTION_SCHEDULER_RUN, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER).put(ID, response.bodyAsJsonObject().getJsonArray(ID).getLong(0)));

                            testContext.awaitCompletion(1, TimeUnit.SECONDS);

                            Bootstrap.vertx().eventBus().send(EVENT_DISCOVERY_ABORT, CommonUtil.getLong(DISCOVERY_IDS.getLong(NMSConstants.Type.SNMP_DEVICE.getName())));

                            Bootstrap.vertx().setTimer(30000, result ->
                            {
                                if (messageConsumer != null)
                                {
                                    messageConsumer.unregister();

                                    testContext.completeNow();
                                }
                            });
                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testUpdateDiscoveryScheduler(VertxTestContext testContext)
    {

        var range = "172.16.10.42-45";

        var discoveryParameters = new JsonObject().put(Discovery.DISCOVERY_TARGET, range);

        TestAPIUtil.put(TestAPIConstants.DISCOVERY_API_ENDPOINT + "/" + DISCOVERY_IDS.getLong(NMSConstants.Type.SNMP_DEVICE.getName()), discoveryParameters, testContext.succeeding(discoveryResponse ->
        {
            assertEquals(SC_OK, discoveryResponse.statusCode());

            assertEquals(discoveryResponse.bodyAsJsonObject().getString(MESSAGE), String.format(InfoMessageConstants.ENTITY_UPDATED, "Discovery"));

            assertEquals(discoveryResponse.bodyAsJsonObject().getLong(ID), DISCOVERY_IDS.getLong(NMSConstants.Type.SNMP_DEVICE.getName()));

            testContext.completeNow();

        }));
    }

//  Sending dummy events for Syslog Notification, Webhook Notification & Push Notification

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testSyslogNotification(VertxTestContext testContext)
    {

        TestUtil.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.EVENT_REPLY, YES).put(NOTIFICATION_TYPE, NotificationType.SYSLOG.getName())
                .put(MESSAGE, "Syslog test Message From motadata...")
                .put(TARGET, "10.20.40.239")
                .put(PORT, 56667)).onComplete(reply ->
        {

            if (reply.succeeded())
            {
                Assertions.assertEquals(STATUS_SUCCEED, reply.result().body().getString(STATUS));

                testContext.completeNow();
            }
            else
            {
                testContext.failNow(reply.cause());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testInvalidSyslogNotification(VertxTestContext testContext)
    {

        TestUtil.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.EVENT_REPLY, YES).put(NOTIFICATION_TYPE, NotificationType.SYSLOG.getName())
                .put(MESSAGE, "Syslog test Message From motadata...")
                .put(TARGET, "355.355.355.355")
                .put(PORT, 56667)).onComplete(reply ->
        {

            if (reply.succeeded())
            {
                Assertions.assertEquals(STATUS_FAIL, reply.result().body().getString(STATUS));

                testContext.completeNow();
            }
            else
            {
                testContext.failNow(reply.cause());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testWebhookNotification(VertxTestContext testContext)
    {

        TestUtil.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.EVENT_REPLY, YES).put(NOTIFICATION_TYPE, NotificationType.WEBHOOK.getName())
                .put(MESSAGE, "Webhook test Message From motadata...")
                .put(TARGET, "https://www.motadata.com/")).onComplete(reply ->
        {

            if (reply.succeeded())
            {
                Assertions.assertEquals(STATUS_SUCCEED, reply.result().body().getString(STATUS));

                testContext.completeNow();
            }
            else
            {
                testContext.failNow(reply.cause());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testInvalidWebhookNotification(VertxTestContext testContext)
    {

        TestUtil.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.EVENT_REPLY, YES).put(NOTIFICATION_TYPE, NotificationType.WEBHOOK.getName())
                .put(MESSAGE, "Webhook test Message From motadata...")
                .put(TARGET, "https://172.16.10.98/")).onComplete(reply ->
        {

            if (reply.succeeded())
            {
                Assertions.assertEquals(STATUS_FAIL, reply.result().body().getString(STATUS));

                testContext.completeNow();
            }
            else
            {
                testContext.failNow(reply.cause());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testInvalidPushNotification(VertxTestContext testContext)
    {

        TestUtil.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.EVENT_REPLY, YES).put(NOTIFICATION_TYPE, NotificationType.PUSH.getName()));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testTriggerInvalidDiscoveryScheduler(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = NETWORK_DISCOVERY_PARAMETERS.copy().put(DISCOVERY_TARGET, "************").put(DISCOVERY_TYPE, DISCOVERY_TYPE_IP_ADDRESS).put(Discovery.DISCOVERY_NAME, testInfo.getTestMethod().get().getName()).put(DISCOVERY_EVENT_PROCESSORS, new JsonArray().add(RemoteEventProcessorConfigStore.getStore().getItemByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId()).getLong(ID)));

        TestAPIUtil.createCredentialProfile(NETWORK_CREDENTIAL_CONTEXT.put(CredentialProfile.CREDENTIAL_PROFILE_NAME, "Network-Scheduler-Fail-Test" + System.currentTimeMillis()), testContext).onComplete(result ->
        {
            if (result.succeeded())
            {
                var credentialId = result.result();

                if (credentialId != null && credentialId != 0)
                {
                    context.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(credentialId));
                }

                context.getJsonObject(DISCOVERY_CONTEXT).put(PING_CHECK_STATUS, YES);

                context.put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000002")));

                TestAPIUtil.post(TestAPIConstants.DISCOVERY_API_ENDPOINT, context, testContext.succeeding(asyncResult ->
                {
                    LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + asyncResult.bodyAsJsonObject().encode());

                    assertEquals(SC_OK, asyncResult.statusCode());

                    TestAPIUtil.post(SCHEDULER_API_ENDPOINT, new JsonObject().put(SCHEDULER_START_DATE, new SimpleDateFormat(PATTERN).format(new Date())).put(SCHEDULER_TIMES, new JsonArray().add("00:00")).put(SCHEDULER_TIMELINE, CronExpressionUtil.CRON_ONCE).put(SCHEDULER_JOB_TYPE, JobScheduler.JobType.DISCOVERY.getName()).put(SCHEDULER_CONTEXT, new JsonObject().put(AUTO_PROVISION_STATUS, YES).put(OBJECTS, new JsonArray().add(asyncResult.bodyAsJsonObject().getLong(ID)))),
                            testContext.succeeding(response -> testContext.verify(() ->
                            {
                                try
                                {
                                    TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(3), id ->
                                    {
                                        LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + response.bodyAsJsonObject().encode());

                                        LOGGER.info("SchedulerConfigStore :" + SchedulerConfigStore.getStore().getItem(response.bodyAsJsonObject().getJsonArray(ID).getLong(0)));

                                        SchedulerCacheStore.getStore().updatePendingProbes(response.bodyAsJsonObject().getJsonArray(ID).getLong(0), 0);

                                        var retries = new AtomicInteger();

                                        TestUtil.vertx().setPeriodic(TimeUnit.SECONDS.toMillis(3), timer ->
                                        {
                                            if (DiscoveryConfigStore.getStore().getItemByValue(DISCOVERY_NAME, testInfo.getTestMethod().get().getName()).getInteger(DISCOVERY_FAILED_OBJECTS) == 1 && ObjectConfigStore.getStore().getItemByIP("************") == null)
                                            {
                                                TestUtil.vertx().cancelTimer(timer);

                                                testContext.completeNow();
                                            }
                                            else if (retries.get() > 10)
                                            {
                                                TestUtil.vertx().cancelTimer(timer);

                                                testContext.failNow("retries exceeded for " + testInfo.getTestMethod().get().getName());
                                            }
                                            else
                                            {
                                                retries.incrementAndGet();
                                            }
                                        });

                                        Bootstrap.vertx().eventBus().send(EventBusConstants.UI_ACTION_SCHEDULER_RUN, new JsonObject().put(SESSION_ID, TestUtil.getSessionId()).put(USER_NAME, DEFAULT_USER).put(ID, response.bodyAsJsonObject().getJsonArray(ID).getLong(0)));
                                    });
                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);

                                    testContext.failNow(exception.getMessage());
                                }
                            })));

                }));
            }
            else
            {
                LOGGER.error(result.cause());

                testContext.failNow(result.cause());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testTriggerMicrosoftTeamsNotificationInvalid(VertxTestContext testContext)
    {
        TestUtil.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_NOTIFICATION, new JsonObject()
                .put(EventBusConstants.EVENT_REPLY, YES)
                .put(NOTIFICATION_TYPE, NotificationType.MICROSOFT_TEAMS.getName())
                .put(PolicyEngineConstants.CHANNELS, new JsonArray().add(DUMMY_ID))
                .put(Notification.CHANNEL_NOTIFICATION_CONTENT, "Test Motadata Message")).onComplete(reply ->
        {
            if (reply.succeeded())
            {
                Assertions.assertEquals(STATUS_FAIL, reply.result().body().getString(STATUS));

                testContext.completeNow();
            }
            else
            {
                testContext.failNow(reply.cause());
            }
        });

        testContext.completeNow();
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testTriggerMicrosoftTeamsNotification(VertxTestContext testContext)
    {

        var credential = TestConstants.prepareParams("testMicrosoftTeamCredentialProfile");

        Assertions.assertNotNull(credential);

        credential.put(CredentialProfile.CREDENTIAL_PROFILE_NAME, credential.getString(CredentialProfile.CREDENTIAL_PROFILE_NAME) + System.currentTimeMillis());

        TestAPIUtil.createCredentialProfile(credential, testContext).onComplete(result ->
        {
            if (result.succeeded())
            {
                var credentialId = result.result();

                var item = new JsonObject("{\n  \"integration.type\": \"Microsoft Teams\",\n  \"integration.context\": {\n    \"integration.credential.profile\": 64238975866,\n    \"timeout\": 60,\n    \"auto.sync\": \"yes\",\n    \"sync.interval\": 4,\n    \"target\": \"https://graph.microsoft.com/v1.0/\"\n  },\n  \"integration.attributes\": {},\n  \"id\": 10000000000003,\n  \"_type\": \"0\"\n}");

                if (credentialId != null && credentialId != 0)
                {
                    item.getJsonObject(Integration.INTEGRATION_CONTEXT).put(IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, credentialId);
                }

                TestAPIUtil.put(INTEGRATION_API_ENDPOINT + "/" + 10000000000003L, item, response ->
                {
                    if (response.succeeded())
                    {
                        try
                        {
                            var payload = new JsonObject("{\n    \"integration.profile.name\": \"NotificationMicrosoftTeamsIntegrationProfile\",\n    \"integration\": 10000000000003,\n    \"integration.profile.context\": {\n        \"handle.name\": \"/NotificationMicrosoftTeamsIntegrationProfile\",\n        \"team\": \"547fa76e-64dc-4182-92fe-f27c79628a03\",\n        \"channel.type\": \"private\",\n        \"channel\": \"19:49f636af7b564b3786272afca6c11858@thread.tacv2\"\n    }\n}");

                            TestAPIUtil.post(INTEGRATION_PROFILE_API_ENDPOINT, payload, testContext.succeeding(asyncReply -> testContext.verify(() ->
                            {
                                TestUtil.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_NOTIFICATION, new JsonObject()
                                        .put(EventBusConstants.EVENT_REPLY, YES)
                                        .put(NOTIFICATION_TYPE, NotificationType.MICROSOFT_TEAMS.getName())
                                        .put(PolicyEngineConstants.CHANNELS, new JsonArray().add(asyncReply.bodyAsJsonObject().getLong(ID)))
                                        .put(Notification.CHANNEL_NOTIFICATION_CONTENT, "Test Motadata Message")).onComplete(reply ->
                                {
                                    if (reply.succeeded())
                                    {
                                        Assertions.assertEquals(STATUS_FAIL, reply.result().body().getString(STATUS));

                                        testContext.completeNow();
                                    }
                                    else
                                    {
                                        testContext.failNow(reply.cause());
                                    }
                                });

                                testContext.completeNow();
                            })));
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            testContext.failNow(exception);
                        }
                    }
                    else
                    {
                        LOGGER.error(response.cause());

                        testContext.failNow(response.cause());
                    }
                });
            }
            else
            {
                LOGGER.error(result.cause());

                testContext.failNow(result.cause());
            }
        });
    }

    private Future<Long> assertTestResult(String notificationType, VertxTestContext vertxTestContext, JsonObject credential, JsonObject discoveryParameters, TestInfo testInfo)
    {
        var promise = Promise.<Long>promise();

        TestAPIUtil.createCredentialProfile(credential, vertxTestContext).onComplete(result ->
        {
            if (result.succeeded())
            {
                var credentialId = result.result();

                if (credentialId != null && credentialId != 0)
                {
                    discoveryParameters.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(credentialId));
                }

                discoveryParameters.put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

                TestAPIUtil.post(TestAPIConstants.DISCOVERY_API_ENDPOINT, discoveryParameters, vertxTestContext.succeeding(discoveryResponse ->
                        vertxTestContext.verify(() ->
                        {
                            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + discoveryResponse.bodyAsJsonObject().encode());

                            assertEquals(SC_OK, discoveryResponse.statusCode());

                            var discoveryId = discoveryResponse.bodyAsJsonObject().getLong(ID);

                            promise.complete(discoveryId);

                            TestAPIUtil.post(String.format(TestAPIConstants.DISCOVERY_RUN_API_ENDPOINT, discoveryId), new JsonObject(), asyncResult ->
                                    vertxTestContext.verify(() ->
                                    {
                                        LOGGER.info(testInfo.getTestMethod().get().getName() + ": " + asyncResult.result().bodyAsJsonObject());

                                        assertEquals(SC_OK, asyncResult.result().statusCode());

                                        if (notificationType.equals(NotificationType.EMAIL.getName()))
                                        {
                                            assertEmailNotificationTestResult(vertxTestContext);

                                        }
                                        else if (notificationType.equals(NotificationType.SMS.getName()))
                                        {
                                            assertSMSNotificationTestResult(vertxTestContext);

                                        }

                                    }));
                        })));
            }
            else
            {
                vertxTestContext.failNow(result.cause());
            }
        });

        return promise.future();
    }


    private void assertEmailNotificationTestResult(VertxTestContext testContext)
    {
        messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            try
            {
                var eventContext = new JsonObject();

                if (message.body().containsKey(EVENT_COMPRESSION_TYPE) && message.body().getInteger(EVENT_COMPRESSION_TYPE) == COMPRESSION_TYPE_ONE)
                {
                    eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));
                }
                else
                {
                    eventContext = message.body().getJsonObject(EventBusConstants.EVENT_CONTEXT);
                }

                if (eventContext.getString(EVENT_TYPE) != null && eventContext.getString(EVENT_TYPE).contains(NotificationType.EMAIL.getName()) && eventContext.getString(STATUS) != null)
                {
                    assertEquals(STATUS_SUCCEED, eventContext.getString(STATUS));

                    var emailMessage = eventContext.getJsonObject(EVENT_CONTEXT).getString(EMAIL_NOTIFICATION_SUBJECT);

                    if (emailMessage.contains("Motadata Notification"))
                    {
                        messageConsumer.unregister(result -> testContext.completeNow());
                    }

                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });
    }

    private void assertSMSNotificationTestResult(VertxTestContext testContext)
    {
        messageConsumer = Bootstrap.vertx().eventBus().localConsumer(EVENT_USER + TestUtil.getSessionId(), message ->
        {
            try
            {
                var eventContext = new JsonObject();

                if (message.body().containsKey(EVENT_COMPRESSION_TYPE) && message.body().getInteger(EVENT_COMPRESSION_TYPE) == COMPRESSION_TYPE_ONE)
                {
                    eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));
                }
                else
                {
                    eventContext = message.body().getJsonObject(EventBusConstants.EVENT_CONTEXT);
                }

                if (eventContext.getString(EVENT_TYPE) != null && eventContext.getString(EVENT_TYPE).contains(NotificationType.SMS.getName()) && eventContext.getString(STATUS) != null)
                {
                    assertEquals(STATUS_SUCCEED, eventContext.getString(STATUS));

                    var smsMessage = eventContext.getJsonObject(EVENT_CONTEXT).getString(SMS_NOTIFICATION_MESSAGE);

                    if (smsMessage.contains("Total Objects") && smsMessage.contains("Discovered Object") && smsMessage.contains("Failed Object") && smsMessage.contains("Discovery Progress"))
                    {
                        messageConsumer.unregister(result -> testContext.completeNow());
                    }
                }
            }
            catch (Exception exception)
            {
                testContext.failNow(exception);
            }
        });
    }


}
