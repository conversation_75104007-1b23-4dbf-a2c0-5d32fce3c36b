/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.eventengine;

import com.mindarray.*;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.CredentialProfile;
import com.mindarray.api.Discovery;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.RemoteEventProcessorCacheStore;
import com.mindarray.store.RemoteEventProcessorConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.HealthUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.Discovery.DISCOVERY_TYPE;
import static com.mindarray.api.Discovery.DISCOVERY_TYPE_IP_ADDRESS_RANGE;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.assertEquals;


@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(90 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY|SLAVE$")
@Disabled
class TestRemoteEventRouter
{
    private static final Logger LOGGER = new Logger(TestRemoteEventRouter.class, MOTADATA_EVENT_BUS, "Remote Event Router Test");

    private static final JsonObject LINUX_DISCOVERY_PARAMETERS = TestConstants.prepareParams("linux.discovery.parameters");

    private static String remoteEventProcessorUUID;

    private static String downEventProcessorUUID;

    private static MessageConsumer<JsonObject> messageConsumer;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        restartCollector(testContext);
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        var ids = new JsonArray();

        var eventProcessor = RemoteEventProcessorConfigStore.getStore().getItemIdByUUID(remoteEventProcessorUUID, null, null);

        Assertions.assertNotNull(eventProcessor);

        Assertions.assertTrue(eventProcessor > 0L);

        RemoteEventProcessorConfigStore.getStore().getItems().forEach(item ->
        {
            var context = JsonObject.mapFrom(item);

            if (!context.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID).equalsIgnoreCase(Bootstrap.getRegistrationId()) && !context.getLong(ID).equals(eventProcessor))
            {
                ids.add(context.getValue(ID));
            }
        });

        if (!ids.isEmpty())
        {
            Bootstrap.configDBService().deleteAll(ConfigDBConstants.COLLECTION_REMOTE_EVENT_PROCESSOR,
                    new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, ids),
                    DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                    {
                        RemoteEventProcessorConfigStore.getStore().deleteItems(ids);

                        ids.forEach(id -> RemoteEventProcessorCacheStore.getStore().deleteItem(CommonUtil.getLong(id)));

                        if (messageConsumer != null)
                        {
                            messageConsumer.unregister(handler1 -> testContext.completeNow());
                        }
                        else
                        {
                            testContext.completeNow();
                        }
                    });
        }
        else
        {
            if (messageConsumer != null)
            {
                messageConsumer.unregister(result -> testContext.completeNow());
            }
            else
            {
                testContext.completeNow();
            }
        }
    }

    static void restartCollector(VertxTestContext testContext)
    {

        var builder = new ProcessBuilder().command(CURRENT_DIR + PATH_SEPARATOR + "collectorrestart");

        Process process = null;

        try
        {

            process = builder.start();

            if (process.isAlive())
            {
                process.waitFor(20, TimeUnit.SECONDS);
            }

        }
        catch (Exception exception)
        {

            testContext.failNow(exception);

        }
        finally
        {

            if (process != null)
            {

                process.destroy();

            }
            testContext.completeNow();
        }


    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext)
    {
        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @Test
    @Order(1)
    void testRemoteEventProcessorRegistration(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(10, TimeUnit.SECONDS);

        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        // uuid for collector
        remoteEventProcessorUUID = System.getProperty("build.pipeline.collector.uuid");

        var context = new JsonObject().put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_HOST, System.getProperty("build.pipeline.collector.host"))
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, BootstrapType.COLLECTOR)
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_IP, System.getProperty("build.pipeline.collector.address"))
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, remoteEventProcessorUUID)
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_VERSION, 1.0);

        TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_REGISTRATION, context.put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_REGISTRATION));

        var retries = new AtomicInteger(0);

        TestUtil.vertx().setPeriodic(1000, timer ->
        {
            retries.getAndIncrement();

            var id = RemoteEventProcessorConfigStore.getStore().getItemIdByUUID(remoteEventProcessorUUID, null, null);

            if (id != null)
            {
                TestUtil.vertx().cancelTimer(timer);

                Assertions.assertTrue(id > 0);

                testContext.completeNow();
            }
            else if (retries.get() == 10)
            {
                TestUtil.vertx().cancelTimer(timer);

                testContext.failNow(new Exception("failed to register remote event processor"));
            }
        });
    }

    //#25056
    @Test
    @Order(2)
    void testVerifyDuplicateRemoteEventProcessorRegistration(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(10, TimeUnit.SECONDS);

        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var size = RemoteEventProcessorConfigStore.getStore().getItems().size();

        var context = new JsonObject().put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_HOST, "motadata8235-updated")
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, BootstrapType.COLLECTOR)
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_IP, System.getProperty("build.pipeline.collector.address"))
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, remoteEventProcessorUUID)
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_VERSION, 1.0);

        TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_REGISTRATION, context.put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_REGISTRATION));

        testContext.awaitCompletion(7, TimeUnit.SECONDS);

        Assertions.assertEquals(size, RemoteEventProcessorConfigStore.getStore().getItems().size());

        testContext.completeNow();
    }

    @Test
    @Order(3)
    void testRouteEventMaster(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var defaultId = RemoteEventProcessorConfigStore.getStore().getItemIdByUUID(Bootstrap.getRegistrationId(), null, null);

        var credentialProfileName = "Linux/Unix-Test-" + System.currentTimeMillis();

        var discoveryParameters = LINUX_DISCOVERY_PARAMETERS.copy().put(Discovery.DISCOVERY_TARGET, "172.16.8.135").put(AIOpsObject.OBJECT_EVENT_PROCESSORS, new JsonArray().add(defaultId)).put(Discovery.DISCOVERY_NAME, "Linux/Unix-Test" + System.currentTimeMillis());

        TestAPIUtil.createCredentialProfile(TestConstants.prepareParams("testRootLinuxDiscovery").put(CredentialProfile.CREDENTIAL_PROFILE_NAME, credentialProfileName), vertxTestContext).onComplete(result ->
        {
            if (result.succeeded())
            {
                var credentialId = result.result();

                if (credentialId != null && credentialId != 0)
                {
                    discoveryParameters.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(credentialId));
                }

                discoveryParameters.put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

                TestAPIUtil.post(TestAPIConstants.DISCOVERY_API_ENDPOINT, discoveryParameters, vertxTestContext.succeeding(discoveryResponse ->
                        vertxTestContext.verify(() ->
                        {
                            assertEquals(SC_OK, discoveryResponse.statusCode());

                            var discoveryId = discoveryResponse.bodyAsJsonObject().getLong(ID);

                            TestAPIUtil.post(String.format(TestAPIConstants.DISCOVERY_RUN_API_ENDPOINT, discoveryId), new JsonObject(), response ->
                                    vertxTestContext.verify(() ->
                                    {
                                        vertxTestContext.awaitCompletion(2, TimeUnit.SECONDS);

                                        assertEventRoutingTestResult(vertxTestContext, Bootstrap.getRegistrationId(), false);
                                    }));
                        })));
            }
        });
    }

    @Test
    @Order(4)
    void testEventAcknowledgementScenarioMaster(VertxTestContext vertxTestContext, TestInfo testInfo) throws Exception
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        vertxTestContext.awaitCompletion(2, TimeUnit.SECONDS);

        assertEventRoutingTestResult(vertxTestContext, Bootstrap.getRegistrationId(), true);
    }

    @Test
    @Order(5)
    void testRouteEvent(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var credentialProfileName = "Linux/Unix-Test-" + System.currentTimeMillis();

        var discoveryParameters = LINUX_DISCOVERY_PARAMETERS.copy().put(Discovery.DISCOVERY_TARGET, "172.16.8.135").put(AIOpsObject.OBJECT_EVENT_PROCESSORS, new JsonArray()).put(Discovery.DISCOVERY_NAME, "Linux/Unix-Test" + System.currentTimeMillis());

        TestAPIUtil.createCredentialProfile(TestConstants.prepareParams("testRootLinuxDiscovery").put(CredentialProfile.CREDENTIAL_PROFILE_NAME, credentialProfileName), vertxTestContext).onComplete(result ->
        {
            if (result.succeeded())
            {
                var credentialId = result.result();

                if (credentialId != null && credentialId != 0)
                {
                    discoveryParameters.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(credentialId));
                }

                discoveryParameters.put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

                TestAPIUtil.post(TestAPIConstants.DISCOVERY_API_ENDPOINT, discoveryParameters, vertxTestContext.succeeding(discoveryResponse ->
                        vertxTestContext.verify(() ->
                        {
                            assertEquals(SC_OK, discoveryResponse.statusCode());

                            var discoveryId = discoveryResponse.bodyAsJsonObject().getLong(ID);

                            TestAPIUtil.post(String.format(TestAPIConstants.DISCOVERY_RUN_API_ENDPOINT, discoveryId), new JsonObject(), response ->
                                    vertxTestContext.verify(() ->
                                    {
                                        vertxTestContext.awaitCompletion(2, TimeUnit.SECONDS);

                                        assertEventRoutingTestResult(vertxTestContext, null, false);
                                    }));
                        })));
            }
        });
    }

    @Test
    @Order(6)
    void testEventAcknowledgement0(VertxTestContext vertxTestContext, TestInfo testInfo) throws Exception
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        vertxTestContext.awaitCompletion(2, TimeUnit.SECONDS);

        assertEventRoutingTestResult(vertxTestContext, Bootstrap.getRegistrationId(), false);
    }

    @Test
    @Order(7)
    void testRouteEventRemoteCollector(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var id = RemoteEventProcessorConfigStore.getStore().getItemIdByUUID(remoteEventProcessorUUID, null, null);

        var credentialProfileName = "Linux/Unix-Test-" + System.currentTimeMillis();

        var discoveryParameters = LINUX_DISCOVERY_PARAMETERS.copy().put(Discovery.DISCOVERY_TARGET, "172.16.8.135").put(AIOpsObject.OBJECT_EVENT_PROCESSORS, new JsonArray().add(id)).put(Discovery.DISCOVERY_NAME, "Linux/Unix-Test" + System.currentTimeMillis());

        TestAPIUtil.createCredentialProfile(TestConstants.prepareParams("testRootLinuxDiscovery").put(CredentialProfile.CREDENTIAL_PROFILE_NAME, credentialProfileName), vertxTestContext).onComplete(result ->
        {
            if (result.succeeded())
            {
                var credentialId = result.result();

                if (credentialId != null && credentialId != 0)
                {
                    discoveryParameters.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(credentialId));
                }

                discoveryParameters.put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

                TestAPIUtil.post(TestAPIConstants.DISCOVERY_API_ENDPOINT, discoveryParameters, vertxTestContext.succeeding(discoveryResponse ->
                        vertxTestContext.verify(() ->
                        {
                            assertEquals(SC_OK, discoveryResponse.statusCode());

                            var discoveryId = discoveryResponse.bodyAsJsonObject().getLong(ID);

                            TestAPIUtil.post(String.format(TestAPIConstants.DISCOVERY_RUN_API_ENDPOINT, discoveryId), new JsonObject(), response ->
                                    vertxTestContext.verify(() ->
                                    {
                                        vertxTestContext.awaitCompletion(2, TimeUnit.SECONDS);

                                        assertEventRoutingTestResult(vertxTestContext, remoteEventProcessorUUID, false);
                                    }));
                        })));
            }
        });
    }

    @Test
    @Order(8)
    void testEventAcknowledgement1(VertxTestContext vertxTestContext, TestInfo testInfo) throws Exception
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        vertxTestContext.awaitCompletion(2, TimeUnit.SECONDS);

        assertEventRoutingTestResult(vertxTestContext, remoteEventProcessorUUID, false);
    }

    @Test
    @Order(9)
    void testRemoteCollectorRegistrationDownState(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        downEventProcessorUUID = UUID.randomUUID().toString().toLowerCase().trim();

        var context = new JsonObject().put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_HOST, "test.register")
                .put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, downEventProcessorUUID);

        TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_REGISTRATION, context.put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_REGISTRATION));

        var retries = new AtomicInteger(0);

        TestUtil.vertx().setPeriodic(1000, timer ->
        {
            retries.getAndIncrement();

            var id = RemoteEventProcessorConfigStore.getStore().getItemIdByUUID(downEventProcessorUUID, null, null);

            if (id != null)
            {
                TestUtil.vertx().cancelTimer(timer);

                testContext.completeNow();
            }
            else if (retries.get() == 10)
            {
                TestUtil.vertx().cancelTimer(timer);

                testContext.failNow(new Exception("failed to register remote event processor"));
            }
        });
    }

    @Test
    @Order(10)
    void testRouteEventInvalidRemoteCollector(VertxTestContext vertxTestContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var id = RemoteEventProcessorConfigStore.getStore().getItemIdByUUID(downEventProcessorUUID, null, null);

        var defaultId = RemoteEventProcessorConfigStore.getStore().getItemIdByUUID(Bootstrap.getRegistrationId(), null, null);

        var credentialProfileName = "Linux/Unix-Test-" + System.currentTimeMillis();

        var range = "************-126";

        var discoveryParameters = LINUX_DISCOVERY_PARAMETERS.copy().put(DISCOVERY_TYPE, DISCOVERY_TYPE_IP_ADDRESS_RANGE).put(Discovery.DISCOVERY_TARGET, range).put(AIOpsObject.OBJECT_EVENT_PROCESSORS, new JsonArray().add(id).add(defaultId)).put(Discovery.DISCOVERY_NAME, "Linux/Unix-Test" + System.currentTimeMillis());

        TestAPIUtil.createCredentialProfile(TestConstants.prepareParams("testRootLinuxDiscovery").put(CredentialProfile.CREDENTIAL_PROFILE_NAME, credentialProfileName), vertxTestContext).onComplete(result ->
        {
            if (result.succeeded())
            {
                var credentialId = result.result();

                if (credentialId != null && credentialId != 0)
                {
                    discoveryParameters.put(Discovery.DISCOVERY_CREDENTIAL_PROFILES, new JsonArray().add(credentialId));
                }

                discoveryParameters.put(Discovery.DISCOVERY_GROUPS, new JsonArray().add(CommonUtil.getLong("10000000000013")));

                TestAPIUtil.post(TestAPIConstants.DISCOVERY_API_ENDPOINT, discoveryParameters, vertxTestContext.succeeding(discoveryResponse ->
                        vertxTestContext.verify(() ->
                        {
                            assertEquals(SC_OK, discoveryResponse.statusCode());

                            var discoveryId = discoveryResponse.bodyAsJsonObject().getLong(ID);

                            TestAPIUtil.post(String.format(TestAPIConstants.DISCOVERY_RUN_API_ENDPOINT, discoveryId), new JsonObject(), response ->
                                    vertxTestContext.verify(() ->
                                    {
                                        vertxTestContext.awaitCompletion(2, TimeUnit.SECONDS);

                                        assertEventRoutingTestResult(vertxTestContext, downEventProcessorUUID, true);
                                    }));
                        })));
            }
        });
    }

    @Test
    @Order(11)
    void testEventTimeout(VertxTestContext vertxTestContext, TestInfo testInfo) throws Exception
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        vertxTestContext.awaitCompletion(30, TimeUnit.SECONDS);

        assertEventRoutingTestResult(vertxTestContext, Bootstrap.getRegistrationId(), false);
    }

    @Test
    @Order(12)
    void testVerifyDuplicateAPPRegistration(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(5, TimeUnit.SECONDS);

        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var size = RemoteEventProcessorConfigStore.getStore().getItems().size();

        var items = RemoteEventProcessorConfigStore.getStore().flatItemsByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE, BootstrapType.APP.name(), RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE, MotadataConfigUtil.getInstallationMode());

        var context = items.getJsonObject(0).copy().put(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId() + System.currentTimeMillis());

        TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_REGISTRATION, context.put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_REGISTRATION));

        testContext.awaitCompletion(10, TimeUnit.SECONDS);

        Assertions.assertEquals(size, RemoteEventProcessorConfigStore.getStore().getItems().size());

        testContext.completeNow();
    }

    void assertEventRoutingTestResult(VertxTestContext testContext, String processorUUID, boolean clearProbe)
    {
        try
        {
            messageConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_ENGINE_STATS_RESPONSE, message ->
            {
                try
                {
                    if (message.body() != null)
                    {
                        var event = message.body();

                        if (event.getString(EventBusConstants.EVENT_TYPE).equalsIgnoreCase(EventBusConstants.EVENT_ENGINE_STATS) && event.getString(EventBusConstants.ENGINE_TYPE).matches(EventBusConstants.EVENT_ROUTER + "." + "\\d+"))
                        {
                            messageConsumer.unregister();

                            event.remove(EventBusConstants.EVENT_TYPE);

                            event.remove(EventBusConstants.ENGINE_TYPE);

                            Assertions.assertNotNull(event.getJsonObject(HealthUtil.HEALTH_STATS));

                            var pendingEvents = event.getJsonObject(HealthUtil.HEALTH_STATS).getJsonObject("pending.event.processors");

                            Assertions.assertNotNull(pendingEvents);

                            if (processorUUID != null)
                            {
                                var pendingEvent = pendingEvents.getInteger(processorUUID);

                                if (pendingEvent <= 0)
                                {
                                    if (clearProbe)
                                    {
                                        assertEventRoutingTestResult(event, testContext, processorUUID);
                                    }
                                    else
                                    {
                                        testContext.completeNow();
                                    }
                                }
                                else
                                {
                                    assertEventRoutingTestResult(testContext, processorUUID, false);
                                }
                            }
                            else
                            {
                                var complete = new AtomicBoolean(true);

                                pendingEvents.getMap().forEach((key, value) ->
                                {
                                    var pendingEvent = pendingEvents.getInteger(key);

                                    if (pendingEvent != 0)
                                    {
                                        complete.set(false);
                                    }
                                });

                                if (complete.get())
                                {
                                    if (clearProbe)
                                    {
                                        assertEventRoutingTestResult(event, testContext, null);
                                    }
                                    else
                                    {
                                        testContext.completeNow();
                                    }
                                }
                                else
                                {
                                    assertEventRoutingTestResult(testContext, null, false);
                                }
                            }
                        }
                    }
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister(result -> testContext.failNow(exception));
                }
            });

            TestUtil.vertx().eventBus().publish(EventBusConstants.EVENT_ENGINE_STATS, new JsonObject());
        }
        catch (Exception exception)
        {
            messageConsumer.unregister();

            testContext.failNow(exception);
        }
    }

    private void assertEventRoutingTestResult(JsonObject context, VertxTestContext testContext, String processorUUID)
    {
        var pendingEvents = context.getJsonObject(HealthUtil.HEALTH_STATS).getInteger("pending.events");

        Assertions.assertNotNull(pendingEvents);

        if (pendingEvents == 0)
        {
            testContext.completeNow();
        }
        else
        {
            assertEventRoutingTestResult(testContext, processorUUID, true);
        }
    }

}

