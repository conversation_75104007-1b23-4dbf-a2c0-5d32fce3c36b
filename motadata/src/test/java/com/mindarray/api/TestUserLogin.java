/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.store.LDAPServerConfigStore;
import com.mindarray.store.PersonalAccessTokenConfigStore;
import com.mindarray.store.UserConfigStore;
import com.mindarray.util.CipherUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.InfoMessageConstants.LDAP_SERVER_SYNC_STARTED;
import static com.mindarray.TestAPIConstants.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.LDAPServer.*;
import static com.mindarray.api.PersonalAccessToken.PERSONAL_ACCESS_TOKEN;
import static com.mindarray.api.User.*;
import static org.apache.http.HttpStatus.SC_UNAUTHORIZED;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;

@ExtendWith(VertxExtension.class)
@Execution(ExecutionMode.SAME_THREAD)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestUserLogin
{
    private static final JsonObject CONTEXT = new JsonObject();

    private static final Logger LOGGER = new Logger(TestUserLogin.class, MOTADATA_API, "Test User Login");

    private static final JsonObject DISABLE_USER_CONTEXT = new JsonObject()
            .put(USER_NAME, "testDisable")
            .put(USER_FIRST_NAME, "test")
            .put(USER_LAST_NAME, "disable")
            .put(USER_EMAIL, "<EMAIL>")
            .put(USER_MOBILE, 123456789)
            .put(USER_PASSWORD, "Mind@123")
            .put(USER_STATUS, "no")
            .put(USER_ROLE, DEFAULT_ID)
            .put(USER_GROUPS, new JsonArray().add(DEFAULT_ID))
            .put(USER_TYPE, USER_TYPE_SYSTEM);

    private static final JsonObject USER1_CONTEXT = new JsonObject()
            .put(USER_NAME, "user1")
            .put(USER_FIRST_NAME, "motadata1")
            .put(USER_LAST_NAME, "admin1")
            .put(USER_EMAIL, "<EMAIL>")
            .put(USER_MOBILE, 123456789)
            .put(USER_PASSWORD, "MindUser@123")
            .put(USER_STATUS, "yes")
            .put(USER_ROLE, DEFAULT_ID)
            .put(USER_GROUPS, new JsonArray().add(DEFAULT_ID))
            .put(USER_TYPE, USER_TYPE_SYSTEM);

    private static final JsonObject LDAP_USER_CONTEXT = new JsonObject()
            .put(USER_NAME, "test11")
            .put(USER_FIRST_NAME, "ldap")
            .put(USER_LAST_NAME, "admin1")
            .put(USER_EMAIL, "<EMAIL>")
            .put(USER_MOBILE, 123456789)
            .put(USER_PASSWORD, "Mind@123")
            .put(USER_STATUS, "yes")
            .put(USER_ROLE, DEFAULT_ID)
            .put(USER_LDAP_SERVER, !LDAPServerConfigStore.getStore().getItems().isEmpty() ? LDAPServerConfigStore.getStore().getItem().getLong(ID) : DEFAULT_ID)
            .put(USER_GROUPS, new JsonArray().add(DEFAULT_ID))
            .put(USER_TYPE, USER_TYPE_LDAP);

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testGetLDAPServerConfiguration(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        TestAPIUtil.get(LDAP_SERVER_API_ENDPOINT, testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertGETAllRequestTestResult(response, LDAPServerConfigStore.getStore(), null);

                    CONTEXT.put("ldap.server", response.bodyAsJsonObject().getJsonArray(RESULT).getJsonObject(0).getLong(ID));

                    TestAPIUtil.post(LDAP_SERVER_API_ENDPOINT + "/sync/" + CONTEXT.getLong("ldap.server"), testContext.succeeding(httpResponse ->
                            testContext.verify(() ->
                            {
                                TestAPIUtil.assertValidResponseTestResult(httpResponse, LOGGER, testInfo.getTestMethod().get().getName());

                                assertEquals(LDAP_SERVER_SYNC_STARTED, httpResponse.bodyAsJsonObject().getString(MESSAGE));

                                testContext.completeNow();
                            })));
                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testGetLDAPUserAndSystemUser(VertxTestContext testContext)
    {
        TestUtil.vertx().setTimer(TimeUnit.SECONDS.toMillis(15), timer ->
                TestAPIUtil.get(USER_API_ENDPOINT, testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertGETAllRequestTestResult(response, UserConfigStore.getStore(), null);

                            var items = response.bodyAsJsonObject().getJsonArray(RESULT);

                            assertNotNull(items);

                            for (var index = 0; index < items.size(); index++)
                            {
                                CONTEXT.put("user", items.getJsonObject(index).getLong(ID));

                                if (items.getJsonObject(index).getString(USER_TYPE).equalsIgnoreCase(USER_TYPE_SYSTEM) && items.getJsonObject(index).getString(USER_NAME).equalsIgnoreCase("user1"))
                                {
                                    CONTEXT.put("custom.user", items.getJsonObject(index).getLong(ID));
                                }

                                if (items.getJsonObject(index).getString(USER_TYPE).equalsIgnoreCase(USER_TYPE_LDAP) && items.getJsonObject(index).getString(USER_NAME).equalsIgnoreCase("chandu"))
                                {
                                    CONTEXT.put("ldap.user", items.getJsonObject(index).getLong(ID));
                                }
                            }

                            testContext.completeNow();
                        }))));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testLoginFailedLDAPAuthDisabled(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(USER_API_ENDPOINT + CONTEXT.getLong("ldap.user"),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                            Assertions.assertNotNull(body);

                            Assertions.assertTrue(body.containsKey(USER_NAME));

                            Assertions.assertTrue(body.getString(USER_NAME).equalsIgnoreCase("chandu"));

                            Assertions.assertTrue(body.getString(USER_TYPE).equalsIgnoreCase(USER_TYPE_LDAP));

                            Assertions.assertNotNull(body.getJsonObject(USER_PREFERENCES));

                            TestAPIUtil.create(ACCESS_TOKEN_API_ENDPOINT, new JsonObject().put(USER_NAME, body.getString(USER_NAME)).put(USER_PASSWORD, "Mind@123"),
                                    testContext.succeeding(httpResponse ->
                                            testContext.verify(() ->
                                            {
                                                assertEquals(SC_UNAUTHORIZED, httpResponse.statusCode());

                                                var output = httpResponse.bodyAsJsonObject();

                                                Assertions.assertNotNull(output);

                                                assertEquals(ErrorCodes.ERROR_CODE_LOGIN_LDAP_AUTH_DISABLED, output.getString(ERROR_CODE));

                                                assertEquals(SC_UNAUTHORIZED, output.getInteger(RESPONSE_CODE));

                                                testContext.completeNow();
                                            })));

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testLDAPUserLoginSucceed(VertxTestContext testContext, TestInfo testInfo)
    {

        var userRole = new JsonObject("{ \"user.name\": \"chandu\", \"user.role\": 10000000000001, \"user.groups\": [ 10000000000017, 10000000000018, 10000000000019, 10000000000046, 10000000000047, 10000000000044, 10000000000045, 10000000000023, 10000000000024, 10000000000025, 10000000000066, 10000000000020, 10000000000022, 10000000000021, 10000000000067, 10000000000026, 10000000000027, 10000000000030, 10000000000031, 10000000000028, 10000000000029, 10000000000032, 10000000000002, 10000000000003, 10000000000004, 10000000000005, 10000000000050, 10000000000051, 10000000000048, 10000000000049, 10000000000052, 10000000000053, 10000000000006, 10000000000007, 10000000000008, 10000000000054, 10000000000055, 10000000000056, 10000000000057, 10000000000065, 10000000000009, 10000000000010, 10000000000011, 10000000000012, 10000000000058, 10000000000059, 10000000000060, 10000000000061, 10000000000062, 10000000000063, 10000000000064, 10000000000001, 10000000000013, 10000000000016, 10000000000014, 10000000000015, 10000000000033, 10000000000034, 10000000000035, 10000000000038, 10000000000039, 10000000000036, 10000000000037, 10000000000042, 10000000000043, 10000000000040, 10000000000041, 10000000000082, 10000000000083, 10000000000080, 10000000000081, 10000000000086, 10000000000087, 10000000000084, 10000000000085, 10000000000090, 10000000000091, 10000000000088, 10000000000089, 10000000000094, 10000000000095, 10000000000092, 10000000000093, 10000000000070, 10000000000071, 10000000000068, 10000000000069, 10000000000074, 10000000000075, 10000000000072, 10000000000073, 10000000000078, 10000000000079, 10000000000076, 10000000000077, 10000000000098, 10000000000096, 10000000000097 ], \"user.status\": \"yes\", \"user.first.name\": \"chandu\", \"user.last.name\": \"thakkar\", \"user.mobile\": 0 }");

        var context = new JsonObject()
                .put(LDAP_SERVER_PRIMARY_HOST, "************")
                .put(LDAP_SERVER_FQDN, "motadata.in")
                .put(LDAP_SERVER_PORT, 636)
                .put(LDAP_SERVER_USERNAME, "dhvani")
                .put(LDAP_SERVER_PASSWORD, "Mind@123")
                .put(LDAP_AUTHENTICATION, YES)
                .put(LDAP_AUTO_SYNC, NO)
                .put(LDAP_SERVER_PROTOCOL, LDAP_SERVER_PROTOCOL_SSL)
                .put(LDAP_SERVER_TYPE, LDAP_SERVER_TYPE_AD)
                .put(LDAP_SERVER_USER_GROUPS, new JsonArray().add("motadata users"));

        TestAPIUtil.put(LDAP_SERVER_API_ENDPOINT + "/" + TestUserLogin.CONTEXT.getLong("ldap.server"), context,
                testContext.succeeding(response -> testContext.verify(() ->
                {

                    TestAPIUtil.assertUpdateEntityTestResult(LDAPServerConfigStore.getStore(), context, response.bodyAsJsonObject(),
                            String.format(InfoMessageConstants.ENTITY_UPDATED, "LDAP Server"), LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.awaitCompletion(10, TimeUnit.SECONDS);

                    TestAPIUtil.put(USER_API_ENDPOINT + TestUserLogin.CONTEXT.getLong("ldap.user"), userRole,
                            testContext.succeeding(updateResponse ->
                                    testContext.verify(() ->
                                    {
                                        Assertions.assertTrue(updateResponse.bodyAsJsonObject().getString(MESSAGE).equalsIgnoreCase("User updated successfully"));

                                        TestAPIUtil.get(USER_API_ENDPOINT + TestUserLogin.CONTEXT.getLong("ldap.user"),
                                                testContext.succeeding(getResponse ->
                                                        testContext.verify(() ->
                                                        {
                                                            TestAPIUtil.assertValidResponseTestResult(getResponse, LOGGER, testInfo.getTestMethod().get().getName());

                                                            var body = getResponse.bodyAsJsonObject().getJsonObject(RESULT);

                                                            LOGGER.info(String.format("User get response : %s", body));

                                                            Assertions.assertNotNull(body);

                                                            Assertions.assertTrue(body.containsKey(USER_NAME));

                                                            Assertions.assertTrue(body.getString(USER_NAME).equalsIgnoreCase("chandu"));

                                                            TestAPIUtil.post(ACCESS_TOKEN_API_ENDPOINT, new JsonObject().put(USER_NAME, body.getString(USER_NAME)).put(USER_PASSWORD, "Mind@123"),
                                                                    testContext.succeeding(httpResponse ->
                                                                            testContext.verify(() ->
                                                                            {
                                                                                assertEquals(HttpStatus.SC_OK, httpResponse.statusCode());

                                                                                var output = httpResponse.bodyAsJsonObject();

                                                                                Assertions.assertNotNull(output);

                                                                                Assertions.assertTrue(output.containsKey(APIConstants.AUTH_ACCESS_TOKEN));

                                                                                testContext.completeNow();
                                                                            })));
                                                        })));

                                    })));
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testUpdateLDAPServer(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(LDAP_SERVER_PRIMARY_HOST, "************")
                .put(LDAP_SERVER_FQDN, "motadata.in")
                .put(LDAP_SERVER_PORT, 389)
                .put(LDAP_SERVER_USERNAME, "administrator")
                .put(LDAP_SERVER_PASSWORD, "mind@123")
                .put(LDAP_AUTHENTICATION, NO)
                .put(LDAP_AUTO_SYNC, NO)
                .put(LDAP_AUTHENTICATION, YES)
                .put(LDAP_SERVER_PROTOCOL, LDAP_SERVER_PROTOCOL_PLAIN)
                .put(LDAP_SERVER_TYPE, LDAP_SERVER_TYPE_AD);

        TestAPIUtil.put(LDAP_SERVER_API_ENDPOINT + "/" + TestUserLogin.CONTEXT.getLong("ldap.server"), context,
                testContext.succeeding(response -> testContext.verify(() ->
                {

                    TestAPIUtil.assertUpdateEntityTestResult(LDAPServerConfigStore.getStore(), context, response.bodyAsJsonObject(),
                            String.format(InfoMessageConstants.ENTITY_UPDATED, "LDAP Server"), LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();
                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testLoginFailedLDAPUserNotConfigured(VertxTestContext testContext)
    {

        TestAPIUtil.create(ACCESS_TOKEN_API_ENDPOINT, new JsonObject().put(USER_NAME, "test1234").put(USER_PASSWORD, "Mind@123"),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            assertEquals(SC_UNAUTHORIZED, response.statusCode());

                            var body = response.bodyAsJsonObject();

                            Assertions.assertNotNull(body);

                            assertEquals(ErrorCodes.ERROR_CODE_LOGIN_INVALID_CREDENTIALS, body.getString(ERROR_CODE));

                            testContext.completeNow();

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testLoginFailedLDAPUserNotBelongToMotadataUserGroup(VertxTestContext testContext, TestInfo testInfo)
    {

        TestAPIUtil.post(USER_API_ENDPOINT, LDAP_USER_CONTEXT,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(UserConfigStore.getStore(), LDAP_USER_CONTEXT, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.USER.getName()), new JsonArray().add(USER_PREFERENCES).add(USER_PASSWORD_LAST_UPDATED_TIME), LOGGER, testInfo.getTestMethod().get().getName());

                            CONTEXT.put("ldap.user", response.bodyAsJsonObject().getLong(ID));

                            TestAPIUtil.create(ACCESS_TOKEN_API_ENDPOINT, new JsonObject().put(USER_NAME, "test11").put(USER_PASSWORD, "Mind@123"),
                                    testContext.succeeding(httpResponse ->
                                            testContext.verify(() ->
                                            {
                                                assertEquals(SC_UNAUTHORIZED, httpResponse.statusCode());

                                                var body = httpResponse.bodyAsJsonObject();

                                                Assertions.assertNotNull(body);

                                                assertEquals(SC_UNAUTHORIZED, body.getInteger(RESPONSE_CODE));

                                                testContext.completeNow();

                                            })));
                        })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testDisableUser(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(USER_STATUS, "no");

        TestAPIUtil.put(USER_API_ENDPOINT + TestUserLogin.CONTEXT.getLong("custom.user"), context,

                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(UserConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.USER.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testLoginFailedDisabledUser(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(USER_API_ENDPOINT + CONTEXT.getLong("custom.user"),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                            Assertions.assertNotNull(body);

                            Assertions.assertTrue(body.containsKey(USER_NAME));

                            Assertions.assertTrue(body.getString(USER_NAME).equalsIgnoreCase("user1"));

                            Assertions.assertTrue(body.getString(USER_TYPE).equalsIgnoreCase(USER_TYPE_SYSTEM));

                            TestAPIUtil.create(ACCESS_TOKEN_API_ENDPOINT, new JsonObject().put(USER_NAME, "user1").put(USER_PASSWORD, "MindUser@123"),
                                    testContext.succeeding(httpResponse ->
                                            testContext.verify(() ->
                                            {
                                                assertEquals(SC_UNAUTHORIZED, httpResponse.statusCode());

                                                var output = httpResponse.bodyAsJsonObject();

                                                Assertions.assertNotNull(output);

                                                assertEquals(ErrorCodes.ERROR_CODE_LOGIN_USER_DISABLED, output.getString(ERROR_CODE));

                                                testContext.completeNow();

                                            })));
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testEnableUser(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(USER_STATUS, "Yes");

        TestAPIUtil.put(USER_API_ENDPOINT + TestUserLogin.CONTEXT.getLong("custom.user"), context,

                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertUpdateEntityTestResult(UserConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_UPDATED, Entity.USER.getName()), LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testLoginSucceeded(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(USER_API_ENDPOINT + TestUserLogin.CONTEXT.getLong("custom.user"),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                            Assertions.assertNotNull(body);

                            Assertions.assertTrue(body.containsKey(USER_NAME));

                            Assertions.assertTrue(body.getString(USER_NAME).equalsIgnoreCase("user1"));

                            TestAPIUtil.post(ACCESS_TOKEN_API_ENDPOINT, new JsonObject().put(USER_NAME, "user1").put(USER_PASSWORD, "MindUser@123"),
                                    testContext.succeeding(httpResponse ->
                                            testContext.verify(() ->
                                            {
                                                assertEquals(HttpStatus.SC_OK, httpResponse.statusCode());

                                                var output = httpResponse.bodyAsJsonObject();

                                                Assertions.assertNotNull(output);

                                                Assertions.assertTrue(output.containsKey(USER_TEMPORARY_PASSWORD));

                                                Assertions.assertEquals(NO, output.getString(USER_TEMPORARY_PASSWORD));

                                                Assertions.assertTrue(output.containsKey(APIConstants.AUTH_ACCESS_TOKEN));

                                                CONTEXT.put("Refresh.token.user1", output.getString(AUTH_REFRESH_TOKEN));

                                                CONTEXT.put("access.token", "Bearer " + output.getString(AUTH_ACCESS_TOKEN));

                                                testContext.completeNow();
                                            })));

                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testCreateUser2(VertxTestContext testContext, TestInfo testInfo)
    {

        var context = USER1_CONTEXT.copy().put(USER_NAME, "User2.access");

        TestAPIUtil.post(USER_API_ENDPOINT, context,
                testContext.succeeding(response ->

                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(UserConfigStore.getStore(), context, response.bodyAsJsonObject(), String.format(InfoMessageConstants.ENTITY_CREATED, Entity.USER.getName()), new JsonArray().add(USER_PREFERENCES).add(USER_PASSWORD_LAST_UPDATED_TIME), LOGGER, testInfo.getTestMethod().get().getName());

                            TestUserLogin.CONTEXT.put("user.id2", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testLoginFailedInvalidUser(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(USER_API_ENDPOINT + CONTEXT.getLong("custom.user"),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                            Assertions.assertNotNull(body);

                            Assertions.assertTrue(body.containsKey(USER_NAME));

                            TestAPIUtil.create(ACCESS_TOKEN_API_ENDPOINT, new JsonObject().put(USER_NAME, "adminUser1").put(USER_PASSWORD, "MindUser@123"),
                                    testContext.succeeding(httpResponse ->
                                            testContext.verify(() ->
                                            {
                                                assertEquals(SC_UNAUTHORIZED, httpResponse.statusCode());

                                                var output = httpResponse.bodyAsJsonObject();

                                                Assertions.assertNotNull(output);

                                                assertEquals(ErrorCodes.ERROR_CODE_LOGIN_INVALID_CREDENTIALS, output.getString(ERROR_CODE));

                                                testContext.completeNow();
                                            })));
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testLoginFailedInvalidPassword(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(USER_API_ENDPOINT + CONTEXT.getLong("custom.user"),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                            Assertions.assertNotNull(body);

                            Assertions.assertTrue(body.containsKey(USER_NAME));

                            Assertions.assertTrue(body.getString(USER_NAME).equalsIgnoreCase("user1"));

                            assertEquals(USER_TYPE_SYSTEM, body.getString(USER_TYPE));

                            TestAPIUtil.create(ACCESS_TOKEN_API_ENDPOINT, new JsonObject().put(USER_NAME, "user1").put(USER_PASSWORD, "MindUser@1234"),
                                    testContext.succeeding(httpResponse ->
                                            testContext.verify(() ->
                                            {
                                                assertEquals(SC_UNAUTHORIZED, httpResponse.statusCode());

                                                var output = httpResponse.bodyAsJsonObject();

                                                Assertions.assertNotNull(output);

                                                assertEquals(ErrorCodes.ERROR_CODE_LOGIN_INVALID_CREDENTIALS, output.getString(ERROR_CODE));

                                                assertEquals(SC_UNAUTHORIZED, output.getInteger(RESPONSE_CODE));

                                                testContext.completeNow();
                                            })));
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testLoginFailedUserNameIsNULL(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(USER_API_ENDPOINT + CONTEXT.getLong("custom.user"),
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                            Assertions.assertNotNull(body);

                            Assertions.assertTrue(body.containsKey(USER_NAME));

                            Assertions.assertTrue(body.getString(USER_NAME).equalsIgnoreCase("user1"));

                            assertEquals(USER_TYPE_SYSTEM, body.getString(USER_TYPE));

                            TestAPIUtil.create(ACCESS_TOKEN_API_ENDPOINT, new JsonObject().put(USER_NAME, "").put(USER_PASSWORD, "MindUser@1234"),
                                    testContext.succeeding(httpResponse ->
                                            testContext.verify(() ->
                                            {
                                                assertEquals(SC_UNAUTHORIZED, httpResponse.statusCode());

                                                var output = httpResponse.bodyAsJsonObject();

                                                Assertions.assertNotNull(output);

                                                assertEquals(ErrorCodes.ERROR_CODE_LOGIN_INVALID_CREDENTIALS, output.getString(ERROR_CODE));

                                                testContext.completeNow();
                                            })));
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testLoginFailedPasswordIsMissing(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(USER_API_ENDPOINT + CONTEXT.getLong("custom.user"),

                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                            Assertions.assertNotNull(body);

                            Assertions.assertTrue(body.containsKey(USER_NAME));

                            Assertions.assertTrue(body.getString(USER_NAME).equalsIgnoreCase("user1"));

                            assertEquals(USER_TYPE_SYSTEM, body.getString(USER_TYPE));

                            TestAPIUtil.create(ACCESS_TOKEN_API_ENDPOINT,
                                    new JsonObject().put(USER_NAME, "user1"),
                                    testContext.succeeding(httpResponse ->
                                            testContext.verify(() ->
                                            {
                                                assertEquals(SC_UNAUTHORIZED, httpResponse.statusCode());

                                                var output = httpResponse.bodyAsJsonObject();

                                                assertEquals(SC_UNAUTHORIZED, output.getInteger(RESPONSE_CODE));

                                                assertEquals(ErrorCodes.ERROR_CODE_BAD_REQUEST, output.getString(ERROR_CODE));

                                                testContext.completeNow();
                                            })));
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testLoginFailedUserNameIsMissing(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(USER_API_ENDPOINT + CONTEXT.getLong("custom.user"),

                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                            Assertions.assertNotNull(body);

                            Assertions.assertTrue(body.containsKey(USER_NAME));

                            Assertions.assertTrue(body.getString(USER_NAME).equalsIgnoreCase("user1"));

                            assertEquals(USER_TYPE_SYSTEM, body.getString(USER_TYPE));

                            TestAPIUtil.create(ACCESS_TOKEN_API_ENDPOINT,
                                    new JsonObject().put(USER_PASSWORD, "MindUser@123"),
                                    testContext.succeeding(httpResponse ->
                                            testContext.verify(() ->
                                            {
                                                assertEquals(SC_UNAUTHORIZED, httpResponse.statusCode());

                                                var output = httpResponse.bodyAsJsonObject();

                                                Assertions.assertNotNull(output);

                                                assertEquals(ErrorCodes.ERROR_CODE_LOGIN_INVALID_CREDENTIALS, output.getString(ERROR_CODE));

                                                testContext.completeNow();
                                            })));
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testCreateDisableUser(VertxTestContext testContext, TestInfo testInfo)
    {

        TestAPIUtil.post(USER_API_ENDPOINT, DISABLE_USER_CONTEXT,
                testContext.succeeding(response ->

                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                            testContext.completeNow();

                        })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testLogout(VertxTestContext testContext)
    {
        TestAPIUtil.post(ACCESS_TOKEN_API_ENDPOINT, new JsonObject().put(USER_NAME, "User2.access").put(USER_PASSWORD, "MindUser@123"), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    assertEquals(HttpStatus.SC_OK, response.statusCode());

                    var body = response.bodyAsJsonObject();

                    Assertions.assertNotNull(body);

                    Assertions.assertTrue(body.containsKey(APIConstants.AUTH_ACCESS_TOKEN));

                    CONTEXT.put("Refresh.token.user1", body.getString(AUTH_REFRESH_TOKEN));

                    var item = new JsonObject().put(AUTH_REFRESH_TOKEN, CONTEXT.getString("Refresh.token.user1"));

                    TestAPIUtil.post("/api/v1/user/logout", item,
                            testContext.succeeding(httpResponse ->
                                    testContext.verify(() ->
                                    {
                                        assertEquals(HttpStatus.SC_MOVED_TEMPORARILY, httpResponse.statusCode());

                                        assertEquals("Found", httpResponse.statusMessage());

                                        testContext.completeNow();

                                    })));

                })));

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testForgetPasswordAdminUser(VertxTestContext testContext)
    {
        TestAPIUtil.post(FORGOT_PASSWORD_ENDPOINT, new JsonObject().put(USER_NAME, "admin"),
                testContext.succeeding(httpResponse ->
                        testContext.verify(() ->
                        {
                            assertEquals(HttpStatus.SC_BAD_REQUEST, httpResponse.statusCode());

                            var body = httpResponse.bodyAsJsonObject();

                            Assertions.assertNotNull(body);

                            Assertions.assertEquals(STATUS_FAIL, body.getString(STATUS));

                            Assertions.assertEquals(ErrorCodes.ERROR_CODE_RESET_PASSWORD_SUPER_ADMIN, body.getString(ERROR_CODE));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    @Disabled
    void testForgetPasswordLDAPUser(VertxTestContext testContext)
    {
        TestAPIUtil.post(FORGOT_PASSWORD_ENDPOINT, new JsonObject().put(USER_NAME, "test11"),
                testContext.succeeding(httpResponse ->
                        testContext.verify(() ->
                        {
                            assertEquals(HttpStatus.SC_BAD_REQUEST, httpResponse.statusCode());

                            var body = httpResponse.bodyAsJsonObject();

                            Assertions.assertNotNull(body);

                            Assertions.assertEquals(STATUS_FAIL, body.getString(STATUS));

                            Assertions.assertEquals(ErrorCodes.ERROR_CODE_RESET_PASSWORD_LDAP_USER, body.getString(ERROR_CODE));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testForgetPasswordDisableUser(VertxTestContext testContext)
    {
        TestAPIUtil.post(FORGOT_PASSWORD_ENDPOINT, new JsonObject().put(USER_NAME, "testDisable"),
                testContext.succeeding(httpResponse ->
                        testContext.verify(() ->
                        {
                            assertEquals(HttpStatus.SC_BAD_REQUEST, httpResponse.statusCode());

                            var body = httpResponse.bodyAsJsonObject();

                            Assertions.assertNotNull(body);

                            Assertions.assertEquals(STATUS_FAIL, body.getString(STATUS));

                            Assertions.assertEquals(ErrorCodes.ERROR_CODE_USER_DISABLED, body.getString(ERROR_CODE));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testForgotPassword(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.post(FORGOT_PASSWORD_ENDPOINT, new JsonObject().put(USER_NAME, "user1"),
                testContext.succeeding(httpResponse ->
                        testContext.verify(() ->
                        {
                            LOGGER.info(testInfo.getTestMethod().get().getName() + ": response: " + httpResponse.bodyAsJsonObject().encode());

                            assertEquals(HttpStatus.SC_OK, httpResponse.statusCode());

                            var body = httpResponse.bodyAsJsonObject();

                            Assertions.assertNotNull(body);

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testForgotPasswordToRestPasswordFlow(VertxTestContext testContext, TestInfo testInfo)
    {
        // expire user's old password
        Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_USER,
                new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, CONTEXT.getLong("custom.user")),
                new JsonObject().put(USER_PASSWORD_LAST_UPDATED_TIME, System.currentTimeMillis() - TimeUnit.DAYS.toMillis(365)), SYSTEM_USER, SYSTEM_REMOTE_ADDRESS, asyncResponse ->
                {
                    if (asyncResponse.succeeded())
                    {
                        UserConfigStore.getStore().updateItem(CONTEXT.getLong("custom.user")).onComplete(asyncResult ->
                                TestAPIUtil.get(USER_API_ENDPOINT + CONTEXT.getLong("custom.user"),
                                        testContext.succeeding(response ->
                                                testContext.verify(() ->
                                                {
                                                    TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

                                                    var body = response.bodyAsJsonObject().getJsonObject(RESULT);

                                                    body.put(USER_TEMPORARY_PASSWORD, UserConfigStore.getStore().getItem(CONTEXT.getLong("custom.user")).getString(USER_TEMPORARY_PASSWORD));

                                                    Assertions.assertNotNull(body);

                                                    Assertions.assertTrue(body.containsKey(USER_NAME));

                                                    Assertions.assertTrue(body.getString(USER_NAME).equalsIgnoreCase("user1"));

                                                    LOGGER.info(String.format("request body  : %s ", body));

                                                    TestAPIUtil.post(ACCESS_TOKEN_API_ENDPOINT, new JsonObject().put(USER_NAME, "user1").put(USER_PASSWORD, new CipherUtil().decrypt(body.getString(USER_TEMPORARY_PASSWORD))),
                                                            testContext.succeeding(httpResponse ->
                                                                    testContext.verify(() ->
                                                                    {
                                                                        LOGGER.info(testInfo.getTestMethod().get().getName() + ": POST response: " + response.bodyAsJsonObject().encode());

                                                                        assertEquals(HttpStatus.SC_OK, httpResponse.statusCode());

                                                                        var output = httpResponse.bodyAsJsonObject();

                                                                        Assertions.assertNotNull(output);

                                                                        Assertions.assertTrue(output.containsKey(AUTH_ACCESS_TOKEN));

                                                                        Assertions.assertTrue(output.containsKey(AUTH_REFRESH_TOKEN));

                                                                        Assertions.assertTrue(output.containsKey(USER_TEMPORARY_PASSWORD));

                                                                        Assertions.assertEquals(YES, output.getString(USER_TEMPORARY_PASSWORD));

                                                                        TestAPIUtil.put(USER_API_ENDPOINT + TestUserLogin.CONTEXT.getLong("custom.user") + "/password",
                                                                                new JsonObject().put(USER_PASSWORD, "MindUser@123")
                                                                                        .put(AUTH_REFRESH_TOKEN, output.getString(AUTH_REFRESH_TOKEN))
                                                                                        .put(User.USER_FORGOT_PASSWORD, YES),
                                                                                testContext.succeeding(result -> testContext.verify(() ->
                                                                                {
                                                                                    TestAPIUtil.assertUpdateEntityTestResult(UserConfigStore.getStore(), CONTEXT, response.bodyAsJsonObject(),
                                                                                            String.format(InfoMessageConstants.RESET_PASSWORD_SUCCEEDED, UserConfigStore.getStore().getItem(TestUserLogin.CONTEXT.getLong("custom.user")).getString(USER_NAME)), LOGGER, testInfo.getTestMethod().get().getName());

                                                                                    testContext.completeNow();
                                                                                })));

                                                                        testContext.completeNow();
                                                                    })));

                                                }))));


                    }
                    else
                    {
                        LOGGER.error(asyncResponse.cause());

                        testContext.failNow(asyncResponse.cause());
                    }
                });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    void testCreatePersonalAccessToken(VertxTestContext testContext)
    {
        var context = new JsonObject().put(PersonalAccessToken.PERSONAL_ACCESS_TOKEN_NAME, "Test LDAP User Token")
                .put(PersonalAccessToken.PERSONAL_ACCESS_TOKEN_USER, CONTEXT.getLong("ldap.user"))
                .put(PersonalAccessToken.PERSONAL_ACCESS_TOKEN_VALIDITY, "30 days")
                .put(PersonalAccessToken.PERSONAL_ACCESS_TOKEN_DESCRIPTION, "This is test token");

        TestAPIUtil.post(USER_API_ENDPOINT + "/" + CONTEXT.getLong("ldap.user") + "/generate-token", context, response ->
        {
            if (response.succeeded())
            {
                var body = response.result().bodyAsJsonObject();

                LOGGER.info(String.format("API response : %s ", body.encode()));

                Assertions.assertNotNull(response);

                Assertions.assertEquals(STATUS_SUCCEED, body.getString(STATUS));

                Assertions.assertEquals(HttpStatus.SC_OK, body.getInteger(RESPONSE_CODE));

                Assertions.assertFalse(body.getString(RESULT).isEmpty());

                TestAPIUtil.post(PERSONAL_ACCESS_TOKEN_API_ENDPOINT, context.put(PERSONAL_ACCESS_TOKEN, body.getString(RESULT)), asyncResult ->
                {
                    if (asyncResult.succeeded())
                    {
                        var result = asyncResult.result().bodyAsJsonObject();

                        LOGGER.info(String.format("API response : %s ", result.encode()));

                        Assertions.assertNotNull(result);

                        Assertions.assertEquals(STATUS_SUCCEED, result.getString(STATUS));

                        Assertions.assertEquals(HttpStatus.SC_OK, result.getInteger(RESPONSE_CODE));

                        Assertions.assertTrue(PersonalAccessTokenConfigStore.getStore().existItem(result.getLong(ID)));

                        PersonalAccessTokenConfigStore.getStore().addItem(result.getLong(ID)).onComplete(handler -> testContext.completeNow());
                    }
                    else
                    {
                        testContext.failNow(asyncResult.cause());
                    }
                });
            }
            else
            {
                testContext.failNow(response.cause());

                LOGGER.error(response.cause());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testLDAPServerDeleteFailed(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        try
        {
            TestAPIUtil.delete(LDAP_SERVER_API_ENDPOINT + "/" + LDAPServerConfigStore.getStore().getItem().getLong(ID), response ->
            {
                if (response.succeeded())
                {
                    var result = response.result().bodyAsJsonObject();

                    LOGGER.trace(String.format("received response : %s", response.result().bodyAsJsonObject()));

                    Assertions.assertTrue(result.containsKey(RESULT));

                    Assertions.assertFalse(result.getJsonObject(RESULT).isEmpty());

                    Assertions.assertTrue(result.containsKey(MESSAGE));

                    Assertions.assertFalse(result.getString(MESSAGE).isEmpty());

                    Assertions.assertTrue(result.containsKey(STATUS));

                    Assertions.assertEquals(STATUS_FAIL, result.getString(STATUS));

                    Assertions.assertTrue(result.containsKey(RESPONSE_CODE));

                    Assertions.assertEquals(HttpStatus.SC_BAD_REQUEST, result.getInteger(RESPONSE_CODE));

                    Assertions.assertTrue(result.getJsonObject(RESULT).containsKey(Entity.PERSONAL_ACCESS_TOKEN.getName()));

                    Assertions.assertFalse(result.getJsonObject(RESULT).getJsonArray(Entity.PERSONAL_ACCESS_TOKEN.getName()).isEmpty());

                    testContext.completeNow();
                }
                else
                {
                    LOGGER.info(response.cause());

                    testContext.failNow(response.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.warn(exception);

            testContext.failNow(exception);
        }
    }
}
