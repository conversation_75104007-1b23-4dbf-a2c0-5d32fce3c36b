/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.TestUtil;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.UserRoleConfigStore;
import com.mindarray.util.*;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.netty.handler.codec.http.HttpHeaderNames;
import io.vertx.core.MultiMap;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.http.*;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.auth.JWTOptions;
import io.vertx.ext.auth.PubSecKeyOptions;
import io.vertx.ext.auth.jwt.JWTAuth;
import io.vertx.ext.auth.jwt.JWTAuthOptions;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.OAUTH_CALLBACK_ENDPOINT;
import static com.mindarray.TestAPIConstants.OBJECT_API_ENDPOINT;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.EVENT_TYPE;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Execution(ExecutionMode.SAME_THREAD)
@Timeout(90 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public
class TestAPIServer
{
    private static final Logger LOGGER = new Logger(TestAPIServer.class, "Test API Server", "Test API Server");
    private static MessageConsumer<JsonObject> messageConsumer;

    private static JWTAuth jwtAuth;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_CONFIG_QUERY, new JsonObject().put(User.USER_NAME, "admin").put(SESSION_ID, TestUtil.getSessionId()));

        var publicKeyBuffer = TestUtil.vertx().fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "public-key.pem");

        var secretKeyBuffer = TestUtil.vertx().fileSystem().readFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "server-key.pem");

        jwtAuth = JWTAuth.create(TestUtil.vertx(), new JWTAuthOptions()
                .addPubSecKey(new PubSecKeyOptions().setAlgorithm(ALGO_RS512).setBuffer(publicKeyBuffer))
                .addPubSecKey(new PubSecKeyOptions().setAlgorithm(ALGO_RS512).setBuffer(secretKeyBuffer)));

        testContext.completeNow();
    }

    @AfterAll
    static void cleanUp(VertxTestContext testContext)
    {
        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running test case %s", testInfo.getTestMethod().get().getName()));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testConfigInitEvent(VertxTestContext testContext)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_USER + TestUtil.getSessionId(), message ->
        {
            if (message.body().containsKey(EventBusConstants.EVENT_TYPE) && message.body().getString(EventBusConstants.EVENT_TYPE).equalsIgnoreCase(EventBusConstants.EVENT_CONFIG_QUERY))
            {
                Assertions.assertNotNull(message.body());

                Assertions.assertFalse(message.body().isEmpty());

                Assertions.assertTrue(message.body().containsKey(EventBusConstants.EVENT_COMPRESSION_TYPE));

                Assertions.assertEquals((byte) 1, (byte) message.body().getValue(EventBusConstants.EVENT_COMPRESSION_TYPE));

                try
                {
                    var eventContext = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                    Assertions.assertNotNull(eventContext);

                    Assertions.assertFalse(eventContext.isEmpty());

                    Assertions.assertNotNull(eventContext.getJsonArray(AIOpsObject.OBJECT_TYPE));

                    Assertions.assertFalse(eventContext.getJsonArray(AIOpsObject.OBJECT_TYPE).isEmpty());

                    Assertions.assertNotNull(eventContext.getJsonArray(Metric.METRIC_TYPE));

                    Assertions.assertFalse(eventContext.getJsonArray(Metric.METRIC_TYPE).isEmpty());

                    Assertions.assertNotNull(eventContext.getJsonArray(NMSConstants.OBJECTS));

                    Assertions.assertFalse(eventContext.getJsonArray(NMSConstants.OBJECTS).isEmpty());

                    var objects = eventContext.getJsonArray(NMSConstants.OBJECTS);

                    var valid = false;

                    var tags = false;

                    for (var index = 0; index < objects.size(); index++)
                    {
                        var object = objects.getJsonObject(index);

                        if (object.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVER.getName()) && object.getJsonArray(NMSConstants.APPS) != null)
                        {
                            valid = true;
                        }

                        if (object.containsKey(AIOpsObject.OBJECT_IP) && object.getString(AIOpsObject.OBJECT_IP).equalsIgnoreCase("fd00:1:1:1::132") && object.getValue(AIOpsObject.OBJECT_TAGS) != null)
                        {
                            tags = true;
                        }

                        if (object.containsKey(AIOpsObject.OBJECT_IP) && object.getString(AIOpsObject.OBJECT_IP).equalsIgnoreCase("************") && object.getValue(AIOpsObject.OBJECT_TAGS) != null)
                        {
                            tags = true;
                        }
                    }

                    messageConsumer.unregister();

                    if (!valid)
                    {
                        testContext.failNow(new Exception("failed to find application on config db init event"));
                    }
                    else if (!tags)
                    {
                        testContext.failNow(new Exception("failed to find tags on config db init event"));
                    }
                    else
                    {
                        testContext.completeNow();
                    }
                }
                catch (Exception exception)
                {
                    messageConsumer.unregister();

                    testContext.failNow(exception);
                }
            }
        });

        var context = new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_CONFIG_QUERY)
                .put(APIConstants.SESSION_ID, TestUtil.getSessionId()).put(User.USER_NAME, "admin")
                .put(EventBusConstants.EVENT_CONTEXT, new JsonObject().put(APIConstants.SESSION_ID, TestUtil.getSessionId()));

        TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_SERVER, context);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testStaticResourceHandler(VertxTestContext testContext)
    {
        TestAPIUtil.get("/samples/custom-script.py", testContext.succeeding(response ->
        {
            Assertions.assertEquals(HttpStatus.SC_OK, response.statusCode());

            testContext.completeNow();
        }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testStaticResourceHandlerWithInvalidFileType(VertxTestContext testContext)
    {
        TestAPIUtil.get("/samples/custom-scriptsss.py", testContext.succeeding(response ->
        {
            Assertions.assertEquals(HttpStatus.SC_BAD_REQUEST, response.statusCode());

            var body = response.bodyAsJsonObject();

            Assertions.assertNotNull(body);

            Assertions.assertFalse(body.isEmpty());

            Assertions.assertEquals("File samples/custom-scriptsss.py not found", body.getString(GlobalConstants.MESSAGE));

            Assertions.assertEquals(HttpStatus.SC_BAD_REQUEST, body.getInteger(APIConstants.RESPONSE_CODE));

            Assertions.assertEquals(GlobalConstants.STATUS_FAIL, body.getString(GlobalConstants.STATUS));

            testContext.completeNow();
        }));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testSocketConnection(VertxTestContext testContext)
    {
        var client = TestUtil.vertx().createHttpClient(new HttpClientOptions().setTrustAll(true)
                .setSsl(true).setVerifyHost(false).setUseAlpn(true));

        var multiMap = MultiMap.caseInsensitiveMultiMap()
                .set(HttpHeaders.AUTHORIZATION.toString(), TestUtil.getAccessToken())
                .set(CommonUtil.getString(HttpHeaderNames.COOKIE), Cookie.cookie(CLIENT_ID, UI_CLIENT_ID).encode());

        client.webSocket(new WebSocketConnectOptions().setPort(MotadataConfigUtil.getHTTPServerPort(GlobalConstants.BootstrapType.APP.name()))
                .setSsl(true).setHeaders(multiMap).setURI("/eventbus/websocket"), asyncResult ->
        {
            if (asyncResult.succeeded())
            {
                var promise = Promise.<Void>promise();

                LOGGER.info("handler succeeded");

                asyncResult.result().writeFrame(WebSocketFrame.textFrame(new JsonObject()
                        .put("type", "send").put("headers", new JsonObject())
                        .put("address", EventBusConstants.EVENT_SERVER).put("body", new JsonObject().put(EventBusConstants.EVENT_TYPE, EventBusConstants.UI_NOTIFICATION_AGENT)
                                .put(EventBusConstants.EVENT_CONTEXT, new JsonObject())).encode(), true), sender ->
                {
                    if (sender.failed())
                    {
                        LOGGER.warn(sender.cause());

                        promise.fail(sender.cause());

                        testContext.failNow(sender.cause());
                    }
                    else
                    {
                        LOGGER.info("Completing sendPromise.");

                        promise.complete();
                    }
                });

                promise.future().onComplete(sender ->
                {
                    try
                    {
                        if (sender.succeeded())
                        {
                            asyncResult.result().writeFrame(WebSocketFrame.pingFrame(Buffer.buffer(new JsonObject().put("type", "ping").encode())));

                            asyncResult.result().writeFrame(WebSocketFrame.textFrame(new JsonObject()
                                    .put("type", "register").put("headers", new JsonObject())
                                    .put("address", EventBusConstants.EVENT_USER + TestUtil.getSessionId()).encode(), true));

                            try
                            {
                                testContext.awaitCompletion(10, TimeUnit.SECONDS);
                            }
                            catch (Exception exception)
                            {
                                exception.printStackTrace();
                            }

                            var fileName = GlobalConstants.CURRENT_DIR + PATH_SEPARATOR + "logs" + PATH_SEPARATOR + "api" +
                                    PATH_SEPARATOR + "$$$-API Server.log".replace("$$$", new SimpleDateFormat("dd-MMMM-yyyy HH").format(new Date()));

                            String buffer;

                            if (!TestUtil.vertx().fileSystem().existsBlocking(fileName))
                            {
                                var time = CommonUtil.getInteger(new SimpleDateFormat("HH").format(new Date()));

                                if (time > 0)
                                {
                                    time = time - 1;
                                }
                                else if (time == 0)
                                {
                                    time = 23;
                                }

                                fileName = GlobalConstants.CURRENT_DIR + PATH_SEPARATOR + "logs" + PATH_SEPARATOR + "api" +
                                        PATH_SEPARATOR + "$$$ @@@-API Server.log".replace("@@@", time >= 0 && time <= 9 ? "0" + CommonUtil.getString(time) : CommonUtil.getString(time)).replace("$$$", new SimpleDateFormat("dd-MMMM-yyyy").format(new Date()));
                            }

                            LOGGER.info("File name: " + fileName);

                            buffer = CommonUtil.getString(TestUtil.vertx().fileSystem().readFileBlocking(fileName));

                            LOGGER.info("does file have content: " + !buffer.isEmpty());

                            LOGGER.info("socket create - contains: " + buffer.contains("socket created"));

                            LOGGER.info("client registered - contains: " + buffer.contains("client registered"));

                            LOGGER.info("received from - contains: " + buffer.contains("received from"));

                            LOGGER.info("buffer: " + buffer);

                            Assertions.assertNotNull(buffer);

                            Assertions.assertTrue(buffer.contains("socket created"));

                            Assertions.assertTrue(buffer.contains("client registered"));

                            Assertions.assertTrue(buffer.contains("received from"));

                            asyncResult.result().close(result ->
                            {
                                if (result.failed())
                                {
                                    testContext.failNow(result.cause());
                                }
                                else
                                {
                                    testContext.completeNow();
                                }
                            });
                        }
                        else
                        {
                            LOGGER.warn("sender.cause: " + sender.cause());
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        testContext.failNow(exception.getMessage());
                    }
                });
            }
        });
    }

    @Test
    @Order(5)
    void testAccessAPIInvalidToken(VertxTestContext testContext) throws Exception
    {
        var token = jwtAuth.generateToken(
                new JsonObject().put(TOKEN_TYPE, TokenType.PERSONAL_ACCESS_TOKEN.getName()).put(USER_NAME, "admin").put(GlobalConstants.ID, DEFAULT_ID).put(USER_PERMISSIONS,
                        UserRoleConfigStore.getStore().getItem(10000000000002L)//default read-only role
                                .getJsonArray(UserRole.USER_ROLE_CONTEXT).add("user:" + RequestType.POST.getName()).add("token:" + RequestType.POST.getName())),
                new JWTOptions()
                        .setAlgorithm(ALGO_RS512)
                        .setSubject(API_VERSION)
                        .setIssuer(API_AUTHOR).setExpiresInSeconds(10));

        testContext.awaitCompletion(15, TimeUnit.SECONDS);

        TestAPIUtil.get(OBJECT_API_ENDPOINT + "?" + UserRole.ADMIN_ROLE + "=" + YES, new JsonObject().put(HttpHeaders.AUTHORIZATION.toString(), "Bearer " + token), result ->
        {
            if (result.succeeded())
            {
                Assertions.assertEquals(HttpStatus.SC_UNAUTHORIZED, result.result().statusCode());

                Assertions.assertEquals("Unauthorized", result.result().statusMessage());

                testContext.completeNow();
            }
            else
            {
                testContext.failNow(result.cause());
            }
        });
    }

    @Test
    @Order(5)
    void testOAuthCallbackInvalid(VertxTestContext testContext) throws Exception
    {

        TestAPIUtil.get(OAUTH_CALLBACK_ENDPOINT + "?code=RANDOM_STRING&state=" + new JsonObject().put(SESSION_ID, "dummy-session-id").put(EVENT_TYPE, EventBusConstants.UI_ACTION_CREDENTIAL_PROFILE_TEST).encode(), new JsonObject(), result ->
        {
            if (result.succeeded())
            {
                Assertions.assertEquals(HttpStatus.SC_OK, result.result().statusCode());

                Assertions.assertEquals(OAuthUtil.HTML_CLOSE_SCRIPT, result.result().bodyAsString());

                testContext.completeNow();
            }
            else
            {
                testContext.failNow(result.cause());
            }
        });
    }

}
