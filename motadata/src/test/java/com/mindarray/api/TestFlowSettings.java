/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.GlobalConstants;
import com.mindarray.TestAPIConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.TestUtil;
import com.mindarray.flow.FlowEngineConstants;
import com.mindarray.store.FlowSettingsConfigStore;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.MethodOrderer;
import org.junit.jupiter.api.Order;
import org.junit.jupiter.api.TestMethodOrder;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Timeout(70 * 1000)
@Execution(ExecutionMode.SAME_THREAD)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestFlowSettings
{
    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    public void testGetFlowSettings(VertxTestContext testContext) throws InterruptedException
    {

        TestAPIUtil.get(TestAPIConstants.FLOW_SETTINGS_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {

            Assertions.assertEquals(200, response.statusCode());

            Assertions.assertTrue(FlowEngineConstants.INGRESS.equalsIgnoreCase(response.bodyAsJsonObject().getJsonObject(GlobalConstants.RESULT).getString(FlowSettings.FLOW_SETTINGS_SFLOW_DIRECTION)), "default direction should be ingress");

            testContext.completeNow();
        })));

        Assertions.assertTrue(testContext.awaitCompletion(10, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    public void testUpdateFlowSettings(VertxTestContext testContext) throws InterruptedException
    {
        var flowProcesses = new AtomicInteger(0);

        TestUtil.vertx().eventBus().<JsonArray>localConsumer("test.flow.config", message ->
        {

            if (flowProcesses.incrementAndGet() == 4)
            {
                testContext.completeNow();
            }
        });

        var context = new JsonObject().put(FlowSettings.FLOW_SETTINGS_SFLOW_DIRECTION, FlowEngineConstants.EGRESS)
                .put(FlowSettings.FLOW_SETTINGS_SFLOW_PORT, 6343)
                .put(FlowSettings.FLOW_SETTINGS_NETFLOW_PORT, 2055)
                .put(FlowSettings.FLOW_SETTINGS_AGGREGATION_INTERVAL_MINUTES, 60)
                .put(FlowSettings.FLOW_SETTINGS_BGP_FLOW_ENABLED, GlobalConstants.YES)
                .put(FlowSettings.FLOW_SETTINGS_BGP_SFLOW_PORT, 6343)
                .put(FlowSettings.FLOW_SETTINGS_BGP_NETFLOW_PORT, 2056);

        TestAPIUtil.put(TestAPIConstants.FLOW_SETTINGS_API_ENDPOINT, context, testContext.succeeding(response -> testContext.verify(() ->
        {

            Assertions.assertEquals(200, response.statusCode());

            Assertions.assertTrue(GlobalConstants.STATUS_SUCCEED.equalsIgnoreCase(response.bodyAsJsonObject().getString(GlobalConstants.STATUS)));

            Assertions.assertTrue(FlowEngineConstants.EGRESS.equalsIgnoreCase(FlowSettingsConfigStore.getStore().getItem().getString(FlowSettings.FLOW_SETTINGS_SFLOW_DIRECTION)));

        })));

        Assertions.assertTrue(testContext.awaitCompletion(60, TimeUnit.SECONDS), "could not complete the task, reason: timed out.");
    }
}