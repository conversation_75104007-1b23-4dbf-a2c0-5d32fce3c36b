/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.AgentConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.apache.http.HttpStatus;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.eventbus.EventBusConstants.*;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Execution(ExecutionMode.SAME_THREAD)
@Timeout(90 * 1000)
@EnabledIfSystemProperty(named = "system.startup.mode", matches = "^APP|^MASTER$")
public class TestAPIAgent
{
    private static final Logger LOGGER = new Logger(TestAPIAgent.class, "Test API Agent", "Test API Agent");

    @BeforeEach
    void beforeEach(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info("running test case: " + testInfo.getTestMethod().get().getName());

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testRegisterAgent(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject()
                .put(Agent.AGENT_VERSION, "8.0.5")
                .put(AIOpsObject.OBJECT_IP, "************")
                .put("objects", new JsonArray().add("************"))
                .put(Agent.AGENT_OS_NAME, "linux")
                .put(Agent.AGENT_UUID, "nikunj")
                .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_AGENT_REGISTRATION)
                .put(AIOpsObject.OBJECT_HOST, "nikunj-Latitude-3420")
                .put(Agent.AGENT_CONFIGS, "{\"agent\":{\"system.log.level\":0,\"agent.id\":\"nikunj\",\"agent.deactivation.status\":\"no\",\"agent.log.retention.days\":1,\"agent.max.event.backlog.queue.size\":100000,\"metric.agent.status\":\"yes\",\"log.agent.status\":\"yes\",\"packet.agent.status\":\"yes\",\"motadata.manager.event.publisher.port\":9441,\"motadata.manager.event.subscriber.port\":9440,\"event.publisher.host\":\"127.0.0.1\",\"event.subscriber.host\":\"127.0.0.1\",\"event.publisher.port\":9449,\"event.subscriber.port\":9444,\"cache.flush.timer.seconds\":30,\"cache.file.max.size.threshold.mb\":1024,\"agent.health.inspection.timer.seconds\":1,\"agent.health.window.count\":1,\"metric.agent.memory.warning.threshold.mb\":1,\"metric.agent.memory.critical.threshold.mb\":1,\"log.agent.memory.warning.threshold.mb\":10,\"log.agent.memory.critical.threshold.mb\":20,\"packet.agent.memory.warning.threshold.mb\":10,\"packet.agent.memory.critical.threshold.mb\":20,\"metric.agent.cpu.warning.percent\":1,\"metric.agent.cpu.critical.percent\":1,\"log.agent.cpu.warning.percent\":50,\"log.agent.cpu.critical.percent\":70,\"packet.agent.cpu.warning.percent\":10,\"packet.agent.cpu.critical.percent\":20,\"http.server.port\":8443,\"agent.state\":\"ENABLE\",\"agent.process.detection.attempts\":90},\"log.agent\":{\"system.log.level\":0,\"multiline.log.status\":\"yes\",\"event.log.source.status\":\"yes\",\"log.filters\":[],\"watcher.create.event\":\"yes\",\"multiline.log.files\":[],\"log.dirs\":[],\"event.log.sources\":[{\"name\":\"System\",\"levels\":[0,1,2,3,4,5],\"events\":[]},{\"name\":\"Application\",\"levels\":[1,2,3,4,5],\"events\":[]},{\"name\":\"Security\",\"levels\":[0,1,2,3,4,5],\"events\":[1102,4624,4625,4634,4648,4657,4673,4688,4697,4719,4720,4722,4723,4724,4725,4726,4728,4732,4735,4737,4738,4740,4755,4756,5025]}],\"log.position.write.timer.seconds\":5,\"cache.flush.timer.seconds\":5,\"read.existing.events\":\"yes\",\"max.workers\":2,\"cache.max.size.bytes\":10240,\"ignore.invalid.log.file\":\"yes\",\"worker.max.queue.size\":50,\"worker.max.page.size.bytes\":4096},\"metric.agent\":{\"system.log.level\":0,\"cache.flush.timer.seconds\":10,\"cpu.memory.metric.poll.timer.seconds\":10,\"cpu.memory.metric.status\":\"yes\",\"system.info.metric.poll.timer.seconds\":300,\"system.info.metric.status\":\"yes\",\"system.load.metric.poll.timer.seconds\":10,\"system.load.metric.status\":\"yes\",\"cpu.core.metric.poll.timer.seconds\":10,\"process.metric.poll.timer.seconds\":60,\"disk.metric.poll.timer.seconds\":60,\"network.metric.poll.timer.seconds\":60,\"service.metric.poll.timer.seconds\":60,\"cpu.core.metric.status\":\"no\",\"system.metric.status\":\"yes\",\"process.metric.status\":\"yes\",\"process.connection.status\":\"yes\",\"disk.metric.status\":\"yes\",\"network.metric.status\":\"yes\",\"service.metric.status\":\"yes\",\"processes\":[],\"services\":[],\"disks\":[],\"interfaces\":[],\"agent.business.hour.profile\":{\"business.hour.name\":\"Default Monitor Hour Profile - Motadata\",\"business.hour.context\":{\"Sunday\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],\"Monday\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],\"Tuesday\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],\"Wednesday\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],\"Thursday\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],\"Friday\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],\"Saturday\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23]}}},\"packet.agent\":{\"applications\":[]}}")
                .put(GlobalConstants.DURATION, DateTimeUtil.currentSeconds());

        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_AGENT, context);

        context = new JsonObject()
                .put(Agent.AGENT_VERSION, "8.0.5")
                .put(AIOpsObject.OBJECT_IP, "************")
                .put("objects", new JsonArray().add("************"))
                .put(Agent.AGENT_OS_NAME, "linux")
                .put(Agent.AGENT_UUID, "nikunj1")
                .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_AGENT_REGISTRATION)
                .put(AIOpsObject.OBJECT_HOST, "nikunj1-Latitude-3420")
                .put(Agent.AGENT_CONFIGS, "{\"agent\":{\"system.log.level\":0,\"agent.id\":\"nikunj1\",\"agent.deactivation.status\":\"no\",\"agent.log.retention.days\":1,\"agent.max.event.backlog.queue.size\":100000,\"metric.agent.status\":\"yes\",\"log.agent.status\":\"yes\",\"packet.agent.status\":\"yes\",\"motadata.manager.event.publisher.port\":9441,\"motadata.manager.event.subscriber.port\":9440,\"event.publisher.host\":\"127.0.0.1\",\"event.subscriber.host\":\"127.0.0.1\",\"event.publisher.port\":9449,\"event.subscriber.port\":9444,\"cache.flush.timer.seconds\":30,\"cache.file.max.size.threshold.mb\":1024,\"agent.health.inspection.timer.seconds\":1,\"agent.health.window.count\":1,\"metric.agent.memory.warning.threshold.mb\":1,\"metric.agent.memory.critical.threshold.mb\":1,\"log.agent.memory.warning.threshold.mb\":10,\"log.agent.memory.critical.threshold.mb\":20,\"packet.agent.memory.warning.threshold.mb\":10,\"packet.agent.memory.critical.threshold.mb\":20,\"metric.agent.cpu.warning.percent\":1,\"metric.agent.cpu.critical.percent\":1,\"log.agent.cpu.warning.percent\":50,\"log.agent.cpu.critical.percent\":70,\"packet.agent.cpu.warning.percent\":10,\"packet.agent.cpu.critical.percent\":20,\"http.server.port\":8443,\"agent.state\":\"ENABLE\",\"agent.process.detection.attempts\":90},\"log.agent\":{\"system.log.level\":0,\"multiline.log.status\":\"yes\",\"event.log.source.status\":\"yes\",\"log.filters\":[],\"watcher.create.event\":\"yes\",\"multiline.log.files\":[],\"log.dirs\":[],\"event.log.sources\":[{\"name\":\"System\",\"levels\":[0,1,2,3,4,5],\"events\":[]},{\"name\":\"Application\",\"levels\":[1,2,3,4,5],\"events\":[]},{\"name\":\"Security\",\"levels\":[0,1,2,3,4,5],\"events\":[1102,4624,4625,4634,4648,4657,4673,4688,4697,4719,4720,4722,4723,4724,4725,4726,4728,4732,4735,4737,4738,4740,4755,4756,5025]}],\"log.position.write.timer.seconds\":5,\"cache.flush.timer.seconds\":5,\"read.existing.events\":\"yes\",\"max.workers\":2,\"cache.max.size.bytes\":10240,\"ignore.invalid.log.file\":\"yes\",\"worker.max.queue.size\":50,\"worker.max.page.size.bytes\":4096},\"metric.agent\":{\"system.log.level\":0,\"cache.flush.timer.seconds\":10,\"cpu.memory.metric.poll.timer.seconds\":10,\"cpu.memory.metric.status\":\"yes\",\"system.info.metric.poll.timer.seconds\":300,\"system.info.metric.status\":\"yes\",\"system.load.metric.poll.timer.seconds\":10,\"system.load.metric.status\":\"yes\",\"cpu.core.metric.poll.timer.seconds\":10,\"process.metric.poll.timer.seconds\":60,\"disk.metric.poll.timer.seconds\":60,\"network.metric.poll.timer.seconds\":60,\"service.metric.poll.timer.seconds\":60,\"cpu.core.metric.status\":\"no\",\"system.metric.status\":\"yes\",\"process.metric.status\":\"yes\",\"process.connection.status\":\"yes\",\"disk.metric.status\":\"yes\",\"network.metric.status\":\"yes\",\"service.metric.status\":\"yes\",\"processes\":[],\"services\":[],\"disks\":[],\"interfaces\":[],\"agent.business.hour.profile\":{\"business.hour.name\":\"Default Monitor Hour Profile - Motadata\",\"business.hour.context\":{\"Sunday\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],\"Monday\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],\"Tuesday\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],\"Wednesday\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],\"Thursday\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],\"Friday\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23],\"Saturday\":[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23]}}},\"packet.agent\":{\"applications\":[]}}")
                .put(GlobalConstants.DURATION, DateTimeUtil.currentSeconds());

        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_AGENT, context);

        TestUtil.vertx().setTimer(20 * 1000, handler ->
        {
            LOGGER.info(testInfo.getTestMethod().get().getName() + ": Checking if agent is registered or not.");

            LOGGER.info(testInfo.getTestMethod().get().getName() + ": " + AgentConfigStore.getStore().getItems().encode());

            assertNotNull(ObjectConfigStore.getStore().getItemByAgentId(AgentConfigStore.getStore().getAgentId("nikunj")));

            assertNotNull(ObjectConfigStore.getStore().getItemByAgentId(AgentConfigStore.getStore().getAgentId("nikunj1")));

            testContext.completeNow();
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testUpdateAgentTags(VertxTestContext testContext, TestInfo testInfo)
    {
        var ids = AgentConfigStore.getStore().getIds();

        var context = new JsonObject()
                .put(APIConstants.REQUEST_PARAM_IDS, ids)
                .put(AIOpsObject.OBJECT_TAGS, new JsonArray().add("test").add("cases"));

        TestAPIUtil.post(TestAPIConstants.AGENT_API_ENDPOINT + "update", context, result ->
        {
            if (result.succeeded())
            {
                var items = AgentConfigStore.getStore().getItems();

                LOGGER.info(testInfo.getTestMethod().get().getName() + ": Agents: " + items.encode());

                for (var index = 0; index < items.size(); index++)
                {
                    var item = items.getJsonObject(index);

                    assertTrue(item.getJsonArray(AIOpsObject.OBJECT_TAGS).contains("test") && item.getJsonArray(AIOpsObject.OBJECT_TAGS).contains("cases"));

                    testContext.completeNow();
                }
            }
            else
            {
                LOGGER.warn(testInfo.getTestMethod().get().getName() + ": " + result.cause().getMessage());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testUpdateAgentGroups(VertxTestContext testContext, TestInfo testInfo)
    {
        var ids = AgentConfigStore.getStore().getIds();

        var context = new JsonObject()
                .put(APIConstants.REQUEST_PARAM_IDS, ids)
                .put(AIOpsObject.OBJECT_GROUPS, new JsonArray().add(10000000000002L).add(10000000000003L).add(10000000000004L));

        TestAPIUtil.post(TestAPIConstants.AGENT_API_ENDPOINT + "update", context, result ->
        {
            if (result.succeeded())
            {
                var items = AgentConfigStore.getStore().getItems();

                LOGGER.info(testInfo.getTestMethod().get().getName() + ": Agents: " + items.encode());

                for (var index = 0; index < items.size(); index++)
                {
                    var item = items.getJsonObject(index);

                    assertTrue(item.getJsonArray(AIOpsObject.OBJECT_GROUPS).contains(10000000000002L) && item.getJsonArray(AIOpsObject.OBJECT_GROUPS).contains(10000000000003L) && item.getJsonArray(AIOpsObject.OBJECT_GROUPS).contains(10000000000004L));

                    testContext.completeNow();
                }
            }
            else
            {
                LOGGER.warn(testInfo.getTestMethod().get().getName() + ": " + result.cause().getMessage());
            }
        });
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testDeleteAllAgents(VertxTestContext testContext, TestInfo testInfo)
    {
        TestUtil.vertx().eventBus().send(EVENT_AGENT, new JsonObject()
                .put(EVENT_TYPE, EVENT_AGENT_HEARTBEAT)
                .put(AIOpsObject.OBJECT_IP, "************")
                .put(GlobalConstants.DURATION, DateTimeUtil.currentSeconds())
                .put(Agent.AGENT_UUID, "nikunj")
                .put(EVENT_COPY_REQUIRED, false));

        TestUtil.vertx().eventBus().send(EVENT_AGENT, new JsonObject()
                .put(EVENT_TYPE, EVENT_AGENT_HEARTBEAT)
                .put(AIOpsObject.OBJECT_IP, "************")
                .put(GlobalConstants.DURATION, DateTimeUtil.currentSeconds())
                .put(Agent.AGENT_UUID, "nikunj1")
                .put(EVENT_COPY_REQUIRED, false));

        var ids = AgentConfigStore.getStore().getIds();

        var context = new JsonObject()
                .put(APIConstants.REQUEST_PARAM_IDS, ids);

        TestUtil.vertx().setTimer(3 * 1000, timer ->
                TestAPIUtil.deleteAll(TestAPIConstants.AGENT_API_ENDPOINT, context, response ->
                {
                    if (response.succeeded())
                    {
                        var result = response.result().bodyAsJsonObject();

                        LOGGER.info(testInfo.getTestMethod().get().getName() + ": result: " + result.encode());

                        assertEquals(HttpStatus.SC_OK, result.getInteger(RESPONSE_CODE));

                        assertTrue(result.getString(GlobalConstants.STATUS).equalsIgnoreCase(GlobalConstants.STATUS_SUCCEED));

                        assertTrue(result.getString(GlobalConstants.MESSAGE).equalsIgnoreCase("Agent deleted successfully"));

                        testContext.completeNow();
                    }
                    else
                    {
                        LOGGER.warn(testInfo.getTestMethod().get().getName() + ": " + response.cause().getMessage());
                    }
                }));
    }
}
