/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.ErrorMessageConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.store.ProtocolMapperConfigStore;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import static com.mindarray.GlobalConstants.ID;
import static com.mindarray.GlobalConstants.MOTADATA_API;
import static com.mindarray.TestAPIConstants.PROTOCOL_MAPPER_API_ENDPOINT;

@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
@Execution(ExecutionMode.SAME_THREAD)
public class TestProtocolMapper
{
    private static final JsonObject CONTEXT = new JsonObject();

    private static final Logger LOGGER = new Logger(TestProtocolMapper.class, MOTADATA_API, "Test Protocol Mapper");

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testGetAllProtocolMapper(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(PROTOCOL_MAPPER_API_ENDPOINT, testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertValidResponseTestResult(response, LOGGER, testInfo.getTestMethod().get().getName());

            testContext.completeNow();

        })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testCreateProtocolMapper(VertxTestContext testContext, TestInfo testInfo)
    {
        var context = new JsonObject().put(ProtocolMapper.PROTOCOL_MAPPER_NAME, "SSH").put(ProtocolMapper.PROTOCOL_MAPPER_NUMBER, 22);

        TestAPIUtil.post(PROTOCOL_MAPPER_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertCreateEntityTestResult(ProtocolMapperConfigStore.getStore(), context, response.bodyAsJsonObject(),
                                    String.format(InfoMessageConstants.ENTITY_CREATED, "Protocol Mapper"), null, LOGGER, testInfo.getTestMethod().get().getName());

                            TestProtocolMapper.CONTEXT.put("protocol.mapper", response.bodyAsJsonObject().getLong(ID));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testCreateDuplicateProtocolMapper(VertxTestContext testContext)
    {
        var context = new JsonObject()
                .put(ProtocolMapper.PROTOCOL_MAPPER_NAME, "SSH").put(ProtocolMapper.PROTOCOL_MAPPER_NUMBER, 22);

        TestAPIUtil.post(PROTOCOL_MAPPER_API_ENDPOINT, context,
                testContext.succeeding(response ->
                        testContext.verify(() ->
                        {
                            TestAPIUtil.assertAlreadyExistedEntityTestResult(response.bodyAsJsonObject(), String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, "Protocol Name"),
                                    ProtocolMapperConfigStore.getStore(), ProtocolMapper.PROTOCOL_MAPPER_NAME, context.getString(ProtocolMapper.PROTOCOL_MAPPER_NAME));

                            testContext.completeNow();
                        })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testGetProtocolMapper(VertxTestContext testContext, TestInfo testInfo)
    {
        TestAPIUtil.get(PROTOCOL_MAPPER_API_ENDPOINT + CONTEXT.getLong("protocol.mapper"), testContext.succeeding(response ->
                testContext.verify(() ->
                {
                    TestAPIUtil.assertGETRequestTestResult(response, CONTEXT.getLong("protocol.mapper"), ProtocolMapperConfigStore.getStore(), null, LOGGER, testInfo.getTestMethod().get().getName());

                    testContext.completeNow();

                })));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testGetDeletedProtocolMapper(VertxTestContext testContext)
    {
        var item = ProtocolMapperConfigStore.getStore().getItem(CONTEXT.getLong("protocol.mapper"));

        Assertions.assertNotNull(item);

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testDeleteProtocolMapper(VertxTestContext testContext)
    {
        TestAPIUtil.delete(PROTOCOL_MAPPER_API_ENDPOINT + CONTEXT.getLong("protocol.mapper"), testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertDeleteEntityTestResult(ProtocolMapperConfigStore.getStore(), response.bodyAsJsonObject(),
                    String.format(InfoMessageConstants.ENTITY_DELETED, "Protocol Mapper"));

            testContext.completeNow();
        })));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testDeleteProtocolMapperNotExist(VertxTestContext testContext)
    {
        TestAPIUtil.delete(PROTOCOL_MAPPER_API_ENDPOINT + CONTEXT.getLong("protocol.mapper"), testContext.succeeding(response -> testContext.verify(() ->
        {
            TestAPIUtil.assertNotExistEntityDeleteTestResult(response, "Protocol Mapper", String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, "Protocol Mapper"));

            testContext.completeNow();
        })));
    }


}
