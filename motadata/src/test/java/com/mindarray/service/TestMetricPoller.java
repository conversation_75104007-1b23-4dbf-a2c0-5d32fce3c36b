/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.service;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.TestNMSUtil;
import com.mindarray.TestUtil;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Metric;
import com.mindarray.api.RemoteEventProcessor;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.MetricCacheStore;
import com.mindarray.store.MetricConfigStore;
import com.mindarray.store.RemoteEventProcessorConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.HealthUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.ID;
import static com.mindarray.GlobalConstants.MESSAGE;


@ExtendWith(VertxExtension.class)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@Execution(ExecutionMode.SAME_THREAD)
@Timeout(80 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")

public class TestMetricPoller
{
    private static final Logger LOGGER = new Logger(TestMetricPoller.class, GlobalConstants.MOTADATA_NMS, "Metric Poller Test");
    private static final AtomicBoolean VALID = new AtomicBoolean(false);
    private static MessageConsumer<JsonObject> messageConsumer;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        var deploymentId = Bootstrap.getDeployedVerticles().get(HealthUtil.class.getSimpleName());

        Assertions.assertNotNull(deploymentId);

        // to avoid system generated timer statistics
        Bootstrap.vertx().undeploy(deploymentId, asyncResult ->
        {
            var futures = new ArrayList<Future<Void>>();

            var metricIds = MetricConfigStore.getStore().getIds();

            for (var id : metricIds)
            {
                var promise = Promise.<Void>promise();

                futures.add(promise.future());

                TestUtil.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_METRIC_DISABLE,
                        new JsonObject().put(ID, CommonUtil.getLong(id)), reply -> promise.complete());
            }

            Future.join(futures).onComplete(result ->
            {
                MotadataConfigUtil.loadConfigs(new JsonObject().put("event.acknowledge.timeout", 10000).put("metric.poller.batch.size", 5).put("metric.poller.batch.timeout", 30));

                testContext.completeNow();
            });
        });
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        MotadataConfigUtil.loadConfigs(new JsonObject().put("event.acknowledge.timeout", 10000).put("metric.poller.batch.size", 50).put("metric.poller.batch.timeout", 60));

        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @BeforeEach
    void beforeEach(VertxTestContext testContext)
    {
        VALID.set(false);

        if (messageConsumer != null)
        {
            messageConsumer.unregister(result -> testContext.completeNow());
        }
        else
        {
            testContext.completeNow();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testSendServerEventBatchSize2(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var pollingCategory = NMSConstants.Category.SERVER;

        var context = new JsonObject()
                .put(Metric.METRIC_CATEGORY, pollingCategory.getName()).put("idle.metric.workers", NMSConstants.getPollerWorkersByCategory(pollingCategory))
                .put(EventBusConstants.EVENT_CONTEXT, new JsonObject());

        assertMetricEventTestResult(context, pollingCategory, NMSConstants.getPollerWorkersByCategory(pollingCategory) - 2, 10, testContext, false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testSendOverloadedNetworkEvents(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(1, TimeUnit.SECONDS);

        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var pollingCategory = NMSConstants.Category.NETWORK;

        var context = new JsonObject()
                .put(Metric.METRIC_CATEGORY, pollingCategory.getName()).put("idle.metric.workers", NMSConstants.getPollerWorkersByCategory(pollingCategory))
                .put(EventBusConstants.EVENT_CONTEXT, new JsonObject());

        assertMetricEventTestResult(context, pollingCategory, 0, (NMSConstants.getPollerWorkersByCategory(pollingCategory) * 5) + 10, testContext, false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testSendOverloadedCloudEvents(VertxTestContext testContext, TestInfo testInfo) throws Exception
    {
        testContext.awaitCompletion(1, TimeUnit.SECONDS);

        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var pollingCategory = NMSConstants.Category.CLOUD;

        var context = new JsonObject()
                .put(Metric.METRIC_CATEGORY, pollingCategory.getName()).put("idle.metric.workers", NMSConstants.getPollerWorkersByCategory(pollingCategory))
                .put(EventBusConstants.EVENT_CONTEXT, new JsonObject());

        for (var category : NMSConstants.Category.values())
        {
            context.getJsonObject(EventBusConstants.EVENT_CONTEXT).put(category.name(), pollingCategory == category ? 0 : NMSConstants.getPollerWorkersByCategory(category));
        }

        assertMetricEventTestResult(context, pollingCategory, 0, (NMSConstants.getPollerWorkersByCategory(pollingCategory) * 5) + 5, testContext, false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testSendAvailabilityEventBatchSize2(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var context = new JsonObject().put(Metric.METRIC_CATEGORY, NMSConstants.MetricPlugin.AVAILABILITY.getName()).put("idle.availability.workers", 8);

        assertEventTestResult(context, 202, testContext, new JsonArray().add(NMSConstants.MetricPlugin.AVAILABILITY.getName()).add(NMSConstants.MetricPlugin.PING.getName()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testSendAvailabilityEventOverloadedBatches(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var context = new JsonObject().put(Metric.METRIC_CATEGORY, NMSConstants.MetricPlugin.AVAILABILITY.getName()).put("idle.availability.workers", 0);

        assertEventTestResult(context, 2020, testContext, new JsonArray().add(NMSConstants.MetricPlugin.AVAILABILITY.getName()).add(NMSConstants.MetricPlugin.PING.getName()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testSendNetworkServiceBatchSize1(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var context = new JsonObject().put(Metric.METRIC_CATEGORY, "network.service").put("idle.network.service.workers", 19);

        assertEventTestResult(context, 1, testContext, new JsonArray().add(NMSConstants.MetricPlugin.NETWORK_SERVICE.getName()));
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testSendNetworkServiceOverloadedEvents(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var context = new JsonObject().put(Metric.METRIC_CATEGORY, "network.service").put("idle.network.service.workers", 0);

        assertEventTestResult(context, 24, testContext, new JsonArray().add(NMSConstants.MetricPlugin.NETWORK_SERVICE.getName()));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testSendOverloadedVirtualizationEvents(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var pollingCategory = NMSConstants.Category.VIRTUALIZATION;

        var context = new JsonObject()
                .put(Metric.METRIC_CATEGORY, pollingCategory.getName()).put("idle.metric.workers", NMSConstants.getPollerWorkersByCategory(pollingCategory))
                .put(EventBusConstants.EVENT_CONTEXT, new JsonObject());

        for (var category : NMSConstants.Category.values())
        {
            context.getJsonObject(EventBusConstants.EVENT_CONTEXT).put(category.name(), pollingCategory == category ? 0 : NMSConstants.getPollerWorkersByCategory(category));
        }

        assertMetricEventTestResult(context, pollingCategory, 0, (NMSConstants.getPollerWorkersByCategory(pollingCategory) * 5) + 5, testContext, false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testSendOverloadedDatabaseEvents(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var pollingCategory = NMSConstants.Category.DATABASE;

        var context = new JsonObject()
                .put(Metric.METRIC_CATEGORY, pollingCategory.getName()).put("idle.metric.workers", NMSConstants.getPollerWorkersByCategory(pollingCategory))
                .put(EventBusConstants.EVENT_CONTEXT, new JsonObject());

        for (var category : NMSConstants.Category.values())
        {
            context.getJsonObject(EventBusConstants.EVENT_CONTEXT).put(category.name(), pollingCategory == category ? 0 : NMSConstants.getPollerWorkersByCategory(category));
        }

        assertMetricEventTestResult(context, pollingCategory, 0, NMSConstants.getPollerWorkersByCategory(pollingCategory) + 5, testContext, false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testSendOverloadedWebServerEvents(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var pollingCategory = NMSConstants.Category.WEB_SERVER;

        var context = new JsonObject()
                .put(Metric.METRIC_CATEGORY, pollingCategory.getName()).put("idle.metric.workers", NMSConstants.getPollerWorkersByCategory(pollingCategory))
                .put(EventBusConstants.EVENT_CONTEXT, new JsonObject());

        for (var category : NMSConstants.Category.values())
        {
            context.getJsonObject(EventBusConstants.EVENT_CONTEXT).put(category.name(), pollingCategory == category ? 0 : NMSConstants.getPollerWorkersByCategory(category));
        }

        assertMetricEventTestResult(context, pollingCategory, 0, NMSConstants.getPollerWorkersByCategory(pollingCategory) + 5, testContext, false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testSendOverloadedServiceCheckEvents(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var pollingCategory = NMSConstants.Category.SERVICE_CHECK;

        var context = new JsonObject()
                .put(Metric.METRIC_CATEGORY, pollingCategory.getName()).put("idle.metric.workers", NMSConstants.getPollerWorkersByCategory(pollingCategory))
                .put(EventBusConstants.EVENT_CONTEXT, new JsonObject());

        for (var category : NMSConstants.Category.values())
        {
            context.getJsonObject(EventBusConstants.EVENT_CONTEXT).put(category.name(), pollingCategory == category ? 0 : NMSConstants.getPollerWorkersByCategory(category));
        }

        assertMetricEventTestResult(context, pollingCategory, 0, NMSConstants.getPollerWorkersByCategory(pollingCategory) + 5, testContext, false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testSendOverloadedOtherEvents(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var pollingCategory = NMSConstants.Category.OTHER;

        var context = new JsonObject()
                .put(Metric.METRIC_CATEGORY, pollingCategory.getName()).put("idle.metric.workers", NMSConstants.getPollerWorkersByCategory(pollingCategory))
                .put(EventBusConstants.EVENT_CONTEXT, new JsonObject());

        for (var category : NMSConstants.Category.values())
        {
            context.getJsonObject(EventBusConstants.EVENT_CONTEXT).put(category.name(), pollingCategory == category ? 0 : NMSConstants.getPollerWorkersByCategory(category));
        }

        assertMetricEventTestResult(context, pollingCategory, 0, NMSConstants.getPollerWorkersByCategory(pollingCategory) + 10, testContext, false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testSendOverloadedMiddleWareEvents(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var pollingCategory = NMSConstants.Category.MIDDLEWARE;

        var context = new JsonObject()
                .put(Metric.METRIC_CATEGORY, pollingCategory.getName()).put("idle.metric.workers", NMSConstants.getPollerWorkersByCategory(pollingCategory))
                .put(EventBusConstants.EVENT_CONTEXT, new JsonObject());

        for (var category : NMSConstants.Category.values())
        {
            context.getJsonObject(EventBusConstants.EVENT_CONTEXT).put(category.name(), pollingCategory == category ? 0 : NMSConstants.getPollerWorkersByCategory(category));
        }

        assertMetricEventTestResult(context, pollingCategory, 0, NMSConstants.getPollerWorkersByCategory(pollingCategory) + 5, testContext, false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testCheckDeleteMetricEvent(VertxTestContext testContext, TestInfo testInfo) throws InterruptedException
    {
        testContext.awaitCompletion(15, TimeUnit.SECONDS);

        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var pollingCategory = NMSConstants.Category.NETWORK;

        var context = new JsonObject()
                .put(Metric.METRIC_CATEGORY, pollingCategory.getName()).put("idle.metric.workers", NMSConstants.getPollerWorkersByCategory(pollingCategory))
                .put(EventBusConstants.EVENT_CONTEXT, new JsonObject());

        for (var category : NMSConstants.Category.values())
        {
            context.getJsonObject(EventBusConstants.EVENT_CONTEXT).put(category.name(), pollingCategory == category ? 0 : NMSConstants.getPollerWorkersByCategory(category));
        }

        assertMetricEventTestResult(context, pollingCategory, 0, (NMSConstants.getPollerWorkersByCategory(pollingCategory) * 5) + 5, testContext, true);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    @Disabled
        // as of now
        // why need this test case -> in go engine if any plugin has some logical error than it throws a panic error and whole batch disqualifed and process exited with status code 2 so need to track that events so in future if any changes occured in go engine we can track it
        // what to do if test case failed -> check processutil.log file and find the context that exited with status code 2 and debug in go engine side...do not need to change anything here.
    void testCheckAbnormalProcess(VertxTestContext testContext)
    {
        var file = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + Logger.LOG_DIRECTORY + GlobalConstants.PATH_SEPARATOR + GlobalConstants.MOTADATA_UTIL + GlobalConstants.PATH_SEPARATOR + "@@@-Process Util.log";

        file = file.replace("@@@", new SimpleDateFormat(GlobalConstants.LOGGER_DATE_FORMAT).format(new Date()));

        Assertions.assertTrue(TestUtil.vertx().fileSystem().existsBlocking(file));

        var content = TestUtil.vertx().fileSystem().readFileBlocking(file);

        Assertions.assertNotNull(content);

        Assertions.assertTrue(content.length() > 0);

        Assertions.assertFalse(content.toString().contains("status code 2"));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testSendOverloadedHCIEvents(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var pollingCategory = NMSConstants.Category.HCI;

        var context = new JsonObject()
                .put(Metric.METRIC_CATEGORY, pollingCategory.getName()).put("idle.metric.workers", NMSConstants.getPollerWorkersByCategory(pollingCategory))
                .put(EventBusConstants.EVENT_CONTEXT, new JsonObject());

        for (var category : NMSConstants.Category.values())
        {
            context.getJsonObject(EventBusConstants.EVENT_CONTEXT).put(category.name(), pollingCategory == category ? 0 : NMSConstants.getPollerWorkersByCategory(category));
        }

        assertMetricEventTestResult(context, pollingCategory, 0, (NMSConstants.getPollerWorkersByCategory(pollingCategory) * 5) + 5, testContext, false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testSendOverloadedSDNEvents(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var pollingCategory = NMSConstants.Category.SDN;

        var context = new JsonObject()
                .put(Metric.METRIC_CATEGORY, pollingCategory.getName()).put("idle.metric.workers", NMSConstants.getPollerWorkersByCategory(pollingCategory))
                .put(EventBusConstants.EVENT_CONTEXT, new JsonObject());

        for (var category : NMSConstants.Category.values())
        {
            context.getJsonObject(EventBusConstants.EVENT_CONTEXT).put(category.name(), pollingCategory == category ? 0 : NMSConstants.getPollerWorkersByCategory(category));
        }

        assertMetricEventTestResult(context, pollingCategory, 0, (NMSConstants.getPollerWorkersByCategory(pollingCategory) * 5) + 5, testContext, false);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testSendOverloadedStorageEvents(VertxTestContext testContext, TestInfo testInfo)
    {
        LOGGER.info(String.format("running %s", testInfo.getTestMethod().get().getName()));

        var pollingCategory = NMSConstants.Category.STORAGE;

        var context = new JsonObject()
                .put(Metric.METRIC_CATEGORY, pollingCategory.getName()).put("idle.metric.workers", NMSConstants.getPollerWorkersByCategory(pollingCategory))
                .put(EventBusConstants.EVENT_CONTEXT, new JsonObject());

        for (var category : NMSConstants.Category.values())
        {
            context.getJsonObject(EventBusConstants.EVENT_CONTEXT).put(category.name(), pollingCategory == category ? 0 : NMSConstants.getPollerWorkersByCategory(category));
        }

        assertMetricEventTestResult(context, pollingCategory, 0, (NMSConstants.getPollerWorkersByCategory(pollingCategory) * 5) + 5, testContext, false);
    }

    private void assertMetricEventTestResult(JsonObject context, NMSConstants.Category pollingCategory, int worker, int qualified, VertxTestContext testContext, boolean deleteQueueMetric)
    {
        try
        {
            testContext.awaitCompletion(2, TimeUnit.SECONDS);

            for (var category : NMSConstants.Category.values())
            {
                context.getJsonObject(EventBusConstants.EVENT_CONTEXT).put(category.name(), pollingCategory == category ? worker : NMSConstants.getPollerWorkersByCategory(category));
            }

            var metrics = MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_CATEGORY, pollingCategory.getName())
                    .stream().filter(item -> !JsonObject.mapFrom(item).getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.AVAILABILITY.getName()) && !JsonObject.mapFrom(item).getString(Metric.METRIC_PLUGIN).equalsIgnoreCase(NMSConstants.MetricPlugin.NETWORK_SERVICE.getName()))
                    .map(JsonObject::mapFrom).collect(Collectors.toList());

            if (!metrics.isEmpty())
            {
                if (metrics.size() < qualified)
                {
                    var difference = qualified - metrics.size();

                    var counter = 0;

                    while (counter < difference)
                    {
                        counter++;

                        metrics.add(metrics.stream().findAny().get().copy());
                    }
                }

                if (metrics.size() >= qualified)
                {
                    if (!deleteQueueMetric)
                    {
                        assertMetricPollerStatisticsTestResult(testContext, context, true);
                    }

                    var qualifiedIndex = qualified - 1;

                    for (var index = 0; index < qualified; index++)
                    {
                        var item = metrics.get(index);

                        if (item != null)
                        {
                            var metric = TestNMSUtil.prepareMetricPollContext(item);

                            metric.put(ID, metric.getLong(ID) + index);

                            metric.put(AIOpsObject.OBJECT_EVENT_PROCESSORS, new JsonArray()
                                    .add(RemoteEventProcessorConfigStore.getStore().getItemByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                                            .getLong(ID)));

                            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_ROUTER, metric.put(GlobalConstants.TIMEOUT, 30));

                            if (deleteQueueMetric && qualifiedIndex == index)
                            {
                                var retries = new AtomicInteger(0);

                                TestUtil.vertx().setPeriodic(100, timer ->
                                {
                                    retries.getAndIncrement();

                                    if (MetricCacheStore.getStore().queuedMetric(metric.getLong(ID)))
                                    {
                                        VALID.set(true);

                                        TestUtil.vertx().cancelTimer(timer);

                                        assertDuplicateMetricPollResponseTestResult(testContext, metric.getLong(ID));

                                        MetricCacheStore.getStore().deleteMetric(metric.getLong(ID));
                                    }

                                    if (retries.get() > 30 && !VALID.get())
                                    {
                                        TestUtil.vertx().cancelTimer(timer);

                                        testContext.completeNow();
                                    }
                                });
                            }
                        }
                    }
                }
                else
                {
                    testContext.failNow(new Exception("metrics not available.."));
                }
            }
            else
            {
                testContext.failNow(new Exception(String.format("failed to find metrics for %s category", pollingCategory.getName())));
            }
        }
        catch (Exception exception)
        {

            LOGGER.error(exception);
        }

    }

    private void assertEventTestResult(JsonObject context, int qualified, VertxTestContext testContext, JsonArray plugins)
    {
        try
        {
            testContext.awaitCompletion(2, TimeUnit.SECONDS);

            var metrics = MetricConfigStore.getStore().getItems().stream().filter(item -> plugins.contains(JsonObject.mapFrom(item).getString(Metric.METRIC_PLUGIN))).map(JsonObject::mapFrom).collect(Collectors.toList());

            if (!metrics.isEmpty())
            {
                if (metrics.size() < qualified)
                {
                    var difference = qualified - metrics.size();

                    var retries = 0;

                    while (retries < difference)
                    {
                        retries++;

                        metrics.add(metrics.stream().findAny().get().copy());
                    }
                }

                if (metrics.size() >= qualified)
                {
                    assertAvailabilityPollerStatisticsTestResult(testContext, context, true);

                    LOGGER.info(String.format("metrics size : %s , qualified : %s with plugins %s :", metrics.size(), qualified, plugins));

                    for (var index = 0; index < qualified; index++)
                    {
                        var metric = metrics.get(index);

                        LOGGER.info(String.format("metric : %s ", metric));

                        metric = TestNMSUtil.prepareMetricPollContext(metric);

                        if (metric != null)
                        {
                            metric.put(ID, metric.getLong(ID) + index);

                            metric.put(AIOpsObject.OBJECT_EVENT_PROCESSORS, new JsonArray()
                                    .add(RemoteEventProcessorConfigStore.getStore().getItemByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId())
                                            .getLong(ID)));

                            TestUtil.vertx().eventBus().send(EventBusConstants.EVENT_ROUTER, metric.put(GlobalConstants.TIMEOUT, 2));
                        }
                    }
                }
                else
                {
                    testContext.failNow(new Exception("metrics not available.."));
                }
            }
            else
            {
                testContext.failNow(new Exception("failed to find metrics"));
            }

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void assertAvailabilityPollerStatisticsTestResult(VertxTestContext testContext, JsonObject context, boolean pendingEvent)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_ENGINE_STATS_RESPONSE, message ->
        {
            try
            {
                if (message.body() != null)
                {
                    var event = message.body();

                    if (event.getString(EventBusConstants.ENGINE_TYPE).equalsIgnoreCase(EventBusConstants.EVENT_METRIC_POLL))
                    {
                        messageConsumer.unregister();

                        var categoryName = context.getString(Metric.METRIC_CATEGORY);

                        var idleAvailabilityWorkers = 0;

                        var idleWorkers = 0;

                        if (categoryName.equalsIgnoreCase(NMSConstants.MetricPlugin.AVAILABILITY.getName()))
                        {
                            idleAvailabilityWorkers = MotadataConfigUtil.getAvailabilityWorkers() * MotadataConfigUtil.getWorkers();

                            idleWorkers = context.getInteger("idle.availability.workers");
                        }

                        else if (categoryName.equalsIgnoreCase(NMSConstants.NETWORK_SERVICE))
                        {
                            idleAvailabilityWorkers = MotadataConfigUtil.getNetworkServiceWorkers() * MotadataConfigUtil.getWorkers();

                            idleWorkers = context.getInteger("idle.network.service.workers");
                        }

                        Assertions.assertNotNull(event.getJsonObject(HealthUtil.HEALTH_STATS));

                        var category = JsonObject.mapFrom(event.getJsonObject(HealthUtil.HEALTH_STATS).getJsonArray(Metric.METRIC_CATEGORY).stream().filter(item -> JsonObject.mapFrom(item).getString(Metric.METRIC_CATEGORY).equalsIgnoreCase(categoryName)).findFirst().get());

                        Assertions.assertTrue(category.containsKey("metric.category.idle.workers"));

                        Assertions.assertTrue(category.containsKey("metric.category.pending.events"));

                        if (pendingEvent)
                        {
                            if (category.getInteger("metric.category.idle.workers").equals(idleWorkers))
                            {
                                VALID.set(true);
                            }

                            if (!VALID.get() && category.getInteger("metric.category.idle.workers") == idleAvailabilityWorkers)
                            {
                                VALID.set(true);
                            }

                            assertAvailabilityPollerStatisticsTestResult(testContext, context, !VALID.get());
                        }
                        else
                        {
                            VALID.set(false);

                            if (category.getInteger("metric.category.idle.workers") == idleAvailabilityWorkers && category.getInteger("metric.category.pending.events") == 0)
                            {
                                VALID.set(true);

                                testContext.completeNow();
                            }

                            if (!VALID.get())
                            {
                                assertAvailabilityPollerStatisticsTestResult(testContext, context, false);
                            }
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                messageConsumer.unregister();

                testContext.failNow(exception);
            }
        });

        TestUtil.vertx().setTimer(500, handler -> TestUtil.vertx().eventBus().publish(EventBusConstants.EVENT_ENGINE_STATS, new JsonObject()));
    }

    private void assertMetricPollerStatisticsTestResult(VertxTestContext testContext, JsonObject context, boolean pendingEvent)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_ENGINE_STATS_RESPONSE, message ->
        {
            try
            {
                if (message.body() != null)
                {
                    var event = message.body();

                    if (event.getString(EventBusConstants.ENGINE_TYPE).equalsIgnoreCase(EventBusConstants.EVENT_METRIC_POLL))
                    {
                        messageConsumer.unregister();

                        var statistics = event.getJsonObject(HealthUtil.HEALTH_STATS);

                        Assertions.assertNotNull(statistics);

                        var idleWorkers = statistics.getInteger(HealthUtil.IDLE_WORKERS);

                        Assertions.assertNotNull(idleWorkers);

                        var eventContext = context.getJsonObject(EventBusConstants.EVENT_CONTEXT);

                        Assertions.assertNotNull(eventContext);

                        Assertions.assertTrue(statistics.containsKey(Metric.METRIC_CATEGORY));

                        var metricCategory = JsonObject.mapFrom(statistics.getJsonArray(Metric.METRIC_CATEGORY).stream().filter(item -> JsonObject.mapFrom(item).getString(Metric.METRIC_CATEGORY).equalsIgnoreCase(context.getString(Metric.METRIC_CATEGORY))).findFirst().get());

                        Assertions.assertNotNull(metricCategory);

                        var pending = metricCategory.getInteger("metric.category.pending.events") + metricCategory.getInteger("metric.category.pending.batch.events");

                        if (pendingEvent)
                        {
                            var contextIdleWorkers = eventContext.getMap().values().stream().mapToInt(CommonUtil::getInteger).sum();

                            if (contextIdleWorkers == idleWorkers)
                            {
                                VALID.set(true);
                            }

                            if (!VALID.get() && metricCategory.getInteger("metric.category.idle.workers").equals(context.getInteger("idle.metric.workers")))
                            {
                                VALID.set(true);
                            }

                            assertMetricPollerStatisticsTestResult(testContext, context, !VALID.get());
                        }

                        else
                        {
                            VALID.set(false);

                            if (metricCategory.getInteger("metric.category.idle.workers").equals(context.getInteger("idle.metric.workers")) && pending == 0)
                            {
                                VALID.set(true);

                                testContext.completeNow();
                            }

                            if (!VALID.get())
                            {
                                assertMetricPollerStatisticsTestResult(testContext, context, false);
                            }
                        }
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                messageConsumer.unregister();

                testContext.failNow(exception);
            }
        });

        TestUtil.vertx().setTimer(1000, handler -> TestUtil.vertx().eventBus().publish(EventBusConstants.EVENT_ENGINE_STATS, new JsonObject()));
    }

    private void assertDuplicateMetricPollResponseTestResult(VertxTestContext testContext, long id)
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EventBusConstants.EVENT_METRIC_POLL_RESPONSE, message ->
        {
            try
            {
                if (message.body() != null)
                {
                    var event = message.body();

                    if (event.getLong(ID).equals(id))
                    {
                        Assertions.assertTrue(event.containsKey(MESSAGE));

                        Assertions.assertTrue(event.getString(MESSAGE).contains("Either metric deleted or disabled"));

                        testContext.completeNow();
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                messageConsumer.unregister();

                testContext.failNow(exception);
            }
        });

    }
}

