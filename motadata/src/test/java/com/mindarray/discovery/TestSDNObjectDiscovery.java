/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.discovery;

import com.mindarray.GlobalConstants;
import com.mindarray.TestAPIConstants;
import com.mindarray.TestAPIUtil;
import com.mindarray.TestUtil;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Discovery;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.DiscoveryConfigStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.eventbus.MessageConsumer;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.parallel.Execution;
import org.junit.jupiter.api.parallel.ExecutionMode;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.HashMap;
import java.util.Map;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.TestAPIConstants.DISCOVERY_API_ENDPOINT;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.eventbus.EventBusConstants.*;
import static org.apache.http.HttpStatus.SC_OK;
import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(VertxExtension.class)
@Execution(ExecutionMode.CONCURRENT)
@Timeout(120 * 1000)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestSDNObjectDiscovery
{
    public static final JsonObject DISCOVERY_PROFILES = new JsonObject();

    public static final Map<Long, VertxTestContext> DISCOVERY_ITEMS = new HashMap<>();

    private static final Logger LOGGER = new Logger(TestSDNObjectDiscovery.class, MOTADATA_NMS, "Test SDN Object Discovery");

    public static MessageConsumer<JsonObject> messageConsumer;

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {
        try
        {
            assertDiscoveryConsumerSetup();

            TestUtil.startEventStreaming(new JsonObject().put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_COMPLETED));

            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "src" + GlobalConstants.PATH_SEPARATOR
                    + "test" + GlobalConstants.PATH_SEPARATOR + "resources" + GlobalConstants.PATH_SEPARATOR + "discovery-parameters.json");

            if (file.exists())
            {
                DISCOVERY_PROFILES.mergeIn(new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)));

                testContext.completeNow();
            }
        }
        catch (Exception exception)
        {
            testContext.failNow(exception.getMessage());

            exception.printStackTrace();
        }
    }

    public static void assertDiscoveryConsumerSetup()
    {
        messageConsumer = TestUtil.vertx().eventBus().localConsumer(EVENT_UI, message ->
        {
            var event = message.body();

            if (event.containsKey(EVENT_TYPE) && event.getString(EVENT_TYPE).equalsIgnoreCase(UI_NOTIFICATION_DISCOVERY_STATE_CHANGE) && event.getBinary(EVENT_CONTEXT) != null && message.body().getBinary(EVENT_CONTEXT) != null && message.body().containsKey(EVENT_COMPRESSION_TYPE) && message.body().getInteger(EVENT_COMPRESSION_TYPE) == COMPRESSION_TYPE_ONE)
            {
                var context = CodecUtil.toJSONObject(message.body().getBinary(EventBusConstants.EVENT_CONTEXT));

                if (context.containsKey(NMSConstants.STATE) && context.getString(NMSConstants.STATE).equalsIgnoreCase(NMSConstants.STATE_NOT_RUNNING))
                {
                    DISCOVERY_ITEMS.get(context.getLong(ID)).verify(() ->
                    {
                        try
                        {
                            var discovery = DiscoveryConfigStore.getStore().getItemByValue(ID, context.getLong(ID));

                            assertNotNull(discovery);

                            var testContext = DISCOVERY_ITEMS.get(context.getLong(ID));

                            TestAPIUtil.get(DISCOVERY_API_ENDPOINT + "/" + discovery.getLong(ID) + "/result", testContext.succeeding(response -> testContext.verify(() ->
                            {
                                try
                                {
                                    assertEquals(SC_OK, response.statusCode());

                                    assertEquals(SC_OK, response.bodyAsJsonObject().getInteger(RESPONSE_CODE));

                                    assertTrue(response.bodyAsJsonObject().containsKey(RESULT));

                                    assertFalse(response.bodyAsJsonObject().getJsonArray(RESULT).isEmpty());

                                    var result = response.bodyAsJsonObject().getJsonArray(RESULT).getJsonObject(0);

                                    assertNotNull(result.getString(AIOpsObject.OBJECT_IP));

                                    assertNotNull(result.getString(AIOpsObject.OBJECT_TYPE));

                                    assertNotNull(result.getString(AIOpsObject.OBJECT_CATEGORY));

                                    assertTrue(result.containsKey(AIOpsObject.OBJECT_STATE));

                                    Assertions.assertTrue(result.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.NEW.name()) || result.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.UNPROVISION.name()) || result.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.PROVISION.name()));

                                    testContext.completeNow();

                                }
                                catch (Exception exception)
                                {
                                    LOGGER.error(exception);

                                    testContext.failNow(exception);
                                }
                            })));
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    });
                }
            }
        });
    }

    public static void runSDNDiscoveryTest(VertxTestContext testContext, JsonObject item)
    {
        DISCOVERY_ITEMS.put(item.getLong(ID), testContext);

        TestAPIUtil.post(String.format(TestAPIConstants.DISCOVERY_RUN_API_ENDPOINT, item.getLong(ID)), new JsonObject(), result -> testContext.verify(() -> assertEquals(SC_OK, result.result().statusCode())));
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        messageConsumer.unregister(result -> testContext.completeNow());
    }

    @Test
    void testCiscovManageDiscovery(VertxTestContext testContext, TestInfo testInfo)
    {
        runSDNDiscoveryTest(testContext, DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_NAME)));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testCiscoMerakiDiscovery(VertxTestContext testContext, TestInfo testInfo)
    {
        runSDNDiscoveryTest(testContext, DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_NAME)));
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testCiscoACIDiscovery(VertxTestContext testContext, TestInfo testInfo)
    {
        runSDNDiscoveryTest(testContext, DiscoveryConfigStore.getStore().getItemByValue(Discovery.DISCOVERY_NAME, DISCOVERY_PROFILES.getJsonObject(testInfo.getTestMethod().get().getName()).getString(Discovery.DISCOVERY_NAME)));
    }
}
