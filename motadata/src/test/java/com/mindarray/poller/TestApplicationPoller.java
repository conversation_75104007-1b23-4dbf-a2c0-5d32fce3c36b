/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.poller;

import com.mindarray.GlobalConstants;
import com.mindarray.TestNMSUtil;
import com.mindarray.api.Metric;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.DiscoveryConfigStore;
import com.mindarray.store.MetricConfigStore;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.MOTADATA_NMS;
import static com.mindarray.GlobalConstants.PLUGIN_ID;

@ExtendWith(VertxExtension.class)
@Timeout(220 * 1000)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestApplicationPoller
{

    private static final Map<String, JsonObject> POLLER_CONTEXT = new HashMap<>();

    private static final Logger LOGGER = new Logger(TestApplicationPoller.class, MOTADATA_NMS, "TestApplicationPoller");

    @BeforeAll
    static void beforeAll(VertxTestContext testContext) throws InterruptedException
    {
        testContext.awaitCompletion(10, TimeUnit.SECONDS);

        var items = MetricConfigStore.getStore().getItems();

        for (var index = 0; index < items.size(); index++)
        {
            POLLER_CONTEXT.putIfAbsent(items.getJsonObject(index).getString(Metric.METRIC_TYPE), new JsonObject().put(GlobalConstants.ID, items.getJsonObject(index).getLong(Metric.METRIC_OBJECT)));
        }

        testContext.completeNow();
    }

    @AfterAll
    static void afterAll(VertxTestContext testContext)
    {
        LOGGER.info(String.format("DiscoveryConfigStore : %s", DiscoveryConfigStore.getStore().getItems().encode()));

        LOGGER.info(String.format("ObjectConfigStore : %s", ObjectConfigStore.getStore().getItems().encode()));

        LOGGER.info(String.format("MetricConfigStore : %s", MetricConfigStore.getStore().getItems().encode()));

        testContext.completeNow();
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testPollWildFlyMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.WILDFLY.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.Type.WILDFLY.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testPollNginxMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.NGINX.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.Type.NGINX.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testPollTomcatMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.APACHE_TOMCAT.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.APACHE_TOMCAT.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testPollApacheHTTPMetric(VertxTestContext testContext) throws InterruptedException
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.APACHE_HTTP.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.APACHE_HTTP.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testPollLightHTTPMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.LIGHTTPD.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.LIGHTTPD.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testPollBind9Metric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.LIGHTTPD.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.BIND9.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testPollBind9NetStatMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.LIGHTTPD.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.BIND9_NET_STAT.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testPollBind9SocketMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.LIGHTTPD.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.BIND9_SOCK_STAT.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testPollIBMWebsphereMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.IBM_WEBSPHERE.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.IBM_WEBSPHERE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testPollRabbitMQMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.IBM_WEBSPHERE.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.RABBITMQ.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testPollRabbitMQChannelMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.IBM_WEBSPHERE.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.RABBITMQ_CHANNEL.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testPollRabbitMQConnectionMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.IBM_WEBSPHERE.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.RABBITMQ_CONNECTION.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testPollRabbitMQExchangeMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.IBM_WEBSPHERE.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.RABBITMQ_EXCHANGE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testPollRabbitMQQueueMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.IBM_WEBSPHERE.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.RABBITMQ_QUEUE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testPollPostgresSQLMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.POSTGRESQL.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.POSTGRESQL.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testPollPostgresSQLDatabaseMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.POSTGRESQL.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.POSTGRESQL_DATABASE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testPollPostgresSQLSessionMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.POSTGRESQL.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.POSTGRESQL_SESSION.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testPollIBMDB2Metric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.IBM_DB2.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.IBM_DB2.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testPollIBMDB2DatabaseMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.IBM_DB2.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.IBM_DB2_DATABASE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testPollIBMDB2PoolMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.IBM_DB2.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.IBM_DB2_POOL.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testPollIBMDB2SessionMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.IBM_DB2.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.IBM_DB2_SESSION.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testPollIBMDB2TableSpaceMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.IBM_DB2.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.IBM_DB2_TABLE_SPACE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testPollMSSQLMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.SQL_SERVER.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SQL_SERVER.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testPollMSSQLDatabaseMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.SQL_SERVER.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SQL_SERVER_DATABASE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    void testPollMSSQLSessionMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.SQL_SERVER.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SQL_SERVER_SESSION.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testPollMSSQLJobMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.SQL_SERVER.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SQL_SERVER_JOB.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    void testPollMSSQLSlowQueryMetric(VertxTestContext testContext)
    {
        var metric = TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.SQL_SERVER.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SQL_SERVER_SLOW_QUERY.getName()), testContext);

        var pluginId = CommonUtil.getString(metric.getValue(PLUGIN_ID));

        if (metric.containsKey(pluginId))
        {
            metric.getJsonObject(pluginId).remove(NMSConstants.LAST_QUERY_TIME);
        }

        TestNMSUtil.assertMetricPollResponseTestResult(metric, testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    void testPollMariaDBMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.MARIADB.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.MARIADB.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testPollMariaDBCommandMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.MARIADB.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.MARIADB_COMMAND.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    void testPollMariaDBInnoDBMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.MARIADB.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.MARIADB_INNO_DB.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(31)
    void testPollMySQLMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.MYSQL.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.MYSQL.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(32)
    void testPollMySQLCommandMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.MYSQL.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.MYSQL_COMMAND.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(33)
    void testPollMySQLInnoDBMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.MYSQL.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.MYSQL_INNO_DB.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(34)
    void testPollOracleMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.ORACLE_DATABASE.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.ORACLE_ASM_DISK.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(35)
    void testPollOracleDatabaseMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.ORACLE_DATABASE.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.ORACLE_DATABASE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(36)
    void testPollOracleRollbackSegmentMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.ORACLE_DATABASE.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.ORACLE_ROLLBACK_SEGMENT.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(37)
    void testPollOracleSessionMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.ORACLE_DATABASE.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.ORACLE_SESSION.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(38)
    void testPollOracleSlowQueryMetric(VertxTestContext testContext)
    {
        var metric = TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.ORACLE_DATABASE.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.ORACLE_SLOW_QUERY.getName()), testContext);

        var pluginId = CommonUtil.getString(metric.getInteger(PLUGIN_ID));

        if (metric.containsKey(pluginId))
        {
            metric.getJsonObject(pluginId).remove(NMSConstants.LAST_QUERY_TIME);
        }

        TestNMSUtil.assertMetricPollResponseTestResult(metric, testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(39)
    void testPollOracleTableSpaceMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.ORACLE_DATABASE.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.ORACLE_TABLE_SPACE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(40)
    void testPollSybaseMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.SYBASE.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SYBASE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(41)
    void testPollSybaseDatabaseMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.SYBASE.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SYBASE_DATABASE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(42)
    void testPollSybaseTransactionMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.SYBASE.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SYBASE_TRANSACTION.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(43)
    void testPollSAPMaxDBMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.SAP_MAXDB.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SAP_MAXDB.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(44)
    void testPollSAPMaxDBCommandMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.SAP_MAXDB.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SAP_MAXDB_COMMAND_MONITOR.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(45)
    void testPollSAPMaxDBCacheMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.SAP_MAXDB.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SAP_MAXDB_CACHE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(46)
    void testPollSAPMaxDBDataVolumeMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.SAP_MAXDB.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SAP_MAXDB_DATA_VOLUME.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(47)
    void testPollSAPMaxDBLogVolumeMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.SAP_MAXDB.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SAP_MAXDB_LOG_VOLUME.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(48)
    void testPollSAPMaxDBOMSHeapMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.SAP_MAXDB.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SAP_MAXDB_OMS_HEAP.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(49)
    void testPollSAPMaxDBResourceMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.SAP_MAXDB.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SAP_MAXDB_RESOURCE_MONITOR.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(50)
    void testPollSAPMaxDBSchemaMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.SAP_MAXDB.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SAP_MAXDB_SCHEMA.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(51)
    void testPollSAPHANAMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.SAP_HANA.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SAP_HANA.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(52)
    void testPollSAPHANABackupMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.SAP_HANA.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SAP_HANA_BACKUP.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(53)
    void testPollSAPHANADiskMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.SAP_HANA.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SAP_HANA_DISK.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(54)
    void testPollSAPHANAHostMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.SAP_HANA.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SAP_HANA_HOST.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(55)
    void testPollSAPHANAServiceMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.SAP_HANA.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SAP_HANA_SERVICE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(56)
    void testPollSAPHANASessionMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.SAP_HANA.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SAP_HANA_SESSION.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(57)
    void testPollSAPHANATransactionMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.SAP_HANA.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SAP_HANA_TRANSACTION.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(58)
    void testPollSAPHANAWorkloadMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.SAP_HANA.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.SAP_HANA_WORK_LOAD.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(59)
    void testPollLinuxDHCPMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.SAP_MAXDB.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.LINUX_DHCP.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(60)
    void testPollHAProxyMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.HA_PROXY.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.HA_PROXY.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(61)
    void testPollHAProxySessionMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.HA_PROXY.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.HA_PROXY_SESSION.getName()), testContext), testContext);

    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(62)
    void testPollActiveDirectoryMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.ACTIVE_DIRECTORY.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.ACTIVE_DIRECTORY.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(63)
    void testPollActiveDirectoryRoleMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.ACTIVE_DIRECTORY.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.ACTIVE_DIRECTORY_ROLE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(64)
    void testPollIISMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.ACTIVE_DIRECTORY.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.MICROSOFT_IIS.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(65)
    void testPollIISApplicationMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.ACTIVE_DIRECTORY.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.MICROSOFT_IIS_APPLICATION.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(66)
    void testPollIISASPNETMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.ACTIVE_DIRECTORY.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.MICROSOFT_IIS_ASP_NET.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(67)
    void testPollIISPoolMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.ACTIVE_DIRECTORY.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.MICROSOFT_IIS_POOL.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(68)
    void testPollWindowsDHCPMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.ACTIVE_DIRECTORY.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.WINDOWS_DHCP.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(69)
    void testPollWindowsDHCPScopeMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.ACTIVE_DIRECTORY.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.WINDOWS_DHCP_SCOPE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(70)
    void testPollWindowsDNSMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.ACTIVE_DIRECTORY.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.WINDOWS_DNS.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(71)
    void testPollWindowsRDPMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.ACTIVE_DIRECTORY.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.WINDOWS_RDP.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(72)
    void testPollMSMQMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.ACTIVE_DIRECTORY.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.MSMQ.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(73)
    void testPollExchangeClientAccessRoleMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.EXCHANGE_MAILBOX.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.EXCHANGE_CLIENT_ACCESS_ROLE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(74)
    void testPollExchangeMailBoxRoleMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.EXCHANGE_MAILBOX.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.EXCHANGE_MAILBOX_ROLE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(75)
    void testPollExchangeEdgeTransportRoleMetric(VertxTestContext testContext)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareApplicationMetricPollContext(POLLER_CONTEXT.get(NMSConstants.Type.EXCHANGE_MAILBOX.getName()).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.EXCHANGE_EDGE_TRANSPORT_ROLE.getName()), testContext), testContext);
    }
}
