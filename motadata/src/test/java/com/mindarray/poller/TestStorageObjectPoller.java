/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.poller;


import com.mindarray.GlobalConstants;
import com.mindarray.TestNMSUtil;
import com.mindarray.TestUtil;
import com.mindarray.api.Metric;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.ObjectConfigStore;
import io.github.artsok.RepeatedIfExceptionsTest;
import io.vertx.core.json.JsonObject;
import io.vertx.junit5.Timeout;
import io.vertx.junit5.VertxExtension;
import io.vertx.junit5.VertxTestContext;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.junit.jupiter.api.extension.ExtendWith;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;

@ExtendWith(VertxExtension.class)
@Timeout(220 * 1000)
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
@EnabledIfSystemProperty(named = "test.mode", matches = "^APP|^PRIMARY$")
public class TestStorageObjectPoller
{


    private static final JsonObject POLLER_CONTEXT = new JsonObject();

    @BeforeAll
    static void beforeAll(VertxTestContext testContext)
    {

        try
        {
            var file = new File(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "src" + GlobalConstants.PATH_SEPARATOR
                    + "test" + GlobalConstants.PATH_SEPARATOR + "resources" + GlobalConstants.PATH_SEPARATOR + "poll-parameters.json");

            if (file.exists())
            {
                TestUtil.startEventStreaming(new JsonObject().put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_COMPLETED));

                POLLER_CONTEXT.mergeIn(new JsonObject(Files.readString(file.toPath(), StandardCharsets.UTF_8)));

                testContext.completeNow();
            }
        }
        catch (Exception exception)
        {
            testContext.failNow(exception.getMessage());

            exception.printStackTrace();
        }
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(1)
    void testPollNetAppONTAPClusterMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.NETAPP_ONTAP_CLUSTER.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(2)
    void testPollNetAppONTAPClusterNodeMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.NETAPP_ONTAP_CLUSTER_NODE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(3)
    void testPollNetAppONTAPClusterHardwareSensorMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.NETAPP_ONTAP_CLUSTER_HARDWARE_SENSOR.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(4)
    void testPollNetAppONTAPClusterInterfaceMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.NETAPP_ONTAP_CLUSTER_INTERFACE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(5)
    void testPollNetAppONTAPClusterDiskMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.NETAPP_ONTAP_CLUSTER_DISK.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(6)
    void testPollNetAppONTAPClusterStorageMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.NETAPP_ONTAP_CLUSTER_STORAGE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(7)
    void testPollNetAppONTAPClusterPortMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.NETAPP_ONTAP_CLUSTER_PORT.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(8)
    void testNetAppONTAPClusterSVMMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.NETAPP_ONTAP_CLUSTER_SVM.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(9)
    void testHPEStoreOnceMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.HPE_STORE_ONCE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(10)
    void testHPEStoreOnceDiskMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.HPE_STORE_ONCE_DISK.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(11)
    void testHPEStoreOnceServiceSetMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.HPE_STORE_ONCE_SERVICE_SET.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(12)
    void testHPEStoreOncePortMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.HPE_STORE_ONCE_PORT.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(13)
    void testHPEPrimeraMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.HPE_PRIMERA.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(14)
    void testHPEPrimeraDiskMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.HPE_PRIMERA_DISK.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(15)
    void testHPEPrimeraHostMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.HPE_PRIMERA_HOST.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(16)
    void testHPEPrimeraNodeMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.HPE_PRIMERA_NODE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(17)
    void testHPEPrimeraVolumeMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.HPE_PRIMERA_VOLUME.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(18)
    void testHPEPrimeraPortMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.HPE_PRIMERA_PORT.getName()), testContext), testContext);
    }


    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(19)
    void testHPE3PARMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.HPE_3PAR.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(20)
    void testHPE3PARDiskMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.HPE_3PAR_DISK.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(21)
    void testHPE3PARHostMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.HPE_3PAR_HOST.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(22)
    void testHPE3PARNodeMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.HPE_3PAR_NODE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(23)
    void testHPE3PARVolumeMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.HPE_3PAR_VOLUME.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(24)
    void testHPE3PARPortMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.HPE_3PAR_PORT.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(25)
    void testDellEMCUnityMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.DELL_EMC_UNITY.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(26)
    void testDellEMCUnityEnclosureMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.DELL_EMC_UNITY_ENCLOSURE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(27)
    void testDellEMCUnityFileSystemMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.DELL_EMC_UNITY_FILE_SYSTEM.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(28)
    void testDellEMCUnitFileShareMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.DELL_EMC_UNITY_FILE_SHARE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(29)
    void testDellEMCUnityStorageMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.DELL_EMC_UNITY_STORAGE.getName()), testContext), testContext);
    }

    @RepeatedIfExceptionsTest(suspend = 3000L)
    @Order(30)
    void testDellEMCUnityStorageProcessorMetric(VertxTestContext testContext, TestInfo testInfo)
    {
        TestNMSUtil.assertMetricPollResponseTestResult(TestNMSUtil.prepareMetricPollContext(new JsonObject().put(GlobalConstants.ID, ObjectConfigStore.getStore().getItemByIP(POLLER_CONTEXT.getString(testInfo.getTestMethod().get().getName()))).put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.DELL_EMC_UNITY_STORAGE_PROCESSOR.getName()), testContext), testContext);
    }
}
