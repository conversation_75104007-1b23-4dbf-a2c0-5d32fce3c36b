/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.util;

import com.mindarray.GlobalConstants;
import inet.ipaddr.IPAddressString;
import io.vertx.core.Vertx;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.mapdb.BTreeMap;
import org.mapdb.DB;
import org.mapdb.DBMaker;
import org.mapdb.Serializer;
import org.mapdb.serializer.SerializerArray;
import org.mapdb.volume.MappedFileVol;
import org.mapdb.volume.Volume;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.mindarray.GlobalConstants.*;

@SuppressWarnings(value = {"unchecked", "rawtypes"})
public class GeoDBUtil
{
    private static final Logger LOGGER = new Logger(GeoDBUtil.class, GlobalConstants.MOTADATA_UTIL, "GeoDB Util");

    private static final Map<String, Object[]> cacheEntries = new ConcurrentHashMap<>();

    private static final Map<String, JsonObject> coordinates = new ConcurrentHashMap<>();

    private static Volume cityIndexVolume;

    private static Volume cityVolume;

    private static Volume domainVolume;

    private static Volume ispVolume;

    private static Volume blackIPVolume;

    private static Volume cityGeoLocationVolume;

    private static DB cityIndexDB;

    private static DB cityDB;

    private static DB domainDB;

    private static DB ispDB;

    private static DB blackIPDB;

    private static DB cityGeoLocationDB;

    private static BTreeMap<Long, Integer> cityIndexItems;

    private static BTreeMap<Integer, Object[]> cityItems;

    private static BTreeMap<Long, String> domainItems;

    private static BTreeMap<Long, Object[]> ispItems;

    private static BTreeMap<Long, String> blackIPItems;

    private static BTreeMap<String, Object[]> cityGeoLocationItems;

    public static void init(Vertx vertx)
    {
        vertx.<Void>executeBlocking(future ->
        {
            try
            {
                // City
                cityIndexVolume = MappedFileVol.FACTORY.makeVolume(CURRENT_DIR + PATH_SEPARATOR + DB_DIR + PATH_SEPARATOR + GEO_LOCATION + PATH_SEPARATOR + "city.idx", true);
                cityIndexDB = DBMaker.volumeDB(cityIndexVolume, true).readOnly().make();
                cityIndexItems = cityIndexDB.treeMap("map").keySerializer(Serializer.LONG).valueSerializer(Serializer.INTEGER).open();

                cityVolume = MappedFileVol.FACTORY.makeVolume(CURRENT_DIR + PATH_SEPARATOR + DB_DIR + PATH_SEPARATOR + GEO_LOCATION + PATH_SEPARATOR + "city.dat", true);
                cityDB = DBMaker.volumeDB(cityVolume, true).readOnly().make();
                cityItems = cityDB.treeMap("map").keySerializer(Serializer.INTEGER).valueSerializer(new SerializerArray(Serializer.STRING)).open();

                // Domain
                domainVolume = MappedFileVol.FACTORY.makeVolume(CURRENT_DIR + PATH_SEPARATOR + DB_DIR + PATH_SEPARATOR + GEO_LOCATION + PATH_SEPARATOR + "domain.dat", true);
                domainDB = DBMaker.volumeDB(domainVolume, true).readOnly().make();
                domainItems = domainDB.treeMap("map").keySerializer(Serializer.LONG).valueSerializer(Serializer.STRING).open();

                // ISP
                ispVolume = MappedFileVol.FACTORY.makeVolume(CURRENT_DIR + PATH_SEPARATOR + DB_DIR + PATH_SEPARATOR + GEO_LOCATION + PATH_SEPARATOR + "isp.dat", true);
                ispDB = DBMaker.volumeDB(ispVolume, true).readOnly().make();
                ispItems = ispDB.treeMap("map").keySerializer(Serializer.LONG).valueSerializer(new SerializerArray(Serializer.STRING)).open();

                // Black IP
                blackIPVolume = MappedFileVol.FACTORY.makeVolume(CURRENT_DIR + PATH_SEPARATOR + DB_DIR + PATH_SEPARATOR + GEO_LOCATION + PATH_SEPARATOR + "black-ip.dat", true);
                blackIPDB = DBMaker.volumeDB(blackIPVolume, true).readOnly().make();
                blackIPItems = blackIPDB.treeMap("map").keySerializer(Serializer.LONG).valueSerializer(Serializer.STRING).open();

                new JsonObject(CommonUtil.getString(vertx.fileSystem().readFileBlocking(CURRENT_DIR + PATH_SEPARATOR + DB_DIR + PATH_SEPARATOR + "countries.json"))).getMap().forEach((key, value) -> coordinates.put(key, JsonObject.mapFrom(value)));

                // Coordinates city long lat
                cityGeoLocationVolume = MappedFileVol.FACTORY.makeVolume(CURRENT_DIR + PATH_SEPARATOR + DB_DIR + PATH_SEPARATOR + GEO_LOCATION + PATH_SEPARATOR + "coordinate.dat", true);
                cityGeoLocationDB = DBMaker.volumeDB(cityGeoLocationVolume, true).readOnly().make();
                cityGeoLocationItems = cityGeoLocationDB.treeMap("map").keySerializer(Serializer.STRING).valueSerializer(new SerializerArray(Serializer.DOUBLE)).open();

                LOGGER.info("GeoDB Stores initialized successfully.");
            }

            catch (Exception exception)
            {
                future.fail(exception.getCause());

                LOGGER.error(exception);
            }
        });
    }

    public static boolean resolveIP(String ipAddress, String prefix, JsonObject context)
    {
        var result = false;

        try
        {
            if (CommonUtil.isNotNullOrEmpty(ipAddress))
            {
                var values = cacheEntries.get(ipAddress);

                if (values == null)
                {
                    values = get(ipAddress);

                    if (values != null)
                    {
                        cacheEntries.put(ipAddress, values);
                    }
                }

                if (values != null)
                {
                    context.put(prefix + ".country", values[0])    // data will be always there
                            .put(prefix + ".city", values[1])
                            .put(prefix + ".domain", values[2])
                            .put(prefix + ".isp", values[3])
                            .put(prefix + ".asn", values[4])
                            .put(prefix + ".aso", values[5])
                            .put(prefix + ".threat", values[6]);

                    result = true;
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }

    public static JsonObject resolveIP(String ipAddress, String column)
    {
        JsonObject result = null;

        try
        {
            if (CommonUtil.isNotNullOrEmpty(ipAddress))
            {
                var values = cacheEntries.get(ipAddress);

                if (values == null)
                {
                    values = get(ipAddress);

                    if (values != null)
                    {
                        cacheEntries.put(ipAddress, values);
                    }
                }

                if (values != null)
                {
                    result = new JsonObject();

                    result.put(column + ".country", values[0])    // data will be always there
                            .put(column + ".city", values[1])
                            .put(column + ".domain", values[2])
                            .put(column + ".isp", values[3])
                            .put(column + ".asn", values[4])
                            .put(column + ".aso", values[5])
                            .put(column + ".threat", values[6]);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }

    /**
     * 0    country
     * 1    city
     * 2    domain
     * 3    isp
     * 4    autonomous_system_number
     * 5    autonomous_system_organization
     * 6    threat
     *
     * @param ipAddress
     * @return
     */
    public static Object[] get(String ipAddress)
    {
        var values = new Object[]{"unknown", "unknown", "unknown", "unknown", "unknown", "unknown", "no"};

        var ipValue = new IPAddressString(ipAddress).getAddress().getValue().longValue();

        var props = getCity(ipValue);

        if (props != null)
        {
            values[0] = props[0];
            values[1] = props[1];
        }

        var domain = getDomain(ipValue);

        if (domain != null)
        {
            values[2] = domain;
        }

        props = getISP(ipValue);

        if (props != null)
        {
            values[3] = props[0];
            values[4] = props[1];
            values[5] = props[2];
        }

        var threat = blackIPItems.get(ipValue);

        if (!CommonUtil.isNullOrEmpty(threat))
        {
            values[6] = "Black IP";
        }

        return props != null || domain != null || !CommonUtil.isNullOrEmpty(threat) ? values : null;
    }

    private static Object[] getCity(long ipValue)
    {
        var item = cityIndexItems.floorEntry(ipValue);

        if (item != null)
        {
            var props = cityItems.get(item.getValue());

            if (props != null && props.length != 0)
            {
                return props;
            }
        }

        return null;
    }

    private static String getDomain(long ipValue)
    {
        var item = domainItems.floorEntry(ipValue);

        if (item != null && !item.getValue().isEmpty())
        {
            return item.getValue();
        }

        return null;
    }

    private static Object[] getISP(long ipValue)
    {
        var item = ispItems.floorEntry(ipValue);

        if (item != null && item.getValue() != null && item.getValue().length != 0)
        {
            return item.getValue();
        }

        return null;
    }

    public static JsonObject getCityGeoLocation(String city)
    {
        var item = cityGeoLocationItems.get(city);

        if (item != null)
        {
            return new JsonObject().put(LATITUDE, item[0]).put(LONGITUDE, item[1]);
        }

        return null;
    }

    public static JsonObject getCountryGeoLocation(String country)
    {
        return coordinates.getOrDefault(country, null);
    }

    public static void close() throws IOException
    {
        try
        {
            if (cityIndexVolume != null && !cityIndexVolume.isClosed())
            {
                cityIndexVolume.close();
            }

            if (cityDB != null && !cityDB.isClosed())
            {
                cityDB.close();
            }

            if (domainVolume != null && !domainVolume.isClosed())
            {
                domainVolume.close();
            }

            if (domainDB != null && !domainDB.isClosed())
            {
                domainDB.close();
            }

            if (ispVolume != null && !ispVolume.isClosed())
            {
                ispVolume.close();
            }

            if (ispDB != null && !ispDB.isClosed())
            {
                ispDB.close();
            }

            if (blackIPVolume != null && !blackIPVolume.isClosed())
            {
                blackIPVolume.close();
            }

            if (cityGeoLocationDB != null && !cityGeoLocationDB.isClosed())
            {
                cityGeoLocationDB.close();
            }

            if (cityGeoLocationVolume != null && !cityGeoLocationVolume.isClosed())
            {
                cityGeoLocationVolume.close();
            }

            LOGGER.info("GeoDB Stores closed successfully.");
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    public static JsonArray getCountries()
    {
        var countries = new JsonArray();

        try
        {
            for (var country : coordinates.keySet())
            {
                countries.add(country);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return countries;
    }
}

