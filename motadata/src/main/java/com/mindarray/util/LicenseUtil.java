
/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 * Change Logs:
 *   Date			Author			          Notes
 *   28-Feb-2025    Pruthviraj                Licensing added for netroute
 *24-Mar-2025     Chandresh           MOTADATA-5426: Docker discovery and polling support added
 */


package com.mindarray.util;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.License;
import com.mindarray.api.User;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.ha.HAConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.notification.Notification;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.store.*;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.IOUtils;
import org.apache.commons.text.StringSubstitutor;
import org.joda.time.DateTime;
import org.joda.time.Days;
import org.joda.time.Duration;
import org.xerial.snappy.Snappy;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.net.NetworkInterface;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import static com.mindarray.Bootstrap.vertx;
import static com.mindarray.ErrorMessageConstants.FAILED_TO_VALIDATE_LICENSE;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.InfoMessageConstants.*;
import static com.mindarray.eventbus.EventBusConstants.EVENT_TYPE;
import static com.mindarray.ha.HAConstants.CACHE_NAME;
import static com.mindarray.notification.Notification.EMAIL_NOTIFICATION_DAILY_LIMIT_NEARLY_REACHED_TEMPLATE;
import static com.mindarray.notification.Notification.EMAIL_NOTIFICATION_DAILY_LIMIT_REACHED_TEMPLATE;

public final class LicenseUtil
{
    public static final String LICENSE_FILE = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + "license.lic";
    public static final String LICENSE_PATH = CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + "license-last-usage";
    public static final String LICENSE_LAST_USAGE_TIME = "license.last.usage.time";
    public static final String LICENSE_CONFIG_MANAGEMENT_ENABLED = "config.management.enabled";
    public static final String LICENSE_CONFIG_DEVICES = "licensed.config.devices";
    public static final String LICENSE_COMPLIANCE_ENABLED = "compliance.enabled";

    // ======== License fields ========
    public static final AtomicReference<LicenseEdition> LICENSE_EDITION = new AtomicReference<>();
    public static final AtomicReference<LicenseType> LICENSE_TYPE = new AtomicReference<>();
    public static final AtomicLong LICENSE_EXPIRY_DATE = new AtomicLong(0);
    public static final AtomicBoolean MONITORING_ENABLED = new AtomicBoolean(false);
    public static final AtomicInteger LICENSED_OBJECTS = new AtomicInteger(0);
    public static final AtomicBoolean CONFIG_MANAGEMENT_ENABLED = new AtomicBoolean(false);
    public static final AtomicInteger LICENSED_CONFIG_DEVICES = new AtomicInteger(0);
    public static final AtomicBoolean COMPLIANCE_ENABLED = new AtomicBoolean(false);
    // License Keys
    public static final AtomicBoolean LOG_ENABLED = new AtomicBoolean(false);
    public static final AtomicLong LICENSED_LOG_QUOTA_BYTES = new AtomicLong(0);
    public static final AtomicLong USED_LOG_QUOTA_BYTES = new AtomicLong(0);
    public static final AtomicBoolean DAILY_LOG_QUOTA_REACHED = new AtomicBoolean(false);
    // Monitoring
    public static final AtomicBoolean FLOW_ENABLED = new AtomicBoolean(false);

    // Log
    public static final AtomicLong LICENSED_FLOW_QUOTA_BYTES = new AtomicLong(0);
    public static final AtomicLong USED_FLOW_QUOTA_BYTES = new AtomicLong(0);
    public static final AtomicBoolean DAILY_FLOW_QUOTA_REACHED = new AtomicBoolean(false);
    private static final Logger LOGGER = new Logger(LicenseUtil.class, GlobalConstants.MOTADATA_SYSTEM, "License Util");
    private static final String HARDWARE_IDS = "hardware.ids";
    private static final String[] PLACEHOLDERS = {"$$$event.type$$$", "$$$event.user.limit$$$", "$$$event.license.limit$$$"};
    private static boolean logQuotaLimitWarningStatus = false;
    private static boolean logQuotaLimitCriticalStatus = false;
    private static boolean flowQuotaLimitWarningStatus = false;
    private static boolean flowQuotaLimitCriticalStatus = false;
    // Flow
    private static byte[] secretKeySpecBytes;
    private static byte[] ivParameterSpecBytes;

    /**
     * IF daily quota reached -> false
     * ELSE update daily quota
     * * IF daily quota is reached -> false
     * * ELSE -> true
     */
    public static boolean updateUsedLogQuota(long bytes)
    {
        if (DAILY_LOG_QUOTA_REACHED.get())
        {
            if (!logQuotaLimitCriticalStatus)
            {
                logQuotaLimitCriticalStatus = true;

                notify(PolicyEngineConstants.PolicyType.LOG.getName(), EMAIL_NOTIFICATION_DAILY_LIMIT_REACHED_TEMPLATE, LICENSE_DAILY_QUOTA_LIMIT_REACHED_NOTIFICATION_SMS, LICENSE_DAILY_QUOTA_LIMIT_REACHED);
            }
            return false;
        }
        else if (USED_LOG_QUOTA_BYTES.addAndGet(bytes) > LICENSED_LOG_QUOTA_BYTES.get())
        {
            DAILY_LOG_QUOTA_REACHED.set(true);

            LOGGER.warn(String.format("Daily log quota reached: %d GB", LICENSED_LOG_QUOTA_BYTES.get() / **********L));

            return false;
        }
        else
        {
            if (CommonUtil.getDouble((USED_LOG_QUOTA_BYTES.get() * 100) / CommonUtil.getDouble(LICENSED_LOG_QUOTA_BYTES.get())) >= 90)
            {
                if (!logQuotaLimitWarningStatus)
                {
                    logQuotaLimitWarningStatus = true;

                    notify(PolicyEngineConstants.PolicyType.LOG.getName(), EMAIL_NOTIFICATION_DAILY_LIMIT_NEARLY_REACHED_TEMPLATE, LICENSE_DAILY_QUOTA_LIMIT_NEARLY_REACHED_NOTIFICATION_SMS, LICENSE_DAILY_QUOTA_LIMIT_NEARLY_REACHED);
                }
            }

        }

        return true;
    }


    /**
     * Reset USED_LOG_QUOTA_BYTES & DAILY_LOG_QUOTA_REACHED
     */
    public static void resetUsedLogQuota()
    {
        USED_LOG_QUOTA_BYTES.set(0);

        DAILY_LOG_QUOTA_REACHED.set(false);

        logQuotaLimitCriticalStatus = false;

        logQuotaLimitWarningStatus = false;
    }

    /**
     * IF daily quota reached -> false
     * ELSE update daily quota
     * * IF daily quota is reached -> false
     * * ELSE -> true
     */
    public static boolean updateUsedFlowQuota(long bytes)
    {
        if (DAILY_FLOW_QUOTA_REACHED.get())
        {
            if (!flowQuotaLimitCriticalStatus)
            {
                flowQuotaLimitCriticalStatus = true;

                notify(PolicyEngineConstants.PolicyType.FLOW.getName(), EMAIL_NOTIFICATION_DAILY_LIMIT_REACHED_TEMPLATE, LICENSE_DAILY_QUOTA_LIMIT_REACHED_NOTIFICATION_SMS, LICENSE_DAILY_QUOTA_LIMIT_REACHED);
            }

            return false;

        }
        else if (USED_FLOW_QUOTA_BYTES.addAndGet(bytes) > LICENSED_FLOW_QUOTA_BYTES.get())
        {
            DAILY_FLOW_QUOTA_REACHED.set(true);

            LOGGER.warn(String.format("Daily flow quota reached: %d GB", LICENSED_LOG_QUOTA_BYTES.get() / **********L));

            return false;
        }
        else
        {
            if (CommonUtil.getDouble((USED_FLOW_QUOTA_BYTES.get() * 100) / CommonUtil.getDouble(LICENSED_FLOW_QUOTA_BYTES.get())) >= 90)
            {
                if (!flowQuotaLimitWarningStatus)
                {
                    flowQuotaLimitWarningStatus = true;

                    notify(PolicyEngineConstants.PolicyType.FLOW.getName(), EMAIL_NOTIFICATION_DAILY_LIMIT_NEARLY_REACHED_TEMPLATE, LICENSE_DAILY_QUOTA_LIMIT_NEARLY_REACHED_NOTIFICATION_SMS, LICENSE_DAILY_QUOTA_LIMIT_NEARLY_REACHED);
                }
            }
        }

        return true;
    }

    /**
     * Reset USED_FLOW_QUOTA_BYTES & DAILY_FLOW_QUOTA_REACHED
     */
    public static void resetUsedFlowQuota()
    {
        USED_FLOW_QUOTA_BYTES.set(0);

        DAILY_FLOW_QUOTA_REACHED.set(false);

        flowQuotaLimitWarningStatus = false;

        flowQuotaLimitCriticalStatus = false;

        vertx().eventBus().send(EventBusConstants.EVENT_FLOW_QUOTA_LIMIT_RESET, null);
    }

    /**
     * init and validate license
     */
    public static Future<JsonObject> init(boolean freshInstallation)
    {
        try (var resourceAsStream = Thread.currentThread().getContextClassLoader().getResourceAsStream("aes.key"))
        {
            var buffer = Buffer.buffer(IOUtils.toString(resourceAsStream, StandardCharsets.UTF_8));

            secretKeySpecBytes = buffer.toString().split(" ")[0].trim().getBytes(StandardCharsets.UTF_8);

            ivParameterSpecBytes = buffer.toString().split(" ")[1].trim().getBytes(StandardCharsets.UTF_8);

            return validate(freshInstallation, null);   // validate
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            return Future.failedFuture(exception);
        }
    }

    public static JsonObject getLicenseDetails()
    {
        JsonObject license = null;

        try
        {
            var buffer = Bootstrap.vertx().fileSystem().readFileBlocking(LICENSE_FILE);

            if (buffer != null && buffer.length() > 0)
            {
                license = new JsonObject(decrypt(buffer.toString()));

                if (!license.isEmpty())
                {
                    license.put(License.LICENSE_ACTIVATION_CODE, buffer.toString());

                    license.put("remaining.days", Duration.millis((LICENSE_EXPIRY_DATE.get() - DateTimeUtil.currentMilliSeconds())).toStandardDays().getDays());

                    if (LICENSE_EXPIRY_DATE.get() - DateTimeUtil.currentMilliSeconds() < 0)
                    {
                        license.put("remaining.days", -1);
                    }

                    if (MONITORING_ENABLED.get())
                    {
                        license.put("used.monitors", (ObjectConfigStore.getStore().getProvisionedItems() - AgentConfigStore.getStore().getProvisionedItems()) + NetRouteConfigStore.getStore().getProvisionedItems())
                                .put("used.applications", MetricConfigStore.getStore().getProvisionedInstances(NMSConstants.APPS))
                                .put("used.vms", MetricConfigStore.getStore().getProvisionedInstances(NMSConstants.VMS))
                                .put("used.containers", MetricConfigStore.getStore().getProvisionedInstances(NMSConstants.CONTAINERS))
                                .put("used.wan.links", MetricConfigStore.getStore().getProvisionedInstances("ipsla.icmp.echo.links") + MetricConfigStore.getStore().getProvisionedInstances("ipsla.icmp.jitter.links") + MetricConfigStore.getStore().getProvisionedInstances("ipsla.path.echo.links"))
                                .put("used.access.points", Math.round(Math.ceil(CommonUtil.getDouble(MetricConfigStore.getStore().getProvisionedInstances(NMSConstants.ACCESS_POINTS)) / 2)))
                                .put("used.agents", AgentConfigStore.getStore().getProvisionedItems() - AgentConfigStore.getStore().getReservedMetricAgents());
                    }

                    if (CONFIG_MANAGEMENT_ENABLED.get())
                    {
                        license.put("used.config.devices", ConfigurationConfigStore.getStore().getProvisionedItems());
                    }

                    if (LOG_ENABLED.get())
                    {
                        license.put("used.log.quota.bytes", USED_LOG_QUOTA_BYTES.get());
                    }

                    if (FLOW_ENABLED.get())
                    {
                        license.put("used.flow.quota.bytes", USED_FLOW_QUOTA_BYTES.get());
                    }

                    license.remove(HARDWARE_IDS); // remove sensitive fields [license-key]

                    LOGGER.debug(license.encode());
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return license;
    }

    /**
     * update license as per last usage
     */
    public static void load(JsonObject license)
    {
        try
        {
             /* if motadata service goes down before 12:00 AM and start after 12:00 AM at that time
                system job will not reset usage quota so need to check last updated date
                and current date and if it's not same start with 0 */

            if (!Bootstrap.vertx().fileSystem().existsBlocking(LicenseUtil.LICENSE_PATH))
            {
                Bootstrap.vertx().fileSystem().writeFileBlocking(LICENSE_PATH,
                        Buffer.buffer(CodecUtil.compress(new JsonObject()
                                .put("used.log.quota.bytes", USED_LOG_QUOTA_BYTES.get())
                                .put("used.flow.quota.bytes", USED_FLOW_QUOTA_BYTES.get())
                                .put(LicenseUtil.LICENSE_LAST_USAGE_TIME, System.currentTimeMillis()).encode())));
            }

            var buffer = Bootstrap.vertx().fileSystem().readFileBlocking(LicenseUtil.LICENSE_PATH);

            license.mergeIn(new JsonObject(new String(Snappy.uncompress(buffer.getBytes()))));

            var valid = validateLastUsageDate(license);

            LICENSE_EXPIRY_DATE.set(license.getLong("license.expiry.time"));

            var licenseEdition = license.getInteger("license.edition");

            LICENSE_EDITION.set(LicenseEdition.values()[licenseEdition]);

            LOGGER.info("LICENSE_EDITION : " + LICENSE_EDITION.get());

            if (CommonUtil.isNotNullOrEmpty(license.getString("monitoring.enabled")))
            {
                MONITORING_ENABLED.set(YES.equalsIgnoreCase(license.getString("monitoring.enabled")));

                if (MONITORING_ENABLED.get())
                {
                    LICENSED_OBJECTS.set(license.getInteger("licensed.monitors"));
                }
            }

            if (CommonUtil.isNotNullOrEmpty(license.getString(LICENSE_CONFIG_MANAGEMENT_ENABLED)))
            {
                CONFIG_MANAGEMENT_ENABLED.set(YES.equalsIgnoreCase(license.getString(LICENSE_CONFIG_MANAGEMENT_ENABLED)));

                if (CONFIG_MANAGEMENT_ENABLED.get())
                {
                    LICENSED_CONFIG_DEVICES.set(license.getInteger(LICENSE_CONFIG_DEVICES));
                }
            }

            if (CommonUtil.isNotNullOrEmpty(license.getString("log.enabled")))
            {
                LOG_ENABLED.set(YES.equalsIgnoreCase(license.getString("log.enabled")));

                if (LOG_ENABLED.get())
                {
                    LICENSED_LOG_QUOTA_BYTES.set(license.getLong("licensed.log.quota.bytes", 0L));

                    USED_LOG_QUOTA_BYTES.set(valid ? license.getLong("used.log.quota.bytes", 0L) : 0);

                    DAILY_LOG_QUOTA_REACHED.set(USED_LOG_QUOTA_BYTES.get() > LICENSED_LOG_QUOTA_BYTES.get());
                }
            }

            if (CommonUtil.isNotNullOrEmpty(license.getString("flow.enabled")))
            {
                FLOW_ENABLED.set(YES.equalsIgnoreCase(license.getString("flow.enabled")));

                if (FLOW_ENABLED.get())
                {
                    USED_FLOW_QUOTA_BYTES.set(license.getLong("used.flow.quota.bytes", 0L));

                    LICENSED_FLOW_QUOTA_BYTES.set(valid ? license.getLong("licensed.flow.quota.bytes", 0L) : 0);

                    DAILY_FLOW_QUOTA_REACHED.set(USED_FLOW_QUOTA_BYTES.get() > LICENSED_FLOW_QUOTA_BYTES.get());
                }
            }

            COMPLIANCE_ENABLED.set(CONFIG_MANAGEMENT_ENABLED.get() && (LICENSE_EDITION.get().getCode() == LicenseEdition.HYBRID.getCode() || LICENSE_EDITION.get().getCode() == LicenseEdition.HYBRID_LITE.getCode() || LICENSE_EDITION.get().getCode() == LicenseEdition.OBSERVABILITY.getCode()));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * scenario: if motadata service goes down before 12:00 AM and start after 12:00 AM at that time system job will not reset usage quota so need to check last updated date and current date and if it's not same start with 0
     */
    private static boolean validateLastUsageDate(JsonObject license)
    {
        var valid = false;

        try
        {
            var dateTime = new DateTime(license.containsKey(LICENSE_LAST_USAGE_TIME) ? license.getLong(LICENSE_LAST_USAGE_TIME) : System.currentTimeMillis());

            var lastUsedDateTime = Calendar.getInstance();

            lastUsedDateTime.set(Calendar.YEAR, dateTime.getYear());

            lastUsedDateTime.set(Calendar.MONTH, dateTime.getMonthOfYear() - 1);

            lastUsedDateTime.set(Calendar.DAY_OF_MONTH, dateTime.getDayOfMonth());

            lastUsedDateTime.set(Calendar.HOUR_OF_DAY, 0);

            lastUsedDateTime.set(Calendar.MINUTE, 0);

            lastUsedDateTime.set(Calendar.SECOND, 0);

            lastUsedDateTime.set(Calendar.MILLISECOND, 0);

            var currentDate = Calendar.getInstance();

            currentDate.set(Calendar.HOUR_OF_DAY, 0);

            currentDate.set(Calendar.MINUTE, 0);

            currentDate.set(Calendar.SECOND, 0);

            currentDate.set(Calendar.MILLISECOND, 0);

            valid = !currentDate.after(lastUsedDateTime) && Days.daysBetween(new DateTime(lastUsedDateTime), new DateTime(currentDate)).getDays() == 0;
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return valid;
    }

    /**
     * generates free evaluation license
     * <p>
     * edition = Free
     * type = Evaluation
     * expiry = 30 Days
     * licensed.monitors = 100
     * licensed.eps = 200
     * licensed.fps = 200
     * licensed.log.quota = 5 GB
     * licensed.flow.quota = 5 GB
     */
    private static JsonObject generateTrialLicense()
    {
        var hardwareAddresses = getHardwareAddresses();

        var license = new JsonObject();

        if (!hardwareAddresses.isEmpty())
        {
            try
            {
                Calendar expiryTime = Calendar.getInstance();

                expiryTime.add(Calendar.MONTH, 1); // Add 1 month

                expiryTime.set(Calendar.HOUR_OF_DAY, 23);

                expiryTime.set(Calendar.MINUTE, 59);

                expiryTime.set(Calendar.SECOND, 59);

                expiryTime.set(Calendar.MILLISECOND, 999);

                license.put("description", "Motadata Observability Edition");

                license.put("license.issue.time", System.currentTimeMillis());

                license.put("license.expiry.time", expiryTime.getTimeInMillis());

                license.put("license.time.zone", expiryTime.getTimeZone().getID());

                license.put("license.type", LicenseType.FREE);

                license.put("license.edition", LicenseEdition.OBSERVABILITY.getCode());

                license.put(HARDWARE_IDS, new JsonArray(hardwareAddresses));

                // monitors
                license.put("monitoring.enabled", YES);

                license.put("licensed.monitors", 100);

                // Config devices
                license.put(LICENSE_CONFIG_MANAGEMENT_ENABLED, YES);

                license.put(LICENSE_CONFIG_DEVICES, 100);

                // log
                license.put("log.enabled", YES);

                license.put("licensed.log.quota.bytes", 5 * **********L); //default value

                // license.put("licensed.eps", 200);

                // flow

                license.put("flow.enabled", YES);

                license.put("licensed.flow.quota.bytes", 5 * **********L);    // default value

                // license.put("licensed.fps", 200);

                // Compliance
                license.put(LICENSE_COMPLIANCE_ENABLED, YES);

                Bootstrap.vertx().fileSystem().writeFileBlocking(LICENSE_FILE, Buffer.buffer(encrypt(license.encode())));
            }
            catch (Exception exception)
            {
                LOGGER.fatal("failed to generate free license. Please, contact <EMAIL> for trial license");

                LOGGER.error(exception);
            }
        }

        return license;
    }

    /**
     * @param freshInstallation if installation is fresh & license file doesn't exist create one with Free.
     * @param buffer            license for update through API
     */
    public static Future<JsonObject> validate(boolean freshInstallation, Buffer buffer)
    {
        var promise = Promise.<JsonObject>promise();

        LOGGER.info(String.format("fresh installation %s ", freshInstallation));

        try
        {
            if (Bootstrap.vertx().fileSystem().existsBlocking(LICENSE_FILE))
            {
                if (buffer == null)
                {
                    buffer = Bootstrap.vertx().fileSystem().readFileBlocking(LICENSE_FILE);
                }

                if (buffer != null && buffer.length() > 0)
                {
                    var license = new JsonObject(decrypt(buffer.toString()));

                    if (!license.isEmpty())
                    {
                        LOGGER.debug("License: " + license.encodePrettily());

                        var hardwareIds = license.getJsonArray(HARDWARE_IDS);

                        if (hardwareIds != null)
                        {
                            if (getHardwareAddresses().stream().anyMatch(hardwareIds::contains))
                            {
                                // hardware id is valid.. now validate issue date and expiry date...
                                if (license.containsKey("license.issue.time"))
                                {
                                    if (System.currentTimeMillis() < license.getLong("license.issue.time"))
                                    {
                                        LOGGER.fatal("License is Expired. Please contact '<EMAIL>' for Renewal or Extension.");

                                        promise.fail(LicenseError.INVALID_ISSUE_TIME.getName());
                                    }

                                    else if (license.containsKey("license.expiry.time"))
                                    {
                                        if (System.currentTimeMillis() < license.getLong("license.expiry.time"))
                                        {
                                            if (Bootstrap.vertx().fileSystem().existsBlocking(LICENSE_PATH))    // TODO: 12/05/23 validate the use-case
                                            {
                                                var lastUsage = CodecUtil.toJSONObject(Bootstrap.vertx().fileSystem().readFileBlocking(LicenseUtil.LICENSE_PATH).getBytes());

                                                LOGGER.info("lastUsage : " + lastUsage.encodePrettily() + "\n" + "current time : " + System.currentTimeMillis());

                                                if (lastUsage.containsKey(LICENSE_LAST_USAGE_TIME) && System.currentTimeMillis() >= lastUsage.getInteger(LICENSE_LAST_USAGE_TIME))
                                                {
                                                    promise.complete(license);
                                                }

                                                else
                                                {
                                                    LOGGER.fatal(String.format(FAILED_TO_VALIDATE_LICENSE, LicenseError.INVALID_SYSTEM_TIME.getName()));

                                                    promise.fail(LicenseError.INVALID_SYSTEM_TIME.getName());
                                                }
                                            }

                                            else
                                            {
                                                promise.complete(license);
                                            }
                                        }

                                        else
                                        {
                                            LOGGER.fatal("License is Expired. Please contact '<EMAIL>' for Renewal or Extension.");

                                            promise.fail(LicenseError.EXPIRED.getName());
                                        }
                                    }

                                    else
                                    {
                                        LOGGER.fatal(String.format(FAILED_TO_VALIDATE_LICENSE, LicenseError.EXPIRY_DATE_MISSING.getName()));

                                        promise.fail(LicenseError.EXPIRY_DATE_MISSING.getName());
                                    }
                                }

                                else
                                {
                                    LOGGER.fatal(String.format(FAILED_TO_VALIDATE_LICENSE, LicenseError.ISSUE_DATE_MISSING.getName()));

                                    promise.fail(LicenseError.ISSUE_DATE_MISSING.getName());
                                }
                            }

                            else
                            {
                                LOGGER.fatal(String.format(FAILED_TO_VALIDATE_LICENSE, LicenseError.INVALID_LICENSE_KEY.getName()));

                                promise.fail(LicenseError.INVALID_LICENSE_KEY.getName());
                            }
                        }

                        else
                        {
                            LOGGER.fatal(String.format(FAILED_TO_VALIDATE_LICENSE, LicenseError.LICENSE_KEY_MISSING.getName()));

                            promise.fail(LicenseError.LICENSE_KEY_MISSING.getName());
                        }
                    }

                    else
                    {
                        LOGGER.fatal(String.format(FAILED_TO_VALIDATE_LICENSE, LicenseError.INVALID_FILE.getName()));

                        promise.fail(LicenseError.INVALID_FILE.getName());
                    }
                }

                else
                {
                    LOGGER.fatal(String.format(FAILED_TO_VALIDATE_LICENSE, LicenseError.INVALID_FILE.getName()));

                    promise.fail(LicenseError.INVALID_FILE.getName());
                }
            }

            else
            {
                // if license file is missing then 2 possibilities ...
                // 1 ) fresh installation or
                // 2 ) file is deleted ...

                // let's check system table to verify its fresh installation or not

                if (freshInstallation)
                {
                    var license = generateTrialLicense();

                    if (!license.isEmpty())
                    {
                        promise.complete(license);
                    }
                    else
                    {
                        LOGGER.fatal(String.format(FAILED_TO_VALIDATE_LICENSE, LicenseError.HARDWARE_ADDRESS_NOT_FOUND.getName()));

                        promise.fail(LicenseError.HARDWARE_ADDRESS_NOT_FOUND.getName());
                    }

                }

                else    // file is deleted
                {
                    LOGGER.fatal(String.format(FAILED_TO_VALIDATE_LICENSE, LicenseError.FILE_NOT_FOUND.getName()));

                    promise.fail(LicenseError.FILE_NOT_FOUND.getName());
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.fatal("error while reading license file...");

            promise.fail(exception.getMessage());

            LOGGER.error(exception);
        }

        return promise.future();
    }

    private static List<String> getHardwareAddresses()
    {
        var hardwareAddresses = new ArrayList<String>();

        try
        {
            var interfaces = NetworkInterface.getNetworkInterfaces();

            var hardwareId = new StringBuilder();

            while (interfaces.hasMoreElements())
            {
                var network = interfaces.nextElement();

                if (!network.isLoopback() && network.isUp())
                {
                    var hardwareAddress = network.getHardwareAddress();

                    if (hardwareAddress != null)
                    {
                        for (var index = 0; index < hardwareAddress.length; index++)
                        {
                            hardwareId.append(String.format("%02X%s", hardwareAddress[index], (index < hardwareAddress.length - 1) ? ":" : GlobalConstants.EMPTY_VALUE));
                        }

                        if (!hardwareId.isEmpty() && !hardwareId.toString().contains("00:00:00:00"))
                        {
                            LOGGER.debug(String.format("hardware id of %s is %s", network.getName(), hardwareId));

                            hardwareAddresses.add(hardwareId.toString().trim().toUpperCase());
                        }

                        hardwareId.setLength(0);
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return hardwareAddresses;
    }

    public static void writeLicenseUsage()
    {
        Bootstrap.vertx().executeBlocking(future ->
        {
            try
            {
                Bootstrap.vertx().fileSystem().writeFileBlocking(LICENSE_PATH, Buffer.buffer(CodecUtil.compress(new JsonObject().put("used.log.quota.bytes", USED_LOG_QUOTA_BYTES.get()).put("used.flow.quota.bytes", USED_FLOW_QUOTA_BYTES.get()).put(LicenseUtil.LICENSE_LAST_USAGE_TIME, System.currentTimeMillis()).encode())));

                if (Bootstrap.bootstrapType() == BootstrapType.APP && Bootstrap.getInstallationMode().equalsIgnoreCase(InstallationMode.PRIMARY.name()))
                {
                    HAConstants.notifyObserver(new JsonObject().put(CACHE_NAME, "license-last-usage").put(RESULT, vertx().fileSystem().readFileBlocking(LICENSE_PATH)));
                }

                if (CommonUtil.debugEnabled())
                {
                    LOGGER.debug("license last usage time updated successfully...");
                }

                future.complete();
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                future.fail(exception);
            }

        }, result ->
        {
        });
    }

    private static String encrypt(String value)
    {
        String result = null;

        try
        {
            var cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");

            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(secretKeySpecBytes, "AES"), new IvParameterSpec(ivParameterSpecBytes));

            result = Base64.encodeBase64String(cipher.doFinal(value.getBytes()));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }

    private static String decrypt(String value)
    {
        String result = null;

        try
        {
            var cipher = Cipher.getInstance("AES/CBC/PKCS5PADDING");

            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(secretKeySpecBytes, "AES"), new IvParameterSpec(ivParameterSpecBytes));

            result = new String(cipher.doFinal(Base64.decodeBase64(value)));
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return result;
    }

    /**
     * check if monitor can be provisioned
     * <p>
     * Any validation or new logic will be written here to change the nature of Monitor Licensing
     */
    public static boolean licensedObjectConsumed() // Monitor License = Objects + Apps + Agents + VMs
    {
        return LicenseCacheStore.getStore().getConsumedLicenses() < LicenseUtil.LICENSED_OBJECTS.get();
    }

    /**
     * @param type type of metric or type of rediscover job
     * @return available Monitor Count for that instant
     */
    public static long getRemainingObjects(String type)
    {
        if (NMSConstants.isAccessPointMetric(type) || NMSConstants.RediscoverJob.ACCESS_POINT.getName().equalsIgnoreCase(type))
        {
            return InstanceLicense.ACCESS_POINT.getValue() * (LicenseUtil.LICENSED_OBJECTS.get() - LicenseCacheStore.getStore().getConsumedLicenses());
        }

        return LicenseUtil.LICENSED_OBJECTS.get() - LicenseCacheStore.getStore().getConsumedLicenses();
    }

    /**
     * Check if config devices can be provisioned
     * <p>
     * Any validation or new logic will be written here to change the nature of Config Licensing
     */
    public static boolean licensedConfigConsumed()
    {
        return LicenseCacheStore.getStore().getConsumedConfigLicenses() < LicenseUtil.LICENSED_CONFIG_DEVICES.get();
    }

    public static String replacePlaceholders(String template, String eventType)
    {
        var message = EMPTY_VALUE;

        for (var placeholder : PLACEHOLDERS)
        {
            if (placeholder.equalsIgnoreCase("$$$event.type$$$"))
            {
                template = template.replace(placeholder, eventType);
            }

            if (eventType.equalsIgnoreCase(PolicyEngineConstants.PolicyType.FLOW.getName()))
            {
                if (placeholder.equalsIgnoreCase("$$$event.user.limit$$$"))
                {
                    template = template.replace(placeholder, CommonUtil.getString(CommonUtil.getDouble(USED_FLOW_QUOTA_BYTES.get() / **********.0)));
                }
                else if (placeholder.equalsIgnoreCase("$$$event.license.limit$$$"))
                {
                    template = template.replace(placeholder, CommonUtil.getString(CommonUtil.getDouble(LICENSED_FLOW_QUOTA_BYTES.get() / **********.0)));

                }
            }
            else
            {
                if (placeholder.equalsIgnoreCase("$$$event.user.limit$$$"))
                {
                    template = template.replace(placeholder, CommonUtil.getString(CommonUtil.getDouble(USED_LOG_QUOTA_BYTES.get() / **********.0)));

                }
                else if (placeholder.equalsIgnoreCase("$$$event.license.limit$$$"))
                {
                    template = template.replace(placeholder, CommonUtil.getString(CommonUtil.getDouble(LICENSED_LOG_QUOTA_BYTES.get() / **********.0)));
                }
            }
        }

        message = template;

        return message;
    }

    private static void notify(String eventType, String mailTemplate, String smsTemplate, String subject)
    {
        var user = UserConfigStore.getStore().getItem(DEFAULT_ID, false);

        if (user.containsKey(User.USER_EMAIL) && user.getString(User.USER_EMAIL) != null)
        {
            if (eventType.equalsIgnoreCase(PolicyEngineConstants.PolicyType.LOG.getName()))
            {
                Notification.sendEmail(new JsonObject()
                        .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                        .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add("warning.png").add("information.png").add("critical.png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                        .put(Notification.EMAIL_NOTIFICATION_SUBJECT, String.format(subject, eventType))
                        .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, new JsonArray().add(user.getString(User.USER_EMAIL))).put(Notification.EMAIL_NOTIFICATION_CONTENT, new StringSubstitutor(new JsonObject().put(EVENT_TYPE, eventType).put("event.license.limit", CommonUtil.getDouble(LICENSED_LOG_QUOTA_BYTES.get() / **********.0)).put("event.user.limit", CommonUtil.getDouble(USED_LOG_QUOTA_BYTES.get() / **********.0)).getMap()).replace(mailTemplate).getBytes(StandardCharsets.UTF_8)));
            }

            else
            {
                Notification.sendEmail(new JsonObject()
                        .put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_DISPOSITION_TYPE, "inline").put(Notification.EMAIL_NOTIFICATION_ATTACHMENT_TYPE, "image/png")
                        .put(Notification.EMAIL_NOTIFICATION_ATTACHMENTS, new JsonArray().add("warning.png").add("information.png").add("critical.png").addAll(Notification.EMAIL_NOTIFICATION_INLINE_ATTACHMENT_ICONS))
                        .put(Notification.EMAIL_NOTIFICATION_SUBJECT, String.format(subject, eventType))
                        .put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, new JsonArray().add(user.getString(User.USER_EMAIL))).put(Notification.EMAIL_NOTIFICATION_CONTENT, new StringSubstitutor(new JsonObject().put(EVENT_TYPE, eventType).put("event.license.limit", CommonUtil.getDouble(LICENSED_FLOW_QUOTA_BYTES.get() / **********.0)).put("event.user.limit", CommonUtil.getDouble(USED_FLOW_QUOTA_BYTES.get() / **********.0)).getMap()).replace(mailTemplate).getBytes(StandardCharsets.UTF_8)));
            }

        }

        if (user.containsKey(User.USER_MOBILE) && user.getString(User.USER_MOBILE) != null && !user.getString(User.USER_MOBILE).equalsIgnoreCase("0"))
        {
            Notification.sendSMS(replacePlaceholders(smsTemplate, eventType), new JsonArray().add(user.getString(User.USER_MOBILE)));
        }
    }

    public enum LicenseType
    {
        FREE,

        SUBSCRIPTION,

        PERPETUAL
    }

    public enum LicenseEdition
    {
        ESSENTIAL(0, "Essential Edition"),

        HYBRID(1, "Hybrid Edition"),

        OBSERVABILITY(2, "Observability Edition"),

        HYBRID_LITE(3, "Hybrid Lite Edition");

        // using code instead of ordinal to avoid code changes impact.

        private final int code;

        private final String name;

        LicenseEdition(int code, String name)
        {
            this.code = code;
            this.name = name;
        }

        public String getName()
        {
            return name;
        }

        public int getCode()
        {
            return code;
        }
    }

    public enum LicenseError
    {
        EXPIRED("Expired"),

        LICENSE_KEY_MISSING("License Key is Missing"),

        EXPIRY_DATE_MISSING("Expiry Date is Missing"),

        ISSUE_DATE_MISSING("Issue Date is Missing"),

        INVALID_LICENSE_KEY("Invalid Hardware Key"),

        INVALID_FILE("Invalid File"),

        FILE_NOT_FOUND("File Not Found"),

        HARDWARE_ADDRESS_NOT_FOUND("Hardware Address Not Found"),

        FREE_LICENSE_ERROR("Free License Error"),

        INVALID_ISSUE_TIME("Invalid Issued Time"),

        INVALID_SYSTEM_TIME("Invalid System Time");

        private static final Map<String, LicenseError> VALUES = Collections.unmodifiableMap(Arrays.stream(values()).collect(Collectors.toMap(LicenseError::getName, e -> e)));
        private final String name;

        LicenseError(String name)
        {
            this.name = name;
        }

        public static LicenseError valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }

    }

    /*
     *   LicenseValue will determine, value of each instance as compare to one monitor license
     *
     *   example:
     *       2 access point --> 1 monitor (1 or 2 access point will consume same 1 license)
     * */
    public enum InstanceLicense
    {
        ACCESS_POINT(2);

        private final int value;

        InstanceLicense(int value)
        {
            this.value = value;
        }

        public int getValue()
        {
            return value;
        }
    }
}
