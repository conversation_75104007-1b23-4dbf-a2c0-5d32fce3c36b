/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *	26-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 *  28-Feb-2025		Bharat Chaudhari    MOTADATA-5233: Updated getTimeStampFromEpochTimestamp to handle milliseconds conversion also as it's only converting second to date.
 *  25-Mar-2025		Chopra Deven 		MOTADATA-5299: modified method millisToDuration() to get timeline in minutes when its required.
 *
 */

package com.mindarray.util;

import com.mindarray.GlobalConstants;
import com.mindarray.api.User;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.json.JsonObject;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.TimeZone;
import java.util.concurrent.TimeUnit;

/**
 * Utility class for date and time operations in the Motadata application.
 * <p>
 * This class provides methods for:
 * - Timestamp formatting and parsing
 * - Time unit conversion (milliseconds, seconds, etc.)
 * - Timeline building for visualization components
 * - Date/time calculations with timezone support
 * - Duration formatting
 * - Rounding time values
 * <p>
 * The class uses both Java's standard date/time APIs and the Joda Time library
 * for comprehensive date and time manipulation capabilities.
 */
public final class DateTimeUtil
{
    /**
     * Constant representing the "day" time unit
     */
    public static final String DAY = "day";

    /**
     * Constant representing the "hour" time unit
     */
    public static final String HOUR = "hour";

    /**
     * Constant representing the "minute" time unit
     */
    public static final String MINUTE = "minute";

    /**
     * Constant representing the "second" time unit
     */
    public static final String SECOND = "second";

    /**
     * Standard timestamp format used throughout the application (yyyy/MM/dd HH:mm:ss)
     */
    public static final String TIMESTAMP_FORMAT = "yyyy/MM/dd HH:mm:ss";

    /**
     * Date format used for correlated metric cache files (yyyy-MM-dd)
     */
    public static final String CORRELATED_METRIC_CACHE_FILE_TIMESTAMP_FORMAT = "yyyy-MM-dd";

    /**
     * Logger instance for this class
     */
    private static final Logger LOGGER = new Logger(DateTimeUtil.class, GlobalConstants.MOTADATA_UTIL, "DateTime Util");

    /**
     * DateTimeFormatter for the standard timestamp format
     */
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormat.forPattern(TIMESTAMP_FORMAT);

    /**
     * DateTimeFormatter for scheduled date and time (dd-MM-yyyy HH:mm:ss)
     */
    private static final DateTimeFormatter SCHEDULED_DATE_TIME_FORMATTER = DateTimeFormat.forPattern("dd-MM-yyyy HH:mm:ss");

    /**
     * DateTimeFormatter for scheduled date only (dd-MM-yyyy)
     */
    private static final DateTimeFormatter SCHEDULED_DATE_FORMATTER = DateTimeFormat.forPattern("dd-MM-yyyy");

    /**
     * Format for displaying day, date and time with AM/PM (EEE, MMM dd, yyyy hh:mm:ss a)
     */
    private static final String DAY_DATE_TIME_FORMAT = "EEE, MMM dd, yyyy hh:mm:ss a";

    /**
     * Private constructor to prevent instantiation of this utility class.
     */
    private DateTimeUtil()
    {
    }

    /**
     * Returns the current date and time formatted as a string using the standard timestamp format.
     *
     * @return Current date and time as a formatted string (yyyy/MM/dd HH:mm:ss)
     */
    public static String timestamp()
    {
        return new SimpleDateFormat(TIMESTAMP_FORMAT).format(new Date());
    }

    /**
     * Converts a timestamp in milliseconds to a formatted date/time string.
     *
     * @param millis The timestamp in milliseconds since epoch
     * @return Formatted date and time string (yyyy/MM/dd HH:mm:ss)
     */
    public static String timestamp(long millis)
    {
        return DATE_TIME_FORMATTER.print(millis);
    }

    /**
     * Formats a timestamp in milliseconds using the scheduled date/time format.
     *
     * @param millis The timestamp in milliseconds since epoch
     * @return Formatted date and time string (dd-MM-yyyy HH:mm:ss)
     */
    public static String getScheduledTimestamp(long millis)
    {
        return SCHEDULED_DATE_TIME_FORMATTER.print(millis);
    }

    /**
     * Returns the current time in milliseconds since epoch (January 1, 1970, 00:00:00 GMT).
     *
     * @return Current time in milliseconds
     */
    public static long currentMilliSeconds()
    {
        return System.currentTimeMillis();
    }

    /**
     * Returns the current time in seconds since epoch (January 1, 1970, 00:00:00 GMT).
     *
     * @return Current time in seconds
     */
    public static long currentSeconds()
    {
        return System.currentTimeMillis() / 1000;
    }

    /**
     * Calculates the time difference between the current time and a specified time.
     *
     * @param time The time in seconds since epoch to compare against current time
     * @return The difference in seconds between current time and the specified time
     */
    public static long convertTime(long time)
    {
        return DateTimeUtil.currentSeconds() - time;
    }

    /**
     * Creates a SimpleDateFormat with the specified pattern and user's timezone preference.
     * <p>
     * This method:
     * 1. Converts some pattern characters to Java-compatible format (ddd→EEE, DD→dd, A→a)
     * 2. Sets the timezone based on user preferences
     * 3. Falls back to a default format if pattern is null or an error occurs
     *
     * @param pattern The date/time pattern to use for formatting
     * @param user    A JsonObject containing user information including timezone preferences
     * @return A SimpleDateFormat configured with the specified pattern and user's timezone
     */
    public static SimpleDateFormat getDateFormat(String pattern, JsonObject user)
    {
        try
        {
            if (pattern != null)
            {
                var preferences = user.getJsonObject(User.USER_PREFERENCES);

                // Convert pattern characters to Java-compatible format
                var dateFormat = new SimpleDateFormat(pattern.replaceAll("ddd", "EEE").replaceAll("DD", "dd").replaceAll("A", "a"));

                // Set timezone based on user preferences or default if not specified
                dateFormat.setTimeZone(preferences.getString(User.USER_PREFERENCE_TIME_ZONE) != null ?
                        TimeZone.getTimeZone(preferences.getString(User.USER_PREFERENCE_TIME_ZONE)) :
                        TimeZone.getDefault());

                return dateFormat;
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        // Return default format if pattern is null or an error occurs
        return new SimpleDateFormat(DAY_DATE_TIME_FORMAT);
    }

    /**
     * Converts an epoch timestamp to a human-readable date/time string.
     * <p>
     * This method formats the timestamp using the DAY_DATE_TIME_FORMAT pattern
     * (EEE, MMM dd, yyyy hh:mm:ss a).
     *
     * @param epochTimestamp   The timestamp to convert (in milliseconds or seconds since epoch)
     * @param convertToSeconds If true, treats the input as seconds and converts to milliseconds;
     *                         if false, treats the input as already in milliseconds
     * @return A formatted date/time string
     */
    public static String getTimeStampFromEpochTimestamp(Long epochTimestamp, boolean convertToSeconds)
    {
        // Convert seconds to milliseconds if needed
        long millisTimestamp = convertToSeconds ? epochTimestamp * 1000 : epochTimestamp;

        return new SimpleDateFormat(DAY_DATE_TIME_FORMAT).format(new Date(millisTimestamp));
    }

    /**
     * Builds a timeline for visualization based on user preferences and specified parameters.
     * <p>
     * This method calculates start and end timestamps for various timeline types:
     * - Predefined periods (LAST_5_MINUTES, TODAY, YESTERDAY, LAST_WEEK, etc.)
     * - Custom date ranges
     * - Relative time periods (e.g., "-30m", "-2h", "-1d")
     * <p>
     * The method handles timezone conversions based on user preferences and
     * supports optional time overrides through fromTime and toTime parameters.
     *
     * @param timeline A JsonObject containing timeline configuration
     * @param context  A JsonObject containing context information that may include timeline data
     * @param user     A JsonObject containing user information including timezone preferences
     */
    public static void buildTimeline(JsonObject timeline, JsonObject context, JsonObject user)
    {
        var result = new JsonObject();

        try
        {
            // Get user's timezone preference
            var preferences = user.getJsonObject(User.USER_PREFERENCES);
            var timeZone = preferences.getString(User.USER_PREFERENCE_TIME_ZONE, DateTimeZone.getDefault().getID());

            // Initialize start and end date/time with current time in user's timezone
            var startDateTime = new DateTime().withZone(DateTimeZone.forID(timeZone));
            var endDateTime = new DateTime().withZone(DateTimeZone.forID(timeZone));

            // Use context as timeline if it contains visualization timeline data
            timeline = context.containsKey(VisualizationConstants.VISUALIZATION_TIMELINE) ? context : timeline;

            // Get the relative timeline setting (e.g., "LAST_24_HOURS", "TODAY", "CUSTOM")
            var relativeTimeline = timeline.getJsonObject(VisualizationConstants.VISUALIZATION_TIMELINE).getString(VisualizationConstants.RELATIVE_TIMELINE);

            // Store the timezone in the result
            result.put(VisualizationConstants.VISUALIZATION_TIMEZONE, timeZone);

            // Convert the timeline string to an enum value for switch statement
            var timelineType = VisualizationConstants.VisualizationTimeline.valueOfName(relativeTimeline);

            String fromTime = null;

            String toTime = null;

            if (timeline.getJsonObject(VisualizationConstants.VISUALIZATION_TIMELINE).containsKey(VisualizationConstants.FROM_TIME))
            {
                fromTime = timeline.getJsonObject(VisualizationConstants.VISUALIZATION_TIMELINE).getString(VisualizationConstants.FROM_TIME).trim();
            }

            if (timeline.getJsonObject(VisualizationConstants.VISUALIZATION_TIMELINE).containsKey(VisualizationConstants.TO_TIME))
            {
                toTime = timeline.getJsonObject(VisualizationConstants.VISUALIZATION_TIMELINE).getString(VisualizationConstants.TO_TIME).trim();
            }

            if (timelineType != null)//default timelines from widget
            {
                switch (timelineType)
                {
                    case LAST_5_MINUTES, LAST_15_MINUTES, LAST_30_MINUTES, LAST_1_HOUR, LAST_6_HOURS, LAST_12_HOURS,
                         LAST_24_HOURS, LAST_48_HOURS, LAST_7_DAYS, LAST_14_DAYS, LAST_15_DAYS, LAST_30_DAYS,
                         LAST_60_DAYS, LAST_90_DAYS, LAST_DAY ->
                    {
                        if (fromTime != null)
                        {
                            startDateTime = startDateTime.withHourOfDay(CommonUtil.getInteger(fromTime.split(":")[0])).withMinuteOfHour(CommonUtil.getInteger(fromTime.split(":")[1])).withZone(DateTimeZone.forID(timeZone));
                        }
                        else
                        {
                            startDateTime = startDateTime.minusMinutes(timelineType.getDuration()).withZone(DateTimeZone.forID(timeZone));
                        }

                        if (toTime != null)
                        {
                            endDateTime = endDateTime.withHourOfDay(CommonUtil.getInteger(toTime.split(":")[0])).withMinuteOfHour(CommonUtil.getInteger(toTime.split(":")[1])).withZone(DateTimeZone.forID(timeZone));
                        }

                        result.put(VisualizationConstants.FROM_DATETIME, startDateTime.getMillis());

                        result.put(VisualizationConstants.TO_DATETIME, endDateTime.getMillis());
                    }

                    case TODAY ->
                    {
                        if (fromTime != null)
                        {
                            startDateTime = startDateTime.withHourOfDay(CommonUtil.getInteger(fromTime.split(":")[0])).withMinuteOfHour(CommonUtil.getInteger(fromTime.split(":")[1])).withZone(DateTimeZone.forID(timeZone));
                        }
                        else
                        {
                            startDateTime = startDateTime.withHourOfDay(0).withMinuteOfHour(0).withSecondOfMinute(0).withMillisOfSecond(0).withZone(DateTimeZone.forID(timeZone));
                        }

                        if (toTime != null)
                        {
                            endDateTime = endDateTime.withHourOfDay(CommonUtil.getInteger(toTime.split(":")[0])).withMinuteOfHour(CommonUtil.getInteger(toTime.split(":")[1]));
                        }
                        else
                        {
                            endDateTime = endDateTime.withZone(DateTimeZone.forID(timeZone));
                        }

                        result.put(VisualizationConstants.FROM_DATETIME, startDateTime.getMillis());

                        result.put(VisualizationConstants.TO_DATETIME, endDateTime.getMillis());
                    }

                    case YESTERDAY ->
                    {

                        if (fromTime != null)
                        {
                            startDateTime = startDateTime.minusDays(1).withHourOfDay(CommonUtil.getInteger(fromTime.split(":")[0])).withMinuteOfHour(CommonUtil.getInteger(fromTime.split(":")[1])).withSecondOfMinute(0).withMillisOfSecond(0).withZone(DateTimeZone.forID(timeZone));
                        }
                        else
                        {
                            startDateTime = startDateTime.minusDays(1).withHourOfDay(0).withMinuteOfHour(0).withSecondOfMinute(0).withMillisOfSecond(0).withZone(DateTimeZone.forID(timeZone));
                        }

                        if (toTime != null)
                        {
                            endDateTime = endDateTime.withHourOfDay(CommonUtil.getInteger(toTime.split(":")[0])).withMinuteOfHour(CommonUtil.getInteger(toTime.split(":")[1])).withSecondOfMinute(59).minusDays(1).withZone(DateTimeZone.forID(timeZone));
                        }
                        else
                        {
                            endDateTime = endDateTime.withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59).minusDays(1).withZone(DateTimeZone.forID(timeZone));
                        }

                        result.put(VisualizationConstants.FROM_DATETIME, startDateTime.getMillis());

                        result.put(VisualizationConstants.TO_DATETIME, endDateTime.getMillis());
                    }

                    case LAST_WEEK ->
                    {
                        if (fromTime != null)
                        {
                            startDateTime = startDateTime.minusWeeks(1).withDayOfWeek(1).withHourOfDay(CommonUtil.getInteger(fromTime.split(":")[0])).withMinuteOfHour(CommonUtil.getInteger(fromTime.split(":")[1])).withSecondOfMinute(0).withMillisOfSecond(0).withZone(DateTimeZone.forID(timeZone));
                        }
                        else
                        {
                            startDateTime = startDateTime.minusWeeks(1).withDayOfWeek(1).withHourOfDay(0).withMinuteOfHour(0).withSecondOfMinute(0).withMillisOfSecond(0).withZone(DateTimeZone.forID(timeZone));
                        }

                        if (toTime != null)
                        {
                            endDateTime = endDateTime.minusWeeks(1).withDayOfWeek(startDateTime.dayOfWeek().getMaximumValue()).withHourOfDay(CommonUtil.getInteger(toTime.split(":")[0])).withMinuteOfHour(CommonUtil.getInteger(toTime.split(":")[1])).withSecondOfMinute(59).withZone(DateTimeZone.forID(timeZone));
                        }
                        else
                        {
                            endDateTime = endDateTime.minusWeeks(1).withDayOfWeek(startDateTime.dayOfWeek().getMaximumValue()).withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59).withZone(DateTimeZone.forID(timeZone));
                        }

                        result.put(VisualizationConstants.FROM_DATETIME, startDateTime.getMillis());

                        result.put(VisualizationConstants.TO_DATETIME, endDateTime.getMillis());
                    }

                    case LAST_MONTH ->
                    {

                        if (fromTime != null)
                        {
                            startDateTime = startDateTime.minusMonths(1).withDayOfMonth(1).withHourOfDay(CommonUtil.getInteger(fromTime.split(":")[0])).withMinuteOfHour(CommonUtil.getInteger(fromTime.split(":")[1])).withSecondOfMinute(0).withMillisOfSecond(0).withZone(DateTimeZone.forID(timeZone));
                        }
                        else
                        {
                            startDateTime = startDateTime.minusMonths(1).withDayOfMonth(1).withHourOfDay(0).withMinuteOfHour(0).withSecondOfMinute(0).withMillisOfSecond(0).withZone(DateTimeZone.forID(timeZone));
                        }

                        if (toTime != null)
                        {
                            endDateTime = endDateTime.minusMonths(1).withDayOfMonth(startDateTime.dayOfMonth().getMaximumValue()).withHourOfDay(CommonUtil.getInteger(toTime.split(":")[0])).withMinuteOfHour(CommonUtil.getInteger(toTime.split(":")[1])).withSecondOfMinute(59).withZone(DateTimeZone.forID(timeZone));
                        }
                        else
                        {
                            endDateTime = endDateTime.minusMonths(1).withDayOfMonth(startDateTime.dayOfMonth().getMaximumValue()).withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59).withZone(DateTimeZone.forID(timeZone));
                        }

                        result.put(VisualizationConstants.FROM_DATETIME, startDateTime.getMillis());

                        result.put(VisualizationConstants.TO_DATETIME, endDateTime.getMillis());
                    }

                    case LAST_QUARTER ->
                    {

                        startDateTime = startDateTime.minusMonths(3);

                        if (fromTime != null)
                        {
                            startDateTime = startDateTime.withMonthOfYear((((startDateTime.monthOfYear().get() - 1) / 3) * 3) + 1).withDayOfMonth(1).withHourOfDay(CommonUtil.getInteger(fromTime.split(":")[0])).withMinuteOfHour(CommonUtil.getInteger(fromTime.split(":")[1])).withSecondOfMinute(0).withMillisOfSecond(0).withZone(DateTimeZone.forID(timeZone));
                        }
                        else
                        {
                            startDateTime = startDateTime.withMonthOfYear((((startDateTime.monthOfYear().get() - 1) / 3) * 3) + 1).withDayOfMonth(1).withHourOfDay(0).withMinuteOfHour(0).withSecondOfMinute(0).withMillisOfSecond(0).withZone(DateTimeZone.forID(timeZone));
                        }

                        endDateTime = endDateTime.minusMonths(3);

                        endDateTime = endDateTime.withMonthOfYear(startDateTime.getMonthOfYear() + 2);

                        if (toTime != null)
                        {
                            endDateTime = endDateTime.withDayOfMonth(endDateTime.dayOfMonth().getMaximumValue()).withHourOfDay(CommonUtil.getInteger(toTime.split(":")[0])).withMinuteOfHour(CommonUtil.getInteger(toTime.split(":")[1])).withSecondOfMinute(59).withZone(DateTimeZone.forID(timeZone));
                        }
                        else
                        {
                            endDateTime = endDateTime.withDayOfMonth(endDateTime.dayOfMonth().getMaximumValue()).withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59).withZone(DateTimeZone.forID(timeZone));
                        }


                        result.put(VisualizationConstants.FROM_DATETIME, startDateTime.getMillis());

                        result.put(VisualizationConstants.TO_DATETIME, endDateTime.getMillis());
                    }

                    case LAST_YEAR ->
                    {

                        if (fromTime != null)
                        {
                            startDateTime = startDateTime.minusYears(1).withMonthOfYear(1).withDayOfMonth(1).withHourOfDay(CommonUtil.getInteger(fromTime.split(":")[0])).withMinuteOfHour(CommonUtil.getInteger(fromTime.split(":")[1])).withSecondOfMinute(0).withMillisOfSecond(0).withZone(DateTimeZone.forID(timeZone));
                        }
                        else
                        {
                            startDateTime = startDateTime.minusYears(1).withMonthOfYear(1).withDayOfMonth(1).withHourOfDay(0).withMinuteOfHour(0).withSecondOfMinute(0).withMillisOfSecond(0).withZone(DateTimeZone.forID(timeZone));
                        }

                        endDateTime = endDateTime.minusYears(1).withMonthOfYear(endDateTime.monthOfYear().getMaximumValue());

                        if (toTime != null)
                        {
                            endDateTime = endDateTime.withDayOfMonth(endDateTime.dayOfMonth().getMaximumValue()).withHourOfDay(CommonUtil.getInteger(toTime.split(":")[0])).withMinuteOfHour(CommonUtil.getInteger(toTime.split(":")[1])).withSecondOfMinute(59).withZone(DateTimeZone.forID(timeZone));
                        }
                        else
                        {
                            endDateTime = endDateTime.withDayOfMonth(endDateTime.dayOfMonth().getMaximumValue()).withHourOfDay(23).withMinuteOfHour(59).withSecondOfMinute(59).withZone(DateTimeZone.forID(timeZone));
                        }

                        result.put(VisualizationConstants.FROM_DATETIME, startDateTime.getMillis());

                        result.put(VisualizationConstants.TO_DATETIME, endDateTime.getMillis());
                    }

                    case THIS_WEEK ->
                    {

                        if (fromTime != null)
                        {
                            startDateTime = startDateTime.withDayOfWeek(1).withHourOfDay(CommonUtil.getInteger(fromTime.split(":")[0])).withMinuteOfHour(CommonUtil.getInteger(fromTime.split(":")[1])).withSecondOfMinute(0).withMillisOfSecond(0).withZone(DateTimeZone.forID(timeZone));
                        }
                        else
                        {
                            startDateTime = startDateTime.withDayOfWeek(1).withHourOfDay(0).withMinuteOfHour(0).withSecondOfMinute(0).withMillisOfSecond(0).withZone(DateTimeZone.forID(timeZone));
                        }

                        if (toTime != null)
                        {
                            endDateTime = endDateTime.withHourOfDay(CommonUtil.getInteger(toTime.split(":")[0])).withMinuteOfHour(CommonUtil.getInteger(toTime.split(":")[1])).withZone(DateTimeZone.forID(timeZone));
                        }
                        else
                        {
                            endDateTime = endDateTime.withZone(DateTimeZone.forID(timeZone));
                        }

                        result.put(VisualizationConstants.FROM_DATETIME, startDateTime.getMillis());

                        result.put(VisualizationConstants.TO_DATETIME, endDateTime.getMillis());
                    }

                    case THIS_MONTH ->
                    {
                        if (fromTime != null)
                        {
                            startDateTime = startDateTime.withDayOfMonth(1).withHourOfDay(CommonUtil.getInteger(fromTime.split(":")[0])).withMinuteOfHour(CommonUtil.getInteger(fromTime.split(":")[1])).withSecondOfMinute(0).withMillisOfSecond(0).withZone(DateTimeZone.forID(timeZone));
                        }
                        else
                        {
                            startDateTime = startDateTime.withDayOfMonth(1).withHourOfDay(0).withMinuteOfHour(0).withSecondOfMinute(0).withMillisOfSecond(0).withZone(DateTimeZone.forID(timeZone));
                        }

                        if (toTime != null)
                        {
                            endDateTime = endDateTime.withHourOfDay(CommonUtil.getInteger(toTime.split(":")[0])).withMinuteOfHour(CommonUtil.getInteger(toTime.split(":")[1])).withZone(DateTimeZone.forID(timeZone));
                        }
                        else
                        {
                            endDateTime = endDateTime.withZone(DateTimeZone.forID(timeZone));
                        }

                        result.put(VisualizationConstants.FROM_DATETIME, startDateTime.getMillis());

                        result.put(VisualizationConstants.TO_DATETIME, endDateTime.getMillis());
                    }

                    case THIS_QUARTER ->
                    {

                        // ref: java.time.Month.firstMonthOfQuarter()
                        if (fromTime != null)
                        {
                            startDateTime = startDateTime.withMonthOfYear((((startDateTime.monthOfYear().get() - 1) / 3) * 3) + 1).withDayOfMonth(1).withHourOfDay(CommonUtil.getInteger(fromTime.split(":")[0])).withMinuteOfHour(CommonUtil.getInteger(fromTime.split(":")[1])).withSecondOfMinute(0).withMillisOfSecond(0).withZone(DateTimeZone.forID(timeZone));
                        }
                        else
                        {
                            startDateTime = startDateTime.withMonthOfYear((((startDateTime.monthOfYear().get() - 1) / 3) * 3) + 1).withDayOfMonth(1).withHourOfDay(0).withMinuteOfHour(0).withSecondOfMinute(0).withMillisOfSecond(0).withZone(DateTimeZone.forID(timeZone));
                        }

                        if (toTime != null)
                        {
                            endDateTime = endDateTime.withHourOfDay(CommonUtil.getInteger(toTime.split(":")[0])).withMinuteOfHour(CommonUtil.getInteger(toTime.split(":")[1])).withZone(DateTimeZone.forID(timeZone));
                        }
                        else
                        {
                            endDateTime = endDateTime.withZone(DateTimeZone.forID(timeZone));
                        }

                        result.put(VisualizationConstants.FROM_DATETIME, startDateTime.getMillis());

                        result.put(VisualizationConstants.TO_DATETIME, endDateTime.getMillis());
                    }

                    case THIS_YEAR ->
                    {

                        if (fromTime != null)
                        {
                            startDateTime = startDateTime.withMonthOfYear(1).withDayOfMonth(1).withHourOfDay(CommonUtil.getInteger(fromTime.split(":")[0])).withMinuteOfHour(CommonUtil.getInteger(fromTime.split(":")[1])).withSecondOfMinute(0).withMillisOfSecond(0).withZone(DateTimeZone.forID(timeZone));
                        }
                        else
                        {
                            startDateTime = startDateTime.withMonthOfYear(1).withDayOfMonth(1).withHourOfDay(0).withMinuteOfHour(0).withSecondOfMinute(0).withMillisOfSecond(0).withZone(DateTimeZone.forID(timeZone));
                        }

                        if (toTime != null)
                        {
                            endDateTime = endDateTime.withHourOfDay(CommonUtil.getInteger(toTime.split(":")[0])).withMinuteOfHour(CommonUtil.getInteger(toTime.split(":")[1])).withZone(DateTimeZone.forID(timeZone));
                        }
                        else
                        {
                            endDateTime = endDateTime.withZone(DateTimeZone.forID(timeZone));
                        }

                        result.put(VisualizationConstants.FROM_DATETIME, startDateTime.getMillis());

                        result.put(VisualizationConstants.TO_DATETIME, endDateTime.getMillis());
                    }

                    case CUSTOM ->
                    {

                        startDateTime = DATE_TIME_FORMATTER.parseDateTime(timeline.getJsonObject(VisualizationConstants.VISUALIZATION_TIMELINE).getString(VisualizationConstants.FROM_DATE).trim().concat(" ").concat(timeline.getJsonObject(VisualizationConstants.VISUALIZATION_TIMELINE).getString(VisualizationConstants.FROM_TIME).trim())).withZone(DateTimeZone.forID(timeZone));

                        endDateTime = DATE_TIME_FORMATTER.parseDateTime(timeline.getJsonObject(VisualizationConstants.VISUALIZATION_TIMELINE).getString(VisualizationConstants.TO_DATE).trim().concat(" ").concat(timeline.getJsonObject(VisualizationConstants.VISUALIZATION_TIMELINE).getString(VisualizationConstants.TO_TIME).trim())).withZone(DateTimeZone.forID(timeZone));

                        result.put(VisualizationConstants.FROM_DATETIME, startDateTime.getMillis());

                        result.put(VisualizationConstants.TO_DATETIME, endDateTime.getMillis());
                    }

                    default ->
                    {
                    }
                }
            }
            else
            {
                if (relativeTimeline.endsWith("m"))
                {
                    startDateTime = startDateTime.minusMinutes(CommonUtil.getInteger(relativeTimeline.replace("-", "").replace("m", "").trim())).withZone(DateTimeZone.forID(timeZone));
                }
                else if (relativeTimeline.endsWith("h"))
                {
                    startDateTime = startDateTime.minusMinutes(CommonUtil.getInteger(relativeTimeline.replace("-", "").replace("h", "").trim()) * 60).withZone(DateTimeZone.forID(timeZone));
                }
                else if (relativeTimeline.endsWith("d"))
                {
                    startDateTime = startDateTime.minusMinutes(CommonUtil.getInteger(relativeTimeline.replace("-", "").replace("d", "").trim()) * 60 * 24).withZone(DateTimeZone.forID(timeZone));
                }
                else
                {
                    //default will be last 1 hour if nothing qualifies
                    startDateTime = startDateTime.minusMinutes(60).withZone(DateTimeZone.forID(timeZone));
                }

                result.put(VisualizationConstants.FROM_DATETIME, startDateTime.getMillis());

                result.put(VisualizationConstants.TO_DATETIME, endDateTime.withZone(DateTimeZone.forID(timeZone)).getMillis());
            }

            result.put(GlobalConstants.DURATION, TimeUnit.SECONDS.convert(Math.abs(startDateTime.getMillis() - endDateTime.getMillis()), TimeUnit.MILLISECONDS));

            result.put(VisualizationConstants.VISUALIZATION_TIME_RANGE_INCLUSIVE, GlobalConstants.YES);

            timeline.getJsonObject(VisualizationConstants.VISUALIZATION_TIMELINE).mergeIn(result);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Converts a formatted timestamp string to seconds since epoch.
     * <p>
     * This method handles incomplete date/time formats by automatically adding missing
     * components (year, month, day, hour, minute, second) from the current date/time.
     * It also supports timezone conversion if specified.
     *
     * @param format    The date/time format pattern
     * @param timestamp The timestamp string to parse
     * @param timezone  The timezone ID to use for conversion
     * @return Seconds since epoch representing the parsed timestamp
     */
    public static long timestampToSeconds(String format, String timestamp, String timezone)
    {
        if (format == null)
        {
            return currentSeconds();
        }

        var currentDate = DateTime.now();

        // Add missing date/time components from current date

        // Add year if missing
        if (!format.contains("yy"))
        {
            format = "yyyy " + format;
            timestamp = currentDate.getYear() + " " + timestamp;
        }

        // Add month if missing
        if (!format.contains("M"))
        {
            format = "MM " + format;
            timestamp = currentDate.getMonthOfYear() + " " + timestamp;
        }

        // Add day if missing
        if (!format.contains("d"))
        {
            format = "dd " + format;
            timestamp = currentDate.getDayOfMonth() + " " + timestamp;
        }

        // Add hour if missing
        if (!format.toLowerCase().contains("hh"))
        {
            format = "HH " + format;
            timestamp = currentDate.getHourOfDay() + " " + timestamp;
        }

        // Add minute if missing
        if (!format.contains("m"))
        {
            format = "mm " + format;
            timestamp = currentDate.getMinuteOfHour() + " " + timestamp;
        }

        // Add second if missing
        if (!format.contains("ss"))
        {
            format = "ss " + format;
            timestamp = currentDate.getSecondOfMinute() + " " + timestamp;
        }

        // Parse with timezone if specified and format doesn't already include timezone
        if ((!format.contains("Z") || !format.contains("z")) && !timezone.isEmpty())
        {
            return DateTimeFormat.forPattern(format).withZone(DateTimeZone.forID(timezone)).parseDateTime(timestamp).getMillis() / 1000;
        }

        // Parse without timezone conversion
        return DateTimeFormat.forPattern(format).parseDateTime(timestamp).getMillis() / 1000;
    }

    /**
     * Creates a formatted date/time range string from a context object.
     * <p>
     * This method extracts FROM_DATETIME and TO_DATETIME values from the context
     * and formats them as a range string (e.g., "2025/04/23 10:00:00 to 2025/04/23 11:00:00").
     *
     * @param context A JsonObject containing FROM_DATETIME and TO_DATETIME values in milliseconds
     * @return A formatted date/time range string, or null if the required values are missing
     */
    public static String getDateTime(JsonObject context)
    {
        String result = null;

        if (context.containsKey(VisualizationConstants.TO_DATETIME) && context.containsKey(VisualizationConstants.FROM_DATETIME))
        {
            result = DateTimeUtil.timestamp(context.getLong(VisualizationConstants.FROM_DATETIME)) +
                    " to " +
                    DateTimeUtil.timestamp(context.getLong(VisualizationConstants.TO_DATETIME));
        }

        return result;
    }

    /**
     * Rounds a timestamp to the nearest multiple of the specified hours.
     * <p>
     * This method truncates the timestamp to the hour and then rounds down to the nearest
     * multiple of the specified hours. For example, with hours=6:
     * - 2025-04-23 13:45:30 would round to 2025-04-23 12:00:00
     * - 2025-04-23 05:20:10 would round to 2025-04-23 00:00:00
     *
     * @param tick  The timestamp in milliseconds to round
     * @param hours The hour interval to round to
     * @return The rounded timestamp in milliseconds
     */
    public static long roundOffHours(long tick, int hours)
    {
        // Convert to seconds and truncate to the hour
        var seconds = Instant.ofEpochMilli(tick)
                .truncatedTo(ChronoUnit.HOURS)
                .atOffset(ZoneOffset.UTC)
                .toEpochSecond();

        // Round down to the nearest multiple of the specified hours and convert back to milliseconds
        return (seconds / 3600 - (seconds % (hours * 3600L))) * 3600 * 1000;
    }

    /**
     * Rounds a timestamp to the nearest multiple of the specified seconds.
     * <p>
     * This method handles two cases:
     * 1. If seconds >= 3600 (1 hour), it delegates to roundOffHours
     * 2. Otherwise, it truncates to the second and rounds down to the nearest multiple of the specified seconds
     *
     * @param tick    The timestamp in milliseconds to round
     * @param seconds The second interval to round to
     * @return The rounded timestamp in milliseconds
     */
    public static long roundOffSeconds(long tick, int seconds)
    {
        // If the interval is an hour or more, use roundOffHours
        if (seconds >= 3600)
        {
            return roundOffHours(tick, seconds / 3600);
        }

        // Convert to seconds and truncate to the second
        var unixSeconds = Instant.ofEpochMilli(tick)
                .truncatedTo(ChronoUnit.SECONDS)
                .atOffset(ZoneOffset.UTC)
                .toEpochSecond();

        // Round down to the nearest multiple of the specified seconds and convert back to milliseconds
        return (unixSeconds - (unixSeconds % seconds)) * 1000;
    }

    /**
     * Formats a timestamp using the scheduled date format (dd-MM-yyyy).
     *
     * @param millis The timestamp in milliseconds
     * @return A formatted date string in dd-MM-yyyy format
     */
    public static String getScheduledDate(long millis)
    {
        return SCHEDULED_DATE_FORMATTER.print(millis);
    }

    /**
     * Converts a duration in milliseconds to a human-readable string.
     * <p>
     * This method breaks down the duration into months, weeks, days, hours, and minutes,
     * and formats it as a string (e.g., "2 months 1 week 3 days 5 hours").
     * <p>
     * The method uses the following conversion rules:
     * - 30 days = 1 month
     * - 7 days = 1 week
     * - Minutes are only included if the duration is less than a day
     *
     * @param millis The duration in milliseconds
     * @return A formatted string representing the duration
     */
    public static String millisToDuration(long millis)
    {
        // Calculate duration components
        var months = TimeUnit.MILLISECONDS.toDays(millis) / 30;
        var weeks = (TimeUnit.MILLISECONDS.toDays(millis) % 30) / 7;
        var days = (TimeUnit.MILLISECONDS.toDays(millis) % 30) % 7;
        var hours = TimeUnit.MILLISECONDS.toHours(millis) % 24;
        var minutes = TimeUnit.MILLISECONDS.toMinutes(millis) % 60;

        var result = new StringBuilder();

        // Add each non-zero component to the result
        if (months > 0) result.append(months).append(" month").append(months > 1 ? "s " : " ");
        if (weeks > 0) result.append(weeks).append(" week").append(weeks > 1 ? "s " : " ");
        if (days > 0) result.append(days).append(" day").append(days > 1 ? "s " : " ");
        if (hours > 0) result.append(hours).append(" hour").append(hours > 1 ? "s " : " ");

        // Only include minutes if there are no larger units (months, weeks, days)
        if (months <= 0 && weeks <= 0 && days <= 0 && minutes > 0)
            result.append(minutes).append(" minute").append(minutes > 1 ? "s " : " ");

        return result.toString();
    }
}
