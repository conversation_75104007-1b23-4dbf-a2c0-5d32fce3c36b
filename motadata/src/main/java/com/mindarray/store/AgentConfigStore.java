/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *   Change Logs:
 *   Date            Author              Notes
 *   23-May-2025     Pruthviraj          Reference entity added for netroute in agent
 */

package com.mindarray.store;

import com.mindarray.GlobalConstants;
import com.mindarray.agent.AgentConstants;
import com.mindarray.api.*;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static com.mindarray.agent.AgentConstants.AGENT;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.Agent.AGENT_CONFIGS;
import static com.mindarray.api.Agent.AGENT_UUID;

public class AgentConfigStore extends AbstractConfigStore
{
    private static final Logger LOGGER = new Logger(AgentConfigStore.class, GlobalConstants.MOTADATA_STORE, "Agent Config Store");

    private static final AgentConfigStore STORE = new AgentConfigStore();

    private final Map<String, Long> itemsByUUID = new ConcurrentHashMap<>();

    private AgentConfigStore()
    {
        super(ConfigDBConstants.COLLECTION_AGENT, LOGGER, false,
                List.of(
                        Map.of(
                                REFERENCE_ENTITY, APIConstants.Entity.DISCOVERY,
                                REFERENCE_ENTITY_PROPERTY, Discovery.DISCOVERY_AGENTS,
                                REFERENCE_ENTITY_STORE, ConfigStore.DISCOVERY,
                                REFERENCE_ENTITY_PROPERTY_TYPE, APIConstants.ReferenceEntityPropertyType.MULTI_VALUE
                        ),
                        Map.of(
                                REFERENCE_ENTITY, APIConstants.Entity.OBJECT,
                                REFERENCE_ENTITY_STORE, ConfigStore.OBJECT,
                                REFERENCE_ENTITY_CONTEXT_PROP_KEY, AIOpsObject.OBJECT_CATEGORY,
                                REFERENCE_ENTITY_CONTEXT_PROP_VALUE, NMSConstants.Category.SERVICE_CHECK.getName(),
                                REFERENCE_ENTITY_CONTEXT_PROP_CONDITION, AIOpsObject.OBJECT_AGENT,
                                REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.CONTEXT_VALUE
                        ),
                        Map.of(
                                REFERENCE_ENTITY, Entity.NETROUTE,
                                REFERENCE_ENTITY_PROPERTY, NetRoute.NETROUTE_SOURCE,
                                REFERENCE_ENTITY_STORE, ConfigStore.NETROUTE,
                                REFERENCE_ENTITY_PROPERTY_TYPE, ReferenceEntityPropertyType.VALUE
                        )
                )
        );
    }

    public static AgentConfigStore getStore()
    {
        return STORE;
    }

    private void init()
    {
        itemsByUUID.clear();

        items.values().stream().filter(item -> item.getString(AGENT_UUID) != null)
                .collect(Collectors.groupingBy(item -> item.getString(AGENT_UUID),
                        Collectors.mapping(item -> item.getLong(GlobalConstants.ID), Collectors.toList())))
                .forEach((key, value) ->
                        itemsByUUID.put(key, value.getFirst()));

    }

    @Override
    public Future<Void> addItem(long id)
    {
        var promise = Promise.<Void>promise();

        super.addItem(id).onComplete(result ->
        {
            if (result.succeeded())
            {
                itemsByUUID.put(getItem(id).getString(AGENT_UUID), id);
            }

            promise.complete();
        });

        return promise.future();
    }

    @Override
    public void deleteItem(long id)
    {
        if (items.containsKey(id))
        {
            super.deleteItem(id);

            init();
        }
    }

    @Override
    public void deleteItems(JsonArray ids)
    {
        super.deleteItems(ids);

        init();
    }

    public String getAgentUUID(long id)
    {
        return existItem(id) ? items.get(id).getString(AGENT_UUID) : null;
    }

    public Long getAgentId(String uuid)
    {
        return itemsByUUID.containsKey(uuid) ? itemsByUUID.get(uuid) : CommonUtil.getLong(GlobalConstants.NOT_AVAILABLE);
    }

    @Override
    public Future<Void> initStore()
    {

        var promise = Promise.<Void>promise();

        super.initStore().onComplete(result ->
        {
            if (result.succeeded())
            {
                try
                {
                    init();

                    promise.complete();
                }

                catch (Exception exception)
                {
                    promise.fail(exception);

                    LOGGER.warn("failed to init agent config store...");

                    LOGGER.error(exception);
                }
            }

            else
            {
                promise.fail(result.cause());
            }
        });

        return promise.future();
    }

    public int getProvisionedItems()
    {
        return items.size();
    }

    public int getReservedMetricAgents()
    {
        var items = 0;

        for (var item : this.items.entrySet())
        {
            var configs = new JsonObject(item.getValue().getString(AGENT_CONFIGS));

            if (GlobalConstants.NO.equalsIgnoreCase(configs.getJsonObject(AGENT).getString(AgentConstants.METRIC_AGENT_STATUS))
                    || RemoteEventProcessorConfigStore.getStore().getItemByValue(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID,
                    configs.getJsonObject(AgentConstants.AGENT).getString(AGENT_UUID)) != null)
            {
                items++;
            }
        }
        return items;
    }

    public void updateItem(long id, JsonObject item)
    {
        items.put(id, item);
    }
}
