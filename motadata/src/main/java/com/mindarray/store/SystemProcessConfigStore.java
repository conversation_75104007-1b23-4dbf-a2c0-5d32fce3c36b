/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.store;

import com.mindarray.GlobalConstants;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.util.Logger;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.stream.Collectors;

public class SystemProcessConfigStore extends AbstractConfigStore
{

    private static final Logger LOGGER = new Logger(SystemProcessConfigStore.class, GlobalConstants.MOTADATA_STORE, "System Process Config Store");

    private static final SystemProcessConfigStore STORE = new SystemProcessConfigStore();

    private SystemProcessConfigStore()
    {

        super(ConfigDBConstants.COLLECTION_SYSTEM_PROCESS, LOGGER, false);
    }

    public static SystemProcessConfigStore getStore()
    {
        return STORE;
    }

    public JsonObject getItemByValue(String field, String value, String flatField, String flatValue)
    {
        var result = items.values().parallelStream()
                .filter(item -> item.containsKey(flatField) && item.getString(flatField).equalsIgnoreCase(flatValue) &&
                        value.trim().contains(item.getString(field)))
                .findFirst().orElse(null);

        return result != null ? result.copy() : null;
    }

    public JsonArray getItemsByValue(String field, String value, String flatField, String flatValue)
    {
        return new JsonArray(items.values().stream()
                .filter(item -> item.containsKey(flatField) && item.getString(flatField).equalsIgnoreCase(flatValue) &&
                        value.trim().contains(item.getString(field)))
                .map(JsonObject::copy)
                .collect(Collectors.toList()));

    }
}
