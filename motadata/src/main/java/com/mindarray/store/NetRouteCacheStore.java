/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			        Notes
 *  20-Feb-2025		Pruthviraj Jadeja		Initial commit
 */


package com.mindarray.store;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.NetRoute;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.netroute.NetRouteConstants;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonObject;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.EVENT_TIMESTAMP;
import static com.mindarray.nms.NMSConstants.PREVIOUS_FLAP_STATUS;
import static com.mindarray.nms.NMSConstants.PREVIOUS_FLAP_TIMESTAMP;

public class NetRouteCacheStore extends AbstractCacheStore
{
    private static final Logger LOGGER = new Logger(NetRouteCacheStore.class, GlobalConstants.MOTADATA_STORE, "NetRoute Cache Store");
    private static final NetRouteCacheStore store = new NetRouteCacheStore();
    private final Map<Long, Integer> metrics = new ConcurrentHashMap<>();                                 // NetRoute metrics
    private final Map<Long, Long> pollingTimestamps = new ConcurrentHashMap<>();                         // Last polling timestamp
    private final Map<Long, String> items = new ConcurrentHashMap<>();                                  // This will store last status of the netroute
    private final Map<Long, Long> timestamps = new ConcurrentHashMap<>();                               // This will store timestamp current status
    private final Map<Long, JsonObject> hopRegistries = new ConcurrentHashMap<>();                    // Id -> (Hop IP -> Hop Info,email,phone,organization,ASN etc.)
    private final AtomicBoolean dirty = new AtomicBoolean();

    private NetRouteCacheStore()
    {
    }

    public static NetRouteCacheStore getStore()
    {
        return store;
    }

    @Override
    public Future<Void> initStore()
    {
        var promise = Promise.<Void>promise();

        var file = new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + NetRouteConstants.NETROUTE_STATUS_FLAPS);

        if (file.exists())
        {
            var buffer = Bootstrap.vertx().fileSystem().readFileBlocking(file.getPath());

            if (buffer != null && buffer.getBytes().length > 0)
            {
                var context = new JsonObject(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes()))).getMap();

                ((Map<String, Object>) (context.get("items"))).forEach((key, value) -> items.put(CommonUtil.getLong(key), value.toString()));

                ((Map<String, Object>) (context.get("timestamp"))).forEach((key, value) -> timestamps.put(CommonUtil.getLong(key), CommonUtil.getLong(value)));

                LOGGER.info("netroute items are loaded from file...");
            }
        }
        else
        {
            Bootstrap.vertx().fileSystem().createFileBlocking(file.getPath());
        }

        file = new File(CURRENT_DIR + PATH_SEPARATOR + CONFIG_DIR + PATH_SEPARATOR + NetRouteConstants.NETROUTE_HOPS_REGISTRY_INFO);

        if (file.exists())
        {
            var buffer = Bootstrap.vertx().fileSystem().readFileBlocking(file.getPath());

            if (buffer != null && buffer.getBytes().length > 0)
            {
                var context = new JsonObject(Buffer.buffer(CodecUtil.toBytes(buffer.getBytes()))).getMap();

                ((Map<String, Object>) (context.get("hops-info"))).forEach((key, value) -> hopRegistries.put(CommonUtil.getLong(key), JsonObject.mapFrom(value)));

                LOGGER.info("netroute hops info are loaded from file...");
            }
        }
        else
        {
            Bootstrap.vertx().fileSystem().createFileBlocking(file.getPath());
        }

        promise.complete();

        LOGGER.info(String.format("store %s initialized...", this.getClass().getSimpleName()));

        return promise.future();
    }

    public void updateMetricInterval(int value, List<Long> metrics)
    {
        var iterator = this.metrics.entrySet().iterator();

        while (iterator.hasNext())
        {
            var metric = iterator.next();

            var interval = metric.getValue() - value;

            if (interval <= 0)
            {
                var context = NetRouteConfigStore.getStore().getItem(metric.getKey(), false);

                if (context != null && !context.isEmpty())
                {
                    metric.setValue(context.getInteger(NetRoute.NETROUTE_POLLING_TIME));

                    metrics.add(metric.getKey());
                }
                else
                {
                    LOGGER.info(String.format("netroute metric %s removed ...", metric.getKey()));

                    iterator.remove();
                }
            }
            else
            {
                metric.setValue(interval);
            }
        }
    }

    public void addMetric(long id, int interval)
    {
        metrics.put(id, interval);
    }

    public void deleteMetric(long id)
    {
        metrics.remove(id);
    }

    public Map<Long, String> getItems()
    {
        return new HashMap<>(items);
    }

    public String getItem(long id)
    {
        return items.get(id);
    }

    public void updateItem(long id, String status, long timestamp)
    {
        var flap = false;

        if (items.containsKey(id) && !items.get(id).equalsIgnoreCase(status))
        {
            flap = true;

            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_NETROUTE_STATUS_UPDATE, new JsonObject().put(PREVIOUS_FLAP_TIMESTAMP, timestamps.get(id))
                    .put(EVENT_TIMESTAMP, timestamp).put(GlobalConstants.ID, id)
                    .put(VisualizationConstants.VisualizationDataSource.CUMULATIVE_OBJECT_STATUS_FLAP.getName(), GlobalConstants.YES)
                    .put(PREVIOUS_FLAP_STATUS, items.get(id)).put(GlobalConstants.STATUS, status));
        }

        items.put(id, status);

        if (flap || !timestamps.containsKey(id))
        {
            timestamps.put(id, timestamp);
        }
    }

    public void deleteItem(long id)
    {
        items.remove(id);

        hopRegistries.remove(id);

        pollingTimestamps.remove(id);
    }

    public long getMetricPollTimestamp(long id)
    {
        return pollingTimestamps.getOrDefault(id, 0L);
    }

    public void updateMetricPollTimestamp(long id, long timestamp)
    {
        pollingTimestamps.put(id, timestamp);
    }

    public JsonObject getHopRegistryInfo(long id)
    {
        return this.hopRegistries.getOrDefault(id, new JsonObject());
    }

    public void updateHopRegistryInfo(long id, JsonObject registryInfo)
    {
        this.hopRegistries.put(id, registryInfo);
    }

    public boolean dirty()
    {
        return dirty.get();
    }

    public void setDirty(boolean value)
    {
        dirty.set(value);
    }

    public void dump()
    {
        if (!items.isEmpty())
        {
            Bootstrap.vertx().<Void>executeBlocking(future ->
            {
                try
                {
                    Bootstrap.vertx().fileSystem().writeFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + NetRouteConstants.NETROUTE_STATUS_FLAPS,
                            Buffer.buffer(CodecUtil.compress(new JsonObject().put("items", new HashMap<>(items)).put("timestamp", new HashMap<>(timestamps)).encode().getBytes())));

                    dirty.set(true);
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

                future.complete();
            });
        }

        if (!hopRegistries.isEmpty())
        {
            Bootstrap.vertx().<Void>executeBlocking(future ->
            {
                try
                {
                    Bootstrap.vertx().fileSystem().writeFileBlocking(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.CONFIG_DIR + GlobalConstants.PATH_SEPARATOR + NetRouteConstants.NETROUTE_HOPS_REGISTRY_INFO,
                            Buffer.buffer(CodecUtil.compress(new JsonObject().put("hops-info", new HashMap<>(hopRegistries)).encode().getBytes())));

                    dirty.set(true);
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

                future.complete();
            });
        }
    }
}
