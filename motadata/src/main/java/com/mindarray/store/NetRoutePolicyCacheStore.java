/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date			Author			        Notes
 *  20-Feb-2025		Pruthviraj Jadeja		Initial commit
 */


package com.mindarray.store;

import com.mindarray.GlobalConstants;
import com.mindarray.api.MetricPolicy;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.ENTITY_ID;

public class NetRoutePolicyCacheStore extends AbstractCacheStore
{
    private static final NetRoutePolicyCacheStore STORE = new NetRoutePolicyCacheStore();
    private static final Logger LOGGER = new Logger(NetRoutePolicyCacheStore.class, GlobalConstants.MOTADATA_STORE, "NetRoute Policy Cache Store");

    private final Map<String, Integer> policyKeys = new ConcurrentHashMap<>();//containing policy keys with its hashcodes
    private final Map<Integer, String> policyKeyHashCodes = new ConcurrentHashMap<>();// containing policy keys against hashcode opposite of previous one to get key value
    private final Map<Long, String> policyKeysByObject = new ConcurrentHashMap<>(); //unique policy keys or policy keys against monitor id or entityid
    private final Map<Integer, Long> timestamps = new ConcurrentHashMap<>();//policy key hashcode with its policy triggered time
    private final Map<Integer, Long> policyIds = new ConcurrentHashMap<>();//policy key hashcode with its policy triggered policyId
    private final Map<Integer, GlobalConstants.Severity> severities = new ConcurrentHashMap<>(); //policy key hashcode with its policy triggered severity
    private final Map<Integer, String> values = new ConcurrentHashMap<>();//policy key hashcode with its policy triggered value
    private final Map<Integer, String> policyThresholds = new ConcurrentHashMap<>();//policy key hashcode with its policy triggered threshold value
    private final Map<Long, String> objects = new ConcurrentHashMap<>();

    private NetRoutePolicyCacheStore()
    {
    }

    public static NetRoutePolicyCacheStore getStore()
    {
        return STORE;
    }

    public void updateItem(int hashCode, JsonObject context)
    {
        try
        {
            if (policyKeys.getOrDefault(context.getString(PolicyEngineConstants.POLICY_KEY), null) == null)
            {
                policyKeys.put(context.getString(PolicyEngineConstants.POLICY_KEY), hashCode);

                policyKeyHashCodes.put(hashCode, context.getString(PolicyEngineConstants.POLICY_KEY));
            }

            var keys = policyKeysByObject.getOrDefault(context.getLong(ENTITY_ID), null);

            var items = new HashSet<String>();

            items.add(CommonUtil.getString(hashCode));

            if (keys != null && !keys.isEmpty())
            {
                items.addAll(Arrays.asList(keys.split(GlobalConstants.COMMA_SEPARATOR)));

                policyKeysByObject.put(context.getLong(ENTITY_ID), StringUtils.join(items, GlobalConstants.COMMA_SEPARATOR));
            }

            else
            {
                policyKeysByObject.put(context.getLong(ENTITY_ID), CommonUtil.getString(hashCode));
            }

            var severity = severities.getOrDefault(hashCode, GlobalConstants.Severity.UNKNOWN);

            policyIds.put(hashCode, context.getLong(ID));

            severities.put(hashCode, GlobalConstants.Severity.valueOf(context.getString(GlobalConstants.SEVERITY)));

            values.put(hashCode, CommonUtil.getString(context.getValue(VALUE)));

            timestamps.put(hashCode, context.getLong(EventBusConstants.EVENT_TIMESTAMP));

            policyThresholds.put(hashCode, CommonUtil.getString(context.getValue(MetricPolicy.POLICY_THRESHOLD, EMPTY_VALUE)));

            if (!severity.equals(Severity.valueOf(context.getString(GlobalConstants.SEVERITY))))
            {
                update(context, items);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void update(JsonObject context, Set<String> items)
    {
        try
        {
            var severities = new TreeSet<Severity>();

            var entityId = context.getLong(ENTITY_ID);

            var policyKey = context.getString(PolicyEngineConstants.POLICY_KEY);

            for (var item : items)
            {
                if (!item.isEmpty())
                {
                    var policyHashCode = CommonUtil.getInteger(item);

                    var severity = this.severities.get(policyHashCode);

                    policyKey = policyKeyHashCodes.get(policyHashCode);

                    if (policyKey != null)
                    {
                        severities.add(severity);
                    }
                }
            }

            if (!objects.getOrDefault(entityId, Severity.UNKNOWN.name()).equalsIgnoreCase(severities.last().name()))
            {
                objects.put(entityId, severities.last().name());
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    public String getSeverity(long entity)
    {
        return objects.getOrDefault(entity, null);
    }

    public void cleanup(long id)
    {
        try
        {
            var hashCodes = new HashSet<Integer>();

            var entities = new HashMap<Long, Set<String>>();

            var iterator = this.policyIds.entrySet().iterator();

            while (iterator.hasNext())
            {
                var entry = iterator.next();

                if (entry.getValue().equals(id) && policyKeyHashCodes.containsKey(entry.getKey()))
                {
                    hashCodes.add(entry.getKey());

                    var entity = CommonUtil.getLong(policyKeyHashCodes.get(entry.getKey()).split(SEPARATOR_WITH_ESCAPE)[0]);

                    entities.computeIfAbsent(entity, value -> new HashSet<>()).add(CommonUtil.getString(entry.getKey()));

                    iterator.remove();
                }
            }

            if (!hashCodes.isEmpty())
            {
                hashCodes.forEach(this::cleanup);

                for (var entry : entities.entrySet())
                {
                    var items = new HashSet<>(Arrays.asList(policyKeysByObject.get(entry.getKey()).split(COMMA_SEPARATOR)));

                    items.removeAll(entry.getValue());

                    if (!items.isEmpty())
                    {
                        policyKeysByObject.put(entry.getKey(), StringUtils.join(items, COMMA_SEPARATOR));
                    }

                    notify(entry.getKey(), items);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void cleanup(int hashCode)
    {
        severities.remove(hashCode);

        policyIds.remove(hashCode);

        values.remove(hashCode);

        timestamps.remove(hashCode);

        policyKeys.entrySet().removeIf(entry -> entry.getValue() == hashCode);

        policyKeyHashCodes.remove(hashCode);

        policyThresholds.remove(hashCode);
    }

    private void notify(long id, Set<String> items)
    {
        try
        {
            if (!items.isEmpty())
            {
                var severities = new TreeSet<Severity>();

                for (var item : items)
                {
                    if (CommonUtil.isNotNullOrEmpty(item))
                    {
                        if (this.severities.containsKey(CommonUtil.getInteger(item)))
                        {
                            severities.add(this.severities.get(CommonUtil.getInteger(item)));
                        }
                    }
                }

                if (!severities.isEmpty() && !objects.getOrDefault(id, Severity.UNKNOWN.name()).equalsIgnoreCase(severities.last().name()))
                {
                    objects.put(id, severities.last().name());
                }
                else if (severities.isEmpty())
                {
                    objects.put(id, Severity.UNKNOWN.name());
                }
            }
            else
            {
                objects.put(id, Severity.UNKNOWN.name());
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    public void deleteItem(long id)
    {
        objects.remove(id);

        var keys = policyKeysByObject.remove(id);

        if (keys != null)
        {
            Arrays.stream(keys.split(COMMA_SEPARATOR)).forEach(key -> cleanup(CommonUtil.getInteger(key)));
        }
    }

    public Map<String, JsonObject> getItems(JsonArray entities)
    {
        var items = new HashMap<String, JsonObject>();

        try
        {
            for (var index = 0; index < entities.size(); index++)
            {
                if (policyKeysByObject.containsKey(entities.getLong(index)))
                {
                    for (var key : policyKeysByObject.get(entities.getLong(index)).split(COMMA_SEPARATOR))
                    {
                        try
                        {
                            if (CommonUtil.isNotNullOrEmpty(key))
                            {
                                var hashCode = CommonUtil.getInteger(key);

                                if (policyKeyHashCodes.containsKey(hashCode))
                                {
                                    items.put(policyKeyHashCodes.get(hashCode), getItem(hashCode));
                                }
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return items;
    }

    public JsonObject getSeverities()
    {
        return new JsonObject().mergeIn(JsonObject.mapFrom(objects));
    }

    public JsonObject getItem(int hashCode)
    {
        JsonObject item = null;

        try
        {
            if (policyKeyHashCodes.containsKey(hashCode))
            {
                var policyKey = policyKeyHashCodes.get(hashCode);

                item = new JsonObject().put(METRIC, policyKey.split(SEPARATOR_WITH_ESCAPE)[1].trim()).
                        put(ID, policyIds.get(hashCode)).put(ENTITY_ID, CommonUtil.getLong(policyKey.split(SEPARATOR_WITH_ESCAPE)[0]))
                        .put(EventBusConstants.EVENT_TIMESTAMP, timestamps.get(hashCode))
                        .put(VALUE, values.get(hashCode)).put(SEVERITY, severities.get(hashCode))
                        .put(MetricPolicy.POLICY_THRESHOLD, policyThresholds.getOrDefault(hashCode, EMPTY_VALUE));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return item;
    }
}
