/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.job;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.json.JsonObject;
import org.quartz.Job;
import org.quartz.JobExecutionContext;

/**
 * The DNSCacheFlushJob class is responsible for periodically flushing the DNS cache in the Motadata platform.
 * <p>
 * This class implements the Quartz Job interface and triggers DNS cache flushing by sending
 * an event to the event bus. DNS cache flushing is important to ensure that DNS resolution
 * remains accurate and up-to-date, especially in environments where DNS records change frequently.
 * <p>
 * The job is scheduled to run at configurable intervals (determined by {@link MotadataConfigUtil#getDNSCacheFlushTimerHours()})
 * to ensure that the DNS cache doesn't become stale.
 */
public class DNSCacheFlushJob implements Job
{
    /**
     * CRON expression for scheduling this job to run at regular intervals.
     * <p>
     * The expression is formatted to run at the top of every N hours, where N is configured
     * through {@link MotadataConfigUtil#getDNSCacheFlushTimerHours()}.
     */
    public static final String DNS_CACHE_FLUSH_JOB_CRON_EXPRESSION = String.format("0 0 */%d ? * *", MotadataConfigUtil.getDNSCacheFlushTimerHours());

    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(DNSCacheFlushJob.class, GlobalConstants.MOTADATA_JOB, "DNS Job");

    /**
     * Executes the DNS cache flush job.
     * <p>
     * This method sends an event to the event bus to trigger the DNS cache flush operation.
     * The actual flushing of the DNS cache is handled by components that listen for the
     * EVENT_DNS_CACHE_FLUSH event.
     *
     * @param jobExecutionContext the context in which the job is executed
     */
    @Override
    public void execute(JobExecutionContext jobExecutionContext)
    {
        try
        {
            // Log debug information if debug mode is enabled
            if (CommonUtil.debugEnabled())
            {
                LOGGER.debug("Starting scheduled DNS cache flush job");
            }

            // Send event to trigger DNS cache flush
            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_DNS_CACHE_FLUSH, new JsonObject());
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
