/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 * Change Logs:
 *  Date			Author			         Notes
 *  25-Mar-2025     Smit Prajapati           MOTADATA-5435: Flow back-pressure mechanism.
 */

package com.mindarray.job;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;

import static com.mindarray.GlobalConstants.FLOW_CACHE_DIRECTORY_PATH;

/**
 * The FlowCacheCleanupJob class is responsible for cleaning up outdated flow cache files in the Motadata platform.
 * <p>
 * This class implements the Quartz Job interface and performs regular cleanup of flow cache files
 * to prevent excessive disk usage and maintain system performance. Flow cache files store network
 * flow data temporarily and need to be periodically cleaned up to avoid accumulation of stale data.
 * <p>
 * The job performs the following tasks:
 * <ol>
 *   <li>Reads the flow-cache directory to list files</li>
 *   <li>Extracts timestamps from filenames (last 12 characters in yyyyMMddHHmm format)</li>
 *   <li>Deletes files older than 1 hour to ensure only recent data is kept</li>
 *   <li>Sends a cleanup event with the list of deleted files</li>
 *   <li>Sends a notification to clear cache from all event collectors</li>
 * </ol>
 * <p>
 * The job is scheduled to run every 6 hours to ensure regular cleanup of flow cache files.
 * <p>
 * Implementation note: This job was enhanced as part of MOTADATA-5435 to support the flow back-pressure mechanism.
 */
public class FlowCacheCleanupJob implements Job
{
    /**
     * CRON expression for scheduling this job to run every 6 hours
     */
    public static final String FLOW_CACHE_CLEANUP_JOB_CRON_EXPRESSION = "0 0 */6 * * ?"; // Runs at every 6 hour

    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(FlowCacheCleanupJob.class, GlobalConstants.MOTADATA_JOB, "Flow Cache Cleanup Job");

    /**
     * Date time formatter for parsing timestamps from filenames.
     * <p>
     * Flow cache files have timestamps in their names in the format yyyyMMddHHmm (e.g., 202503251430).
     * This formatter is used to parse these timestamps to determine file age.
     */
    private static final DateTimeFormatter FILE_TIMESTAMP_FORMAT = DateTimeFormat.forPattern("yyyyMMddHHmm");

    /**
     * Executes the flow cache cleanup job.
     * <p>
     * This method performs the following operations:
     * <ol>
     *   <li>Reads the flow-cache directory asynchronously to list all files</li>
     *   <li>Extracts timestamps from filenames (last 12 characters in yyyyMMddHHmm format)</li>
     *   <li>Identifies files older than 1 hour</li>
     *   <li>Deletes outdated files to ensure only recent data is kept</li>
     *   <li>Sends a cleanup event with the list of deleted files</li>
     *   <li>Sends a notification to clear cache from all event collectors</li>
     * </ol>
     * <p>
     * The cleanup operation helps prevent excessive disk usage and maintains system performance
     * by removing stale flow cache data that is no longer needed.
     *
     * @param jobExecutionContext the context in which the job is executed
     * @throws JobExecutionException if an error occurs during job execution
     */
    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException
    {
        try
        {
            LOGGER.info("Executing Flow Cleanup Job...");

            // Asynchronously read the flow cache directory to get a list of all files
            Bootstrap.vertx().fileSystem().readDir(FLOW_CACHE_DIRECTORY_PATH, result ->
            {
                if (result.succeeded())
                {
                    // Create an array to store the paths of files that will be deleted
                    var items = new JsonArray();

                    // Process each file in the directory
                    for (var item : result.result())
                    {
                        try
                        {
                            // Extract timestamp from filename (last 12 characters: yyyyMMddHHmm)
                            // For example, from "flow-cache-202503251430.json", extract "202503251430"
                            var time = FILE_TIMESTAMP_FORMAT.parseDateTime(item.substring(item.length() - 12));

                            // Check if the file is older than 1 hour (60 minutes)
                            // Files older than 1 hour are considered stale and should be deleted
                            if (time.isBefore(DateTime.now().minusMinutes(60)))
                            {
                                // Add the file path to the list of items to be deleted
                                items.add(item);

                                // Asynchronously delete the file
                                Bootstrap.vertx().fileSystem().delete(item, asyncResult ->
                                {
                                    if (asyncResult.succeeded())
                                    {
                                        // Log successful deletion if debug mode is enabled
                                        if (CommonUtil.debugEnabled())
                                        {
                                            LOGGER.debug("Deleted old file: " + item);
                                        }
                                    }
                                    else
                                    {
                                        // Log error if deletion fails
                                        LOGGER.error(asyncResult.cause());
                                    }
                                });
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    }

                    // Send a cleanup event with the list of deleted files
                    // This allows other components to update their state if needed
                    Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_FLOW_CACHE_CLEANUP, items);
                }
                else
                {
                    // Log error if reading the directory fails
                    LOGGER.error(result.cause());
                }
            });

            // Send a notification to clear cache from all event collectors
            // This ensures that all distributed components also clean up their local caches
            Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_PUBLICATION,
                    new JsonObject()
                            .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_CHANGE_NOTIFICATION)
                            .put(EventBusConstants.EVENT_TOPIC, EventBusConstants.REMOTE_EVENT_PROCESSOR_TOPIC)
                            .put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.CLEANUP_FLOW_CACHE.name()));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }
}
