/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *     Change Logs:
 *     Date            Author          Notes
 *     5-Mar-2025      Chaitas      MOTADATA-4074 : Stream Widget | When editing the stream widget, data does not appear.
 *     5-Mar-2025      Chaitas      MOTADATA-4334 : The system failed to display the actual preview based on the applied filter conditions in the Event History widget preview.
 *     10-Mar-2025     Pruthviraj   MOTADATA-5331 : NetRoute metric and availability added
 *     2025-03-25   <PERSON><PERSON>       Added Support for Status Flap Metric.
 */
package com.mindarray.visualization;

/**
 * Central coordinator for visualization requests in the Motadata platform.
 * <p>
 * VisualizationManager is responsible for:
 * <ul>
 *   <li>Receiving and processing visualization requests from clients</li>
 *   <li>Routing requests to appropriate specialized managers based on visualization type</li>
 *   <li>Tracking query progress and managing query timeouts</li>
 *   <li>Aggregating and formatting responses from different data sources</li>
 *   <li>Caching visualization results for improved performance</li>
 *   <li>Publishing visualization results back to clients</li>
 * </ul>
 * <p>
 * This class maintains several maps to track query status, execution time, and cached results.
 * It also implements timeout handling to abort long-running queries that exceed execution limits.
 * <p>
 * The manager supports various visualization types including metrics, events, availability,
 * and compliance data, routing each request to the appropriate specialized manager.
 */

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.InfoMessageConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.APIConstants;
import com.mindarray.api.User;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.UserConfigStore;
import com.mindarray.store.WidgetConfigStore;
import com.mindarray.util.*;
import com.opencsv.CSVWriterBuilder;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.io.FileWriter;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.visualization.VisualizationConstants.*;

public class VisualizationManager extends AbstractVerticle
{
    /**
     * Logger instance for this class
     */
    private static final Logger LOGGER = new Logger(VisualizationManager.class, GlobalConstants.MOTADATA_VISUALIZATION, "Visualization Manager");

    /**
     * Map to track query statuses: queryId -> (subqueryId -> status)
     */
    private final Map<Long, Map<Long, String>> queryStatuses = new HashMap<>();

    /**
     * Map to track query timeouts: queryId -> remaining time (in seconds)
     */
    private final Map<Long, Integer> queryTickers = new HashMap<>();

    /**
     * Map to track query execution times: queryId -> execution time
     */
    private final Map<Long, Long> queryExecutionTicks = new HashMap<>();

    /**
     * Map to store query identifiers: queryId -> query string
     */
    private final Map<Long, String> queries = new HashMap<>();

    /**
     * Map to store session contexts: queryId -> (sessionId -> context)
     */
    private final Map<Long, Map<String, JsonObject>> sessions = new HashMap<>();

    /**
     * Map to track query progress: queryId -> progress
     */
    private final Map<Long, Short> queryTrackers = new HashMap<>();

    /**
     * Map to store cached visualization results: widgetId -> (userId -> (sessionId -> cached data))
     */
    private final Map<Long, Map<Long, Map<String, byte[]>>> cachedItems = new HashMap<>();

    /**
     * Map to store subquery contexts: subQueryId -> context
     */
    private final Map<Long, JsonObject> subQueries = new HashMap<>();

    /**
     * Map to track progress of composite queries with multiple data sources: queryId -> (subQueryId -> progress)
     */
    private final Map<Long, Map<Long, Short>> compositeQueryTrackers = new HashMap<>();

    /**
     * StringBuilder for constructing query strings
     */
    private final StringBuilder builder = new StringBuilder();

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            vertx.setPeriodic(30 * 1000L, periodicTimer ->
            {
                var iterator = queryTickers.entrySet().iterator();

                while (iterator.hasNext())
                {
                    var entry = iterator.next();

                    entry.setValue(entry.getValue() - 30);

                    if (entry.getValue() <= 0)
                    {
                        vertx.eventBus().send(EVENT_PUBLICATION_DATASTORE_READ, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false)
                                .put(EventBusConstants.EVENT_TOPIC, DATASTORE_QUERY_TOPIC)
                                .put(EventBusConstants.EVENT_CONTEXT, Buffer.buffer().appendByte(DatastoreConstants.OperationType.QUERY_ABORT.getName()).appendBytes(new JsonObject().put(QUERY_ID, entry.getKey()).encode().getBytes()).getBytes()));

                        if (queryStatuses.containsKey(entry.getKey()))
                        {
                            for (var subQueryId : queryStatuses.get(entry.getKey()).keySet())
                            {
                                Bootstrap.vertx().eventBus().publish(EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(packError(String.format(ErrorMessageConstants.VISUALIZATION_QUERY_ABORTED, "Execution time limit reached"), entry.getKey(), subQueryId).getBytes()));

                                Bootstrap.vertx().eventBus().publish(EVENT_VISUALIZATION_RESPONSE, CodecUtil.compress(packError(String.format(ErrorMessageConstants.VISUALIZATION_QUERY_ABORTED, "Execution time limit reached"), entry.getKey(), subQueryId).getBytes()));
                            }
                        }

                        iterator.remove();
                    }
                }
            });

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CHANGE_NOTIFICATION, message ->
            {
                var event = message.body();

                if (VISUALIZATION_CACHING_ENABLED)
                {
                    switch (EventBusConstants.ChangeNotificationType.valueOf(event.getString(EventBusConstants.CHANGE_NOTIFICATION_TYPE)))
                    {
                        case UPDATE_WIDGET, DELETE_WIDGET ->
                                cachedItems.remove(event.getJsonObject(EVENT_CONTEXT).getLong(ID));

                        case DELETE_USER ->
                                cachedItems.values().removeIf(value -> value.containsKey(event.getJsonObject(EVENT_CONTEXT).getLong(ID)));

                        default ->
                        {
                        }
                    }
                }
            }).exceptionHandler(LOGGER::error);

            vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_VISUALIZATION_CACHE_INVALIDATE, message ->
            {
                var event = message.body();

                if (cachedItems.containsKey(event.getLong(ID)) && cachedItems.get(event.getLong(ID)).containsKey(event.getLong(User.USER_ID)))
                {
                    cachedItems.get(event.getLong(ID)).get(event.getLong(User.USER_ID)).remove(event.getString(APIConstants.SESSION_ID));

                    if (cachedItems.get(event.getLong(ID)).get(event.getLong(User.USER_ID)).isEmpty())
                    {
                        cachedItems.get(event.getLong(ID)).remove(event.getLong(User.USER_ID));

                        if (cachedItems.get(event.getLong(ID)).isEmpty())
                        {
                            cachedItems.remove(event.getLong(ID));
                        }
                    }
                }
            }).exceptionHandler(LOGGER::error);

            vertx.eventBus().<JsonObject>localConsumer(EVENT_VISUALIZATION_SUB_QUERY_CONTEXT, message ->
            {
                var event = message.body();

                if (subQueries.containsKey(event.getLong(SUB_QUERY_ID)))
                {
                    subQueries.put(event.getLong(SUB_QUERY_ID), event);
                }
            });

            vertx.eventBus().<byte[]>localConsumer(EVENT_VISUALIZATION_RESPONSE, message ->
            {
                try
                {
                    var buffer = Buffer.buffer(CodecUtil.toBytes(message.body()));

                    var cacheItem = true;

                    var widgetId = 0L;

                    var userId = 0L;

                    var queryId = buffer.getLongLE(0);

                    var subQueryId = buffer.getLongLE(8);

                    var subQueryProgress = buffer.getUnsignedByte(16);

                    var status = buffer.getUnsignedByte(33);

                    if (queryStatuses.containsKey(queryId) && queryStatuses.get(queryId).containsKey(subQueryId))
                    {
                        var succeeded = true;

                        short queryProgress = 0;

                        //in case of widget that can contain both metric or availability and flow or log or alert data source in that case need to calculate progress of query
                        if (compositeQueryTrackers.containsKey(queryId))
                        {
                            compositeQueryTrackers.get(queryId).put(subQueryId, subQueryProgress);

                            queryProgress = CommonUtil.getShort(compositeQueryTrackers.get(queryId).values().stream().mapToInt(CommonUtil::getInteger).sum() / queryStatuses.get(queryId).size());

                            buffer.setUnsignedByte(16, queryProgress >= 100 ? 100 : queryProgress);

                            queryTrackers.put(queryId, queryProgress);
                        }

                        else
                        {
                            queryTrackers.put(queryId, subQueryProgress);
                        }

                        if (queries.containsKey(queryId))
                        {
                            var tokens = queries.get(queryId).split(SEPARATOR_WITH_ESCAPE);

                            if (CommonUtil.getLong(tokens[0]) > 0)//other than preview request
                            {
                                widgetId = CommonUtil.getLong(tokens[0]);
                            }

                            if (CommonUtil.getLong(tokens[1]) > 0)//other than preview request
                            {
                                userId = CommonUtil.getLong(tokens[1]);
                            }
                        }

                        if (status == 0)//fail
                        {
                            var errorLength = 38 + buffer.getIntLE(34);

                            var errorMessage = buffer.getString(38, errorLength);

                            if (buffer.length() < errorLength + 1)//if after error length nothing is there then it contains only error so event failed no data is received
                            {
                                succeeded = false;
                            }

                            queryStatuses.get(queryId).put(subQueryId, errorMessage);

                            vertx.eventBus().send(EventBusConstants.EVENT_USER_NOTIFICATION, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(EventBusConstants.EVENT_TYPE, EVENT_VISUALIZATION).put(MESSAGE, String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, widgetId > 0 ? WidgetConfigStore.getStore().getItem(widgetId).getString(VisualizationConstants.VISUALIZATION_NAME) : "Preview Widget", errorMessage)).put(STATUS, STATUS_FAIL));
                        }
                        else
                        {
                            queryStatuses.get(queryId).put(subQueryId, STATUS_SUCCEED);
                        }

                        EventBusConstants.updateEvent(queryId, String.format(InfoMessageConstants.EVENT_TRACKER_VISUALIZATION_RESPONSE_RECEIVED, subQueryId, status, subQueryProgress, queryProgress, DateTimeUtil.timestamp()));

                        if (widgetId > 0 && subQueries.containsKey(subQueryId) && (
                                (subQueries.get(subQueryId).containsKey("cache") && subQueries.get(subQueryId).getBoolean("cache"))
                                        || (subQueries.get(subQueryId).containsKey(VISUALIZATION_CATEGORY) && subQueries.get(subQueryId).getString(VISUALIZATION_CATEGORY).equalsIgnoreCase(VisualizationCategory.STREAM.getName()))
                                        || (subQueries.get(subQueryId).containsKey(VisualizationConstants.VISUALIZATION_TYPE) && (subQueries.get(subQueryId).getString(VisualizationConstants.VISUALIZATION_TYPE).equalsIgnoreCase(VisualizationConstants.VISUALIZATION_TYPE_APPLICATION_AVAILABILITY_TIME_SERIES) || subQueries.get(subQueryId).getString(VisualizationConstants.VISUALIZATION_TYPE).equalsIgnoreCase(VisualizationConstants.VISUALIZATION_TYPE_AVAILABILITY_TIME_SERIES)))))
                        {
                            cacheItem = false;
                        }

                        // Publish result to UI only when it is not composite query irrespective of progress, If it is composite query then progress should be 100 %
                        if (sessions.containsKey(queryId))
                        {
                            publish(queryId, subQueryId, subQueryProgress, buffer);
                        }

                        if (queryTrackers.get(queryId) >= 100)
                        {
                            queryStatuses.get(queryId).forEach((key, value) ->
                            {
                                if (!value.equalsIgnoreCase(STATUS_SUCCEED))
                                {
                                    builder.append(value).append(NEW_LINE);
                                }
                            });

                            if (succeeded)
                            {
                                vertx.eventBus().send(EventBusConstants.EVENT_SUCCEED, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(ERROR, builder.toString()).put(EventBusConstants.EVENT_ID, queryId));
                            }
                            else
                            {
                                vertx.eventBus().send(EventBusConstants.EVENT_FAIL, new JsonObject().put(EventBusConstants.EVENT_COPY_REQUIRED, false).put(EventBusConstants.EVENT_ID, queryId).put(ERROR, builder.toString()));
                            }

                            builder.setLength(0);

                            if (VISUALIZATION_CACHING_ENABLED && widgetId > 0 && cacheItem)
                            {
                                if (succeeded)
                                {
                                    if (queryStatuses.get(queryId).size() == 1)
                                    {
                                        for (var session : sessions.get(queryId).keySet())
                                        {
                                            cachedItems.computeIfAbsent(widgetId, value -> new HashMap<>()).computeIfAbsent(userId, value -> new HashMap<>()).put(session.split(COLUMN_SEPARATOR)[0], buffer.getBytes());
                                        }
                                    }
                                }

                                else
                                {
                                    for (var session : sessions.get(queryId).keySet())
                                    {
                                        if (cachedItems.containsKey(widgetId) && cachedItems.get(widgetId).containsKey(userId))
                                        {
                                            cachedItems.get(widgetId).get(userId).remove(session.split(COLUMN_SEPARATOR)[0]);
                                        }
                                    }
                                }
                            }

                            if (CommonUtil.traceEnabled())
                            {
                                LOGGER.trace("Visualization Response time taken:" + (System.currentTimeMillis() - queryExecutionTicks.get(queryId)) + " ms for query id:" + queryId);
                            }

                            cleanUp(queryId);

                            if (CommonUtil.traceEnabled())
                            {
                                //as of now dumping trace log will be having query explorer to track each and every query
                                LOGGER.trace("Query Stats:" + new JsonObject().put("running.queries", queryStatuses.size()).put("running.sub.queries", subQueries.size()).put("cache.items", cachedItems.size())
                                        .put("query.trackers", queryTrackers.size()).put("composite.query.trackers", compositeQueryTrackers.size()).put("query.tickers", queryTickers.size())
                                        .put("queries", queries.size()).put("query.execution.ticks", queryExecutionTicks.size()).put("query.sessions", sessions.size()));
                            }
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });

            vertx.eventBus().<Long>localConsumer(EVENT_VISUALIZATION_QUERY_ABORT, message ->
            {
                try
                {

                    if (message.body() != null)
                    {
                        if (queryStatuses.containsKey(message.body()))
                        {
                            for (var subQueryId : queryStatuses.get(message.body()).keySet())
                            {
                                Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_DATASTORE_QUERY_RESPONSE, CodecUtil.compress(packError(String.format(ErrorMessageConstants.VISUALIZATION_QUERY_ABORTED, "Execution time limit reached"), message.body(), subQueryId).getBytes()));

                                Bootstrap.vertx().eventBus().publish(EVENT_VISUALIZATION_RESPONSE, CodecUtil.compress(packError(String.format(ErrorMessageConstants.VISUALIZATION_QUERY_ABORTED, "Execution time limit reached"), message.body(), subQueryId).getBytes()));
                            }
                        }

                        cleanUp(message.body());
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });

            vertx.eventBus().<JsonObject>localConsumer(config().getString(EVENT_TYPE), message ->
            {
                var queryId = new AtomicLong(CommonUtil.newEventId());

                try
                {
                    var cacheFound = false;

                    var event = message.body();

                    if (event.containsKey(VisualizationConstants.QUERY_ID))
                    {
                        queryId.set(event.getLong(VisualizationConstants.QUERY_ID));
                    }

                    if (MotadataConfigUtil.devMode())
                    {
                        vertx.eventBus().publish(EventBusConstants.EVENT_VISUALIZATION_TEST, new JsonObject().put(CommonUtil.getString(event.getLong(ID)), queryId.get()));
                    }

                    var user = UserConfigStore.getStore().getItemByValue(User.USER_NAME, event.getString(User.USER_NAME));

                    if (event.containsKey(VISUALIZATION_TIMELINE) && event.getJsonObject(VISUALIZATION_TIMELINE).isEmpty())// in case when user selects no timeline
                    {
                        event.remove(VISUALIZATION_TIMELINE);
                    }

                    JsonObject context;

                    if (event.containsKey(ID) && event.getLong(ID) > 0)
                    {
                        event.remove(VISUALIZATION_DATA_SOURCES);//as in case of widget request will consider saved datasource

                        context = WidgetConfigStore.getStore().getItem(event.getLong(ID)).mergeIn(event);
                    }
                    else
                    {
                        context = event;
                    }

                    if (VISUALIZATION_CACHING_ENABLED && event.getLong(ID) > 0 && cachedItems.containsKey(event.getLong(ID)) && cachedItems.get(event.getLong(ID)).containsKey(user.getLong(ID)) && cachedItems.get(event.getLong(ID)).get(user.getLong(ID)).containsKey(event.getString(APIConstants.SESSION_ID)))
                    {
                        cacheFound = true;
                    }

                    var notifyVisualizationManager = !event.containsKey(EVENT_TYPE) && !event.containsKey(PAGINATION_QUERY);//in this case we will not be sending response to VM it will directly be either send to UI or whomsoever requested for data

                    if (VISUALIZATION_CACHING_ENABLED && cacheFound && !event.containsKey(VisualizationConstants.VISUALIZATION_STREAMING))
                    {
                        EventBusConstants.publish(event.getString(APIConstants.SESSION_ID), UI_NOTIFICATION_WIDGET_QUERY_ID, new JsonObject().put(UI_EVENT_UUID, event.getString(UI_EVENT_UUID)).put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ZERO).put(CommonUtil.getString(event.getLong(ID)), queryId.get()));

                        var buffer = Buffer.buffer(cachedItems.get(event.getLong(ID)).get(user.getLong(ID)).get(event.getString(APIConstants.SESSION_ID)));

                        try
                        {
                            buffer.setLongLE(0, queryId.get());
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }

                        EventBusConstants.publish(event.getString(APIConstants.SESSION_ID), EventBusConstants.UI_ACTION_VISUALIZATION_RENDER, new JsonObject().put(UI_EVENT_UUID, event.getString(UI_EVENT_UUID)).mergeIn(event.getLong(ID) > 0 ? WidgetConfigStore.getStore().getItem(event.getLong(ID)) : event).put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ZERO).put(RESULT, buffer.getBytes()));

                        cleanUp(queryId.get()); //as in case of incremental it can cause issue if we are caching widget data so cleaning up query context..
                    }
                    else
                    {
                        if (event.containsKey(VisualizationConstants.VISUALIZATION_STREAMING) && event.containsKey(VISUALIZATION_SESSIONS))
                        {
                            var visualizationSessions = (JsonArray) event.remove(VISUALIZATION_SESSIONS);

                            queryStatuses.computeIfAbsent(queryId.get(), value -> new HashMap<>());

                            queryExecutionTicks.computeIfAbsent(queryId.get(), value -> DateTimeUtil.currentMilliSeconds());

                            visualizationSessions.forEach(visualizationSession ->
                            {
                                var tokens = CommonUtil.getString(visualizationSession).split(COLUMN_SEPARATOR);

                                EventBusConstants.publish(tokens[0], UI_NOTIFICATION_WIDGET_QUERY_ID, new JsonObject().put(UI_EVENT_UUID, tokens[1]).put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ZERO).put(CommonUtil.getString(event.getLong(ID)), queryId.get()));

                                sessions.computeIfAbsent(queryId.get(), value -> new HashMap<>()).put(tokens[0] + COLUMN_SEPARATOR + tokens[1], new JsonObject().mergeIn(event).put(UI_EVENT_UUID, tokens[1]).put(APIConstants.SESSION_ID, tokens[0]));
                            });
                        }
                        else if (notifyVisualizationManager)
                        {
                            EventBusConstants.publish(event.getString(APIConstants.SESSION_ID), UI_NOTIFICATION_WIDGET_QUERY_ID, new JsonObject().put(UI_EVENT_UUID, event.getString(UI_EVENT_UUID)).put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ZERO).put(CommonUtil.getString(event.getLong(ID)), queryId.get()));

                            sessions.computeIfAbsent(queryId.get(), value -> new HashMap<>()).put(event.getString(APIConstants.SESSION_ID) + COLUMN_SEPARATOR + event.getString(UI_EVENT_UUID), new JsonObject().mergeIn(event));

                            queryStatuses.computeIfAbsent(queryId.get(), value -> new HashMap<>());

                            queryExecutionTicks.computeIfAbsent(queryId.get(), value -> DateTimeUtil.currentMilliSeconds());
                        }

                        DateTimeUtil.buildTimeline(context, event, user);

                        var visualizationTimeLine = context.getJsonObject(VisualizationConstants.VISUALIZATION_TIMELINE);

                        if (visualizationTimeLine.containsKey(DURATION) && visualizationTimeLine.getLong(GlobalConstants.DURATION) > 0)
                        {
                            if (notifyVisualizationManager)
                            {
                                queries.put(queryId.get(), event.getLong(ID) + SEPARATOR + user.getLong(ID));

                                queryTickers.put(queryId.get(), INTERVAL_SECONDS);
                            }

                            var dataSources = context.getJsonArray(VisualizationConstants.VISUALIZATION_DATA_SOURCES);

                            //as we are sending one datasource at a time and in case of application status it can have multiple datasources as we are preparing from here so generating datasource from here depending upon application either its service or process
                            if (context.containsKey(VisualizationConstants.VISUALIZATION_TYPE) && (context.getString(VisualizationConstants.VISUALIZATION_TYPE).equalsIgnoreCase(VisualizationConstants.VISUALIZATION_TYPE_APPLICATION_AVAILABILITY) || context.getString(VisualizationConstants.VISUALIZATION_TYPE).equalsIgnoreCase(VisualizationConstants.VISUALIZATION_TYPE_APPLICATION_AVAILABILITY_TIME_SERIES)))
                            {
                                dataSources = getApplicationStatusDataSources(context, context.getString(VisualizationConstants.VISUALIZATION_TYPE));
                            }

                            if (!dataSources.isEmpty())
                            {
                                var visualizationDataSources = new HashSet<String>();

                                for (var index = 0; index < dataSources.size(); index++)
                                {
                                    var visualizationDataSource = dataSources.getJsonObject(index);

                                    switch (VisualizationConstants.VisualizationDataSource.valueOfName(visualizationDataSource.getString(VisualizationConstants.TYPE)))
                                    {
                                        case PERFORMANCE_METRIC, CORRELATION_WORKLOG ->
                                                visualizationDataSources.add(VisualizationDataSource.PERFORMANCE_METRIC.getName());

                                        case NETROUTE_METRIC, NETROUTE_AVAILABILITY ->
                                                visualizationDataSources.add(VisualizationDataSource.NETROUTE_METRIC.getName());

                                        case CORRELATED_METRIC, POLICY_STREAM, POLICY_TRIGGER_TICK, POLICY, POLICY_FLAP,
                                             POLICY_ACKNOWLEDGEMENT, POLICY_RESULT, AUDIT, USER_NOTIFICATION, FLOW,
                                             TRAP, LOG, TRAP_ACKNOWLEDGEMENT, TRAP_FLAP, RUNBOOK_WORKLOG,
                                             HEALTH_METRIC, COMPLIANCE ->
                                                visualizationDataSources.add(VisualizationDataSource.LOG.getName());

                                        case OBJECT_AVAILABILITY, CUMULATIVE_OBJECT_STATUS_FLAP,
                                             HOURLY_OBJECT_STATUS_FLAP ->
                                                visualizationDataSources.add(VisualizationDataSource.OBJECT_AVAILABILITY.getName());

                                        case COMPLIANCE_TRAIL, COMPLIANCE_STATS_ENTITY, COMPLIANCE_STATS_POLICY ->
                                                visualizationDataSources.add(VisualizationDataSource.COMPLIANCE.getName());

                                    }
                                }

                                if (visualizationDataSources.size() > 1)//for multiple datasource query will be tracking it..
                                {
                                    compositeQueryTrackers.put(queryId.get(), new HashMap<>());
                                }

                                for (var index = 0; index < dataSources.size(); index++)
                                {
                                    var visualizationDataSource = dataSources.getJsonObject(index);

                                    var subQueryId = 0L;

                                    if (!event.containsKey(SUB_QUERY_ID) || visualizationDataSource.getString(VisualizationConstants.TYPE).equalsIgnoreCase(VisualizationDataSource.POLICY_TRIGGER_TICK.getName()))
                                    {
                                        subQueryId = CommonUtil.newEventId();
                                    }
                                    else if (event.containsKey(SUB_QUERY_ID))
                                    {
                                        subQueryId = event.getLong(SUB_QUERY_ID);
                                    }

                                    if (notifyVisualizationManager && queryStatuses.containsKey(queryId.get()))
                                    {
                                        queryStatuses.get(queryId.get()).put(subQueryId, EMPTY_VALUE);

                                        subQueries.put(subQueryId, new JsonObject());
                                    }

                                    if (event.containsKey(INSTANCE))
                                    {
                                        visualizationDataSource.put(INSTANCE, event.getString(INSTANCE, EMPTY_VALUE));
                                    }

                                    switch (VisualizationConstants.VisualizationDataSource.valueOfName(visualizationDataSource.getString(VisualizationConstants.TYPE)))
                                    {
                                        case PERFORMANCE_METRIC, CORRELATION_WORKLOG, CONFIG_METRIC ->
                                                vertx.eventBus().send(EVENT_VISUALIZATION_METRIC, new JsonObject().mergeIn(context.put(VISUALIZATION_DATA_SOURCES, new JsonArray().add(visualizationDataSource))).put("composite.query", visualizationDataSources.size() > 1).put(COMPOSITE_QUERY_SIZE, visualizationDataSources.size()).put(QUERY_ID, queryId.get()).put(SUB_QUERY_ID, subQueryId));

                                        case CORRELATED_METRIC, POLICY_STREAM, POLICY_TRIGGER_TICK, POLICY, POLICY_FLAP,
                                             POLICY_ACKNOWLEDGEMENT, POLICY_RESULT, AUDIT, USER_NOTIFICATION, FLOW,
                                             TRAP, LOG, TRAP_ACKNOWLEDGEMENT, TRAP_FLAP, RUNBOOK_WORKLOG,
                                             HEALTH_METRIC, STATIC_METRIC, COMPLIANCE ->
                                                vertx.eventBus().send(EVENT_VISUALIZATION_EVENT, new JsonObject().mergeIn(context.put(VISUALIZATION_DATA_SOURCES, new JsonArray().add(visualizationDataSource))).put("composite.query", visualizationDataSources.size() > 1).put(COMPOSITE_QUERY_SIZE, visualizationDataSources.size()).put(QUERY_ID, queryId.get()).put(SUB_QUERY_ID, subQueryId));

                                        case EVENT_HISTORY ->
                                                vertx.eventBus().send(EVENT_VISUALIZATION_EVENT_HISTORY, new JsonObject().mergeIn(context.put(VISUALIZATION_DATA_SOURCES, new JsonArray().add(visualizationDataSource))).put(QUERY_ID, queryId.get()).put(SUB_QUERY_ID, subQueryId));

                                        case OBJECT_AVAILABILITY, CUMULATIVE_OBJECT_STATUS_FLAP,
                                             HOURLY_OBJECT_STATUS_FLAP ->
                                                vertx.eventBus().send(EVENT_VISUALIZATION_AVAILABILITY, new JsonObject().mergeIn(context.put(VISUALIZATION_DATA_SOURCES, new JsonArray().add(visualizationDataSource))).put("composite.query", visualizationDataSources.size() > 1).put(COMPOSITE_QUERY_SIZE, visualizationDataSources.size()).put(QUERY_ID, queryId.get()).put(SUB_QUERY_ID, subQueryId));

                                        case COMPLIANCE_TRAIL, COMPLIANCE_STATS_ENTITY, COMPLIANCE_STATS_POLICY ->
                                                vertx.eventBus().send(EVENT_VISUALIZATION_COMPLIANCE, new JsonObject().mergeIn(context.put(VISUALIZATION_DATA_SOURCES, new JsonArray().add(visualizationDataSource))).put("composite.query", visualizationDataSources.size() > 1).put(COMPOSITE_QUERY_SIZE, visualizationDataSources.size()).put(QUERY_ID, queryId.get()).put(SUB_QUERY_ID, subQueryId));

                                        case NETROUTE_METRIC, NETROUTE_AVAILABILITY ->
                                                vertx.eventBus().send(EVENT_VISUALIZATION_NETROUTE, new JsonObject().mergeIn(context.put(VISUALIZATION_DATA_SOURCES, new JsonArray().add(visualizationDataSource))).put("composite.query", visualizationDataSources.size() > 1).put(COMPOSITE_QUERY_SIZE, visualizationDataSources.size()).put(QUERY_ID, queryId.get()).put(SUB_QUERY_ID, subQueryId));

                                    }
                                }
                            }

                            else
                            {
                                queryStatuses.get(queryId.get()).put(CommonUtil.getLong(NOT_AVAILABLE), "");

                                VisualizationConstants.send(context.getString(VisualizationConstants.VISUALIZATION_CATEGORY), context.getString(VisualizationConstants.VISUALIZATION_TYPE), null, String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, context.getLong(ID) > 0 ? context.getString(VisualizationConstants.VISUALIZATION_NAME) : "Preview Widget", ErrorMessageConstants.INVALID_DATA_SOURCE), queryId.get(), NOT_AVAILABLE, LOGGER, EVENT_VISUALIZATION_RESPONSE);
                            }
                        }

                        else
                        {
                            queryStatuses.get(queryId.get()).put(CommonUtil.getLong(NOT_AVAILABLE), "");

                            VisualizationConstants.send(context.getString(VisualizationConstants.VISUALIZATION_CATEGORY), context.getString(VisualizationConstants.VISUALIZATION_TYPE), null, String.format(ErrorMessageConstants.VISUALIZATION_QUERY_FAILED, context.getLong(ID) > 0 ? context.getString(VisualizationConstants.VISUALIZATION_NAME) : "Preview Widget", ErrorMessageConstants.INVALID_TIMELINE), queryId.get(), NOT_AVAILABLE, LOGGER, EVENT_VISUALIZATION_RESPONSE);
                        }
                    }
                }

                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });

            vertx.eventBus().<JsonObject>localConsumer(EVENT_LOG_EXPORT, message ->
            {
                try
                {
                    var event = message.body();

                    var item = UserConfigStore.getStore().getItemByValue(User.USER_NAME, event.getString(User.USER_NAME));

                    var format = DateTimeUtil.getDateFormat(item.getJsonObject(User.USER_PREFERENCES).getString(User.USER_PREFERENCE_DATE_TIME_FORMAT), item);

                    JsonObject result;

                    if (event.containsKey("unpacked"))
                    {
                        result = event.getJsonObject(RESULT);
                    }
                    else
                    {
                        result = VisualizationConstants.unpack(Buffer.buffer(event.getBinary(RESULT)), LOGGER, false, null, false, true);
                    }

                    if (result != null)
                    {
                        var writer = new CSVWriterBuilder(new FileWriter(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + event.getString(UI_EVENT_UUID).trim() + ".csv")).withSeparator(',').withLineEnd("\n").build();

                        var columns = event.containsKey(VISUALIZATION_EXTRA_COLUMNS) ? event.getJsonArray(VISUALIZATION_EXTRA_COLUMNS) : null;

                        var idx = 0;

                        String[] cells;

                        if (columns != null)
                        {
                            cells = new String[2 + columns.size()];
                        }
                        else
                        {
                            cells = new String[2];
                        }

                        cells[idx++] = "Time";

                        cells[idx++] = "Message";

                        if (columns != null && !columns.isEmpty())
                        {
                            for (var i = 0; i < columns.size(); i++)
                            {
                                cells[idx++] = CommonUtil.getString(columns.getValue(i));
                            }
                        }

                        //  header
                        writer.writeNext(cells, true);

                        if (result.getJsonArray(RESULT) != null)
                        {
                            var rows = result.getJsonArray(RESULT);

                            for (var index = 0; index < rows.size(); index++)
                            {
                                if (rows.getJsonObject(index) != null)
                                {
                                    var row = rows.getJsonObject(index);

                                    if (!row.getString(MESSAGE + CARET_SEPARATOR + VALUE, EMPTY_VALUE).isEmpty())
                                    {
                                        idx = 0;

                                        // Add mandatory elements
                                        cells[idx++] = format.format(new Date(row.getLong(TIMESTAMP, DateTimeUtil.currentSeconds())));

                                        cells[idx++] = row.getString(MESSAGE + CARET_SEPARATOR + VALUE, EMPTY_VALUE);

                                        //Add Dynamic Fields...
                                        if (columns != null && !row.getString(EVENT + CARET_SEPARATOR + VALUE, EMPTY_VALUE).isEmpty())
                                        {
                                            VisualizationConstants.extractEvent(Buffer.buffer(row.getBinary(EVENT + CARET_SEPARATOR + VALUE)), row, null, null, LOGGER);

                                            for (var i = 0; i < columns.size(); i++)
                                            {
                                                cells[idx++] = CommonUtil.getString(row.getValue(columns.getString(i), EMPTY_VALUE));
                                            }
                                        }

                                        writer.writeNext(cells, true);
                                    }

                                    writer.flushQuietly();
                                }
                            }

                            writer.flushQuietly();

                            EventBusConstants.publish(event.getString(APIConstants.SESSION_ID), EventBusConstants.UI_NOTIFICATION_CSV_EXPORT_READY, new JsonObject().put(GlobalConstants.FILE_NAME, event.getString(UI_EVENT_UUID).trim() + ".csv").put(STATUS, STATUS_SUCCEED).put(UI_EVENT_UUID, event.getString(UI_EVENT_UUID)));
                        }

                        writer.close();
                    }
                }

                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }
            });

            /*
            Following Local Consumer Purpose is to get acknowledgment from db and then to set the timer as per the ack in queryTicker.
             */
            vertx.eventBus().<Long>localConsumer(EVENT_DATASTORE_ACKNOWLEDGEMENT, message ->
            {
                try
                {
                    if (message.body() != null)
                    {
                        var queryId = message.body();

                        if (queryTickers.containsKey(queryId))
                        {
                            queryTickers.put(queryId, INTERVAL_SECONDS);
                        }
                    }
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);
                }

            }).exceptionHandler(LOGGER::error);
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        promise.complete();
    }

    private void publish(long queryId, long subQueryId, short subQueryProgress, Buffer buffer)
    {
        if (subQueries.containsKey(subQueryId))
        {
            if (subQueries.get(subQueryId).containsKey(VisualizationConstants.VISUALIZATION_TYPE) && (!subQueries.get(subQueryId).getString(VisualizationConstants.VISUALIZATION_TYPE).equalsIgnoreCase(VisualizationConstants.VISUALIZATION_TYPE_APPLICATION_AVAILABILITY_TIME_SERIES)) && !subQueries.get(subQueryId).getString(VisualizationConstants.VISUALIZATION_TYPE).equalsIgnoreCase(VisualizationConstants.VISUALIZATION_TYPE_AVAILABILITY_TIME_SERIES))
            {
                subQueries.get(subQueryId).remove(VisualizationConstants.VISUALIZATION_TIMELINE);
            }

            sessions.get(queryId).forEach((key, value) -> EventBusConstants.publish(key.split(COLUMN_SEPARATOR)[0], EventBusConstants.UI_ACTION_VISUALIZATION_RENDER, new JsonObject().put(UI_EVENT_UUID, key.split(COLUMN_SEPARATOR)[1]).mergeIn(value.getLong(ID) > 0 ? WidgetConfigStore.getStore().getItem(value.getLong(ID)).mergeIn(subQueries.get(subQueryId)) : value.mergeIn(subQueries.get(subQueryId))).put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ZERO).put(RESULT, buffer.getBytes())));
        }

        else
        {
            sessions.get(queryId).forEach((key, value) -> EventBusConstants.publish(key.split(COLUMN_SEPARATOR)[0], EventBusConstants.UI_ACTION_VISUALIZATION_RENDER, new JsonObject().put(UI_EVENT_UUID, key.split(COLUMN_SEPARATOR)[1]).mergeIn(value.getLong(ID) > 0 ? WidgetConfigStore.getStore().getItem(value.getLong(ID)) : value).put(EventBusConstants.EVENT_COMPRESSION_TYPE, COMPRESSION_TYPE_ZERO).put(RESULT, buffer.getBytes())));
        }

        if (subQueryProgress >= 100)
        {
            subQueries.remove(subQueryId);
        }
    }

    private void cleanUp(long queryId)
    {
        queryTrackers.remove(queryId);

        if (queryStatuses.containsKey(queryId))
        {
            queryStatuses.remove(queryId).keySet().forEach(subQueries::remove);
        }

        queries.remove(queryId);

        queryTickers.remove(queryId);

        sessions.remove(queryId);

        queryExecutionTicks.remove(queryId);

        compositeQueryTrackers.remove(queryId);
    }

    private JsonArray getApplicationStatusDataSources(JsonObject context, String widgetType)
    {
        var dataSourceTypes = new HashSet<String>();

        var dataSources = new JsonArray();

        var processes = new JsonArray();

        var services = new JsonArray();

        if (context.containsKey(NMSConstants.APP_PROCESS) && !context.getJsonObject(NMSConstants.APP_PROCESS).isEmpty())
        {
            context.getJsonObject(NMSConstants.APP_PROCESS).getMap().values().forEach(value ->
            {
                var object = JsonObject.mapFrom(value);

                if (object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.SYSTEM_PROCESS))
                {
                    dataSourceTypes.add(NMSConstants.SYSTEM_PROCESS);

                    processes.add(object.getString(AIOpsObject.OBJECT_NAME));
                }
                else if (object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.SYSTEM_SERVICE))
                {
                    dataSourceTypes.add(NMSConstants.SYSTEM_SERVICE);

                    services.add(object.getString(AIOpsObject.OBJECT_NAME));
                }
            });
        }

        dataSourceTypes.forEach(type ->
        {
            JsonArray sources;

            if (VisualizationConstants.VISUALIZATION_TYPE_APPLICATION_AVAILABILITY_TIME_SERIES.equalsIgnoreCase(widgetType))
            {
                sources = (JsonArray) VisualizationConstants.VISUALIZATION_APP_DATA_SOURCES.get(type + ".availability.statistics").getValue(VISUALIZATION_DATA_SOURCES);
            }
            else
            {
                sources = (JsonArray) VisualizationConstants.VISUALIZATION_APP_DATA_SOURCES.get(type).getValue(VISUALIZATION_DATA_SOURCES);
            }

            for (var index = 0; index < sources.size(); index++)
            {
                var filters = new JsonObject(VisualizationConstants.VISUALIZATION_DATA_FILTER_JSON_TEMPLATE);

                if (type.equalsIgnoreCase(NMSConstants.SYSTEM_PROCESS))
                {
                    filters.getJsonObject(DATA_FILTER).getJsonArray(CONDITION_GROUPS).getJsonObject(0).getJsonArray(CONDITIONS).add(new JsonObject().put(OPERAND, type).put(OPERATOR, DatastoreConstants.ConditionGroup.IN.getName()).put(VALUE, processes));
                }
                else
                {
                    filters.getJsonObject(DATA_FILTER).getJsonArray(CONDITION_GROUPS).getJsonObject(0).getJsonArray(CONDITIONS).add(new JsonObject().put(OPERAND, type).put(OPERATOR, DatastoreConstants.ConditionGroup.IN.getName()).put(VALUE, services));
                }

                var dataSource = (JsonObject) sources.getValue(index);

                dataSource.put(FILTERS, filters);

                if (widgetType.equalsIgnoreCase(VisualizationConstants.VISUALIZATION_TYPE_APPLICATION_AVAILABILITY_TIME_SERIES))//as we have three different time series with three different visualization datasource
                {
                    dataSources.add(new JsonObject().mergeIn(dataSource).put(VisualizationConstants.VISUALIZATION_TIMELINE, new JsonObject().put(VisualizationConstants.RELATIVE_TIMELINE, VisualizationTimeline.LAST_DAY.getName())));

                    dataSources.add(new JsonObject().mergeIn(dataSource).put(VisualizationConstants.VISUALIZATION_TIMELINE, new JsonObject().put(VisualizationConstants.RELATIVE_TIMELINE, VisualizationTimeline.LAST_7_DAYS.getName())));

                    dataSources.add(new JsonObject().mergeIn(dataSource).put(VisualizationConstants.VISUALIZATION_TIMELINE, new JsonObject().put(VisualizationConstants.RELATIVE_TIMELINE, VisualizationTimeline.LAST_15_DAYS.getName())));
                }

                else
                {
                    dataSources.add(dataSource);
                }
            }


        });

        return dataSources;
    }
}
