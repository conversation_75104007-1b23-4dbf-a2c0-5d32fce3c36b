/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *  Date            Author          Notes
 *  17-Mar-2025     Chandresh       Initial Version
 */

package com.mindarray.patch;

import com.mindarray.Bootstrap;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.RunbookPlugin;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.runbook.Runbook;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.RunbookPluginConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.db.ConfigDBConstants.FIELD_NAME;
import static com.mindarray.nms.NMSConstants.PING_CHECK_STATUS;

public class Patch8018 implements Patch
{
    private static final Logger LOGGER = new Logger(Patch8018.class, MOTADATA_PATCH, "Patch 8.0.18");

    private static final String VERSION = "8.0.18";

    @Override
    public String getVersion()
    {
        return VERSION;
    }

    @Override
    public Future<Void> doPatch()
    {
        var promise = Promise.<Void>promise();

        var futures = new ArrayList<Future<Void>>();

        LOGGER.info("executing patch 8.0.18");

        futures.add(assignRunbookPlugins());

        Future.join(futures).onComplete(result ->
        {
            if (result.succeeded())
            {
                promise.complete();

                LOGGER.info("successfully executed patch 8.0.18");
            }
            else
            {
                promise.fail(result.cause());

                LOGGER.error(result.cause());
            }
        });

        return promise.future();
    }

    private Future<Void> assignRunbookPlugins()
    {
        var promise = Promise.<Void>promise();

        try
        {
            var items = ObjectConfigStore.getStore().getItems();

            var pingRunbook = RunbookPluginConfigStore.getStore().getItem(Runbook.RunbookPluginId.PING.getName());

            var traceRouteRunbook = RunbookPluginConfigStore.getStore().getItem(Runbook.RunbookPluginId.TRACE_ROUTE.getName());

            var pingRunbookEntities = pingRunbook.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES) ? pingRunbook.getJsonArray(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES) : new JsonArray(new ArrayList<>(1));

            var traceRouteRunbookEntities = traceRouteRunbook.containsKey(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES) ? traceRouteRunbook.getJsonArray(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES) : new JsonArray(new ArrayList<>(1));

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                if (item.containsKey(AIOpsObject.OBJECT_CONTEXT))
                {
                    item.mergeIn(item.getJsonObject(AIOpsObject.OBJECT_CONTEXT));

                    item.remove(AIOpsObject.OBJECT_CONTEXT);
                }

                if (CommonUtil.isNotNullOrEmpty(item.getString(PING_CHECK_STATUS)) && YES.equalsIgnoreCase(item.getString(PING_CHECK_STATUS)))
                {
                    if (!pingRunbookEntities.contains(item.getLong(ID)))
                    {
                        pingRunbookEntities.add(item.getLong(ID));
                    }

                    if (!traceRouteRunbookEntities.contains(item.getLong(ID)))
                    {
                        traceRouteRunbookEntities.add(item.getLong(ID));
                    }
                }
            }

            var pingPromise = Promise.<Void>promise();

            var traceRoutePromise = Promise.<Void>promise();

            Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_RUNBOOK_PLUGIN,
                    new JsonObject().put(FIELD_NAME, ID).put(VALUE, pingRunbook.getLong(ID)),
                    new JsonObject().put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, pingRunbookEntities),
                    DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                    result ->
                    {
                        if (result.succeeded())
                        {
                            RunbookPluginConfigStore.getStore().updateItem(pingRunbook.getLong(ID)).onComplete(asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    pingPromise.complete();
                                }
                                else
                                {
                                    LOGGER.error(asyncResult.cause());
                                }
                            });
                        }
                        else
                        {
                            LOGGER.error(result.cause());
                        }
                    });

            Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_RUNBOOK_PLUGIN,
                    new JsonObject().put(FIELD_NAME, ID).put(VALUE, traceRouteRunbook.getLong(ID)),
                    new JsonObject().put(RunbookPlugin.RUNBOOK_PLUGIN_ENTITIES, traceRouteRunbookEntities),
                    DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                    result ->
                    {
                        if (result.succeeded())
                        {
                            RunbookPluginConfigStore.getStore().updateItem(traceRouteRunbook.getLong(ID)).onComplete(asyncResult ->
                            {
                                if (asyncResult.succeeded())
                                {
                                    traceRoutePromise.complete();
                                }
                                else
                                {
                                    LOGGER.error(asyncResult.cause());
                                }
                            });
                        }
                        else
                        {
                            LOGGER.error(result.cause());
                        }
                    });

            Future.join(pingPromise.future(), traceRoutePromise.future()).onComplete(result ->
            {
                if (result.succeeded())
                {
                    promise.complete();
                }
                else
                {
                    LOGGER.error(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            return Future.failedFuture(exception);
        }

        return promise.future();
    }
}
