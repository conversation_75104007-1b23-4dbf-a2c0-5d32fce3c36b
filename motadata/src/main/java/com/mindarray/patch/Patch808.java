/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.patch;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.Configuration;
import com.mindarray.api.DataRetentionPolicy;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.store.ConfigurationConfigStore;
import com.mindarray.store.DataRetentionPolicyConfigStore;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.db.ConfigDBConstants.FIELD_NAME;

public class Patch808 implements Patch
{
    private static final Logger LOGGER = new Logger(Patch808.class, MOTADATA_PATCH, "Patch 8.0.8");

    private static final String VERSION = "8.0.8";

    @Override
    public String getVersion()
    {
        return VERSION;
    }

    @Override
    public Future<Void> doPatch()
    {
        var promise = Promise.<Void>promise();

        try
        {
            var futures = new ArrayList<Future<Void>>();

            var items = ConfigurationConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                var valid = false;

                if (item.containsKey("config.last.firmware.upgrade.status"))
                {
                    item.put(Configuration.CONFIG_LAST_UPGRADE_STATUS, item.getString("config.last.firmware.upgrade.status"));

                    valid = true;
                }

                if (item.containsKey("config.last.firmware.upgrade.time"))
                {
                    item.put(Configuration.CONFIG_LAST_UPGRADE_TIME, item.getLong("config.last.firmware.upgrade.time"));

                    valid = true;
                }

                if (valid)
                {
                    var future = Promise.<Void>promise();

                    futures.add(future.future());

                    Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_CONFIGURATION, new JsonObject().put(FIELD_NAME, GlobalConstants.ID).put(VALUE, item.getLong(ID)),
                            item, DEFAULT_USER, MOTADATA_SYSTEM, result ->
                            {
                                if (result.succeeded())
                                {
                                    ConfigurationConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                                    {
                                        if (asyncResult.succeeded())
                                        {
                                            LOGGER.info(String.format("Successfully updated config object %s", item.getInteger(Configuration.CONFIG_OBJECT)));

                                            future.complete();
                                        }
                                        else
                                        {
                                            LOGGER.warn(String.format("Unable to update configuration config store item %s with reason %s", item.encode(), asyncResult.cause().getMessage()));

                                            future.fail(asyncResult.cause().getMessage());
                                        }
                                    });
                                }
                                else
                                {
                                    LOGGER.warn(String.format("Unable to update configuration config store item %s with reason %s", item.encode(), result.cause().getMessage()));

                                    future.fail(result.cause().getMessage());
                                }
                            });
                }
            }

            var asyncFuture = Promise.<Void>promise();

            futures.add(asyncFuture.future());

            var item = DataRetentionPolicyConfigStore.getStore().getItem(GlobalConstants.DEFAULT_ID);

            if (item != null)
            {
                item.getJsonObject(DataRetentionPolicy.DATA_RETENTION_POLICY_CONTEXT).getJsonObject("CONFIG").put(GlobalConstants.VERSION, 15);

                Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_DATA_RETENTION_POLICY,
                        new JsonObject().put(FIELD_NAME, ID).put(VALUE, GlobalConstants.DEFAULT_ID),
                        item,
                        DEFAULT_USER,
                        SYSTEM_REMOTE_ADDRESS, result ->
                        {
                            if (result.succeeded())
                            {
                                DataRetentionPolicyConfigStore.getStore().updateItem(GlobalConstants.DEFAULT_ID).onComplete(handler ->
                                {
                                    LOGGER.info("Data retention value changed for Config");

                                    asyncFuture.complete();
                                });
                            }
                            else
                            {
                                LOGGER.warn(String.format("Failed to update Data retention with reason : %s", result.cause().getMessage()));

                                asyncFuture.fail(result.cause());
                            }
                        });
            }
            else
            {
                LOGGER.warn("Data retention item not found");

                asyncFuture.fail("Data retention item not found");
            }

            Future.join(futures).onComplete(result -> promise.complete());
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }
}
