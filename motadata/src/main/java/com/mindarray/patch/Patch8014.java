/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.patch;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.api.APIConstants;
import com.mindarray.api.Integration;
import com.mindarray.api.IntegrationProfile;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.integration.IntegrationConstants;
import com.mindarray.integration.ServiceOpsIntegration;
import com.mindarray.notification.Notification;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.runbook.Runbook;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Map;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.CredentialProfile.CREDENTIAL_PROFILE_CONTEXT;
import static com.mindarray.api.CredentialProfile.DEFAULT_EMAIL_CREDENTIAL_PROFILE;
import static com.mindarray.api.MailServerConfiguration.*;
import static com.mindarray.db.ConfigDBConstants.COLLECTION_INTEGRATION_PROFILE;
import static com.mindarray.db.ConfigDBConstants.FIELD_NAME;

public class Patch8014 implements Patch
{
    private static final Logger LOGGER = new Logger(Patch8014.class, MOTADATA_PATCH, "Patch 8.0.14");

    private static final String VERSION = "8.0.14";

    @Override
    public String getVersion()
    {
        return VERSION;
    }

    @Override
    public Future<Void> doPatch()
    {
        var promise = Promise.<Void>promise();

        var futures = new ArrayList<Future<Void>>();

        LOGGER.info("executing patch 8.0.14");

        futures.add(executePatch());

        futures.add(executeCustomTabPatch());

        futures.add(executeMailServerConfigurationPatch());

        Future.join(futures).onComplete(result ->
        {
            if (result.succeeded())
            {
                promise.complete();

                LOGGER.info("successfully executed patch 8.0.14");
            }
            else
            {
                promise.fail(result.cause());

                LOGGER.error(result.cause());
            }
        });

        return promise.future();
    }

    private Future<Void> executePatch()
    {
        var promise = Promise.<Void>promise();

        getServiceOpsIntegrationProfiles().onComplete(result ->
        {
            if (result.succeeded())
            {
                try
                {
                    var integrationProfiles = result.result();

                    if (integrationProfiles != null && !integrationProfiles.isEmpty())
                    {
                        // need to use reflection here as cache store is already initialized before patch execution
                        var field = IntegrationCacheStore.class.getDeclaredField("items");

                        field.setAccessible(true);

                        var items = (Map<String, Object>) field.get(IntegrationCacheStore.getStore());

                        field = IntegrationCacheStore.class.getDeclaredField("profiles");

                        field.setAccessible(true);

                        var profiles = (Map<String, Long>) field.get(IntegrationCacheStore.getStore());

                        for (var entry : items.entrySet())
                        {
                            var tokens = entry.getKey().split(SEPARATOR_WITH_ESCAPE);

                            profiles.put(tokens.length == 6 ? String.join(SEPARATOR, Arrays.copyOfRange(tokens, 1, 6)) : String.join(SEPARATOR, Arrays.copyOfRange(tokens, 1, 5)), integrationProfiles.getLong(Severity.CRITICAL.name()));
                        }
                    }

                    var futures = new ArrayList<Future<Void>>();

                    futures.add(updateMetricPolicies(integrationProfiles));

                    futures.add(updateEventPolicies());

                    Future.join(futures).onComplete(asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            promise.complete();

                            LOGGER.info("successfully executed policy and integration patch");
                        }
                        else
                        {
                            promise.fail(asyncResult.cause());

                            LOGGER.error(asyncResult.cause());
                        }
                    });
                }
                catch (Exception exception)
                {
                    LOGGER.error(exception);

                    promise.fail(exception.getCause());
                }
            }
            else
            {
                LOGGER.error(result.cause());

                promise.fail(result.cause());
            }
        });

        return promise.future();
    }

    private Future<Void> updateMetricPolicies(JsonObject profiles)
    {
        var promise = Promise.<Void>promise();

        try
        {
            LOGGER.info("updating metric policies");

            var items = MetricPolicyConfigStore.getStore().getItems();

            var futures = new ArrayList<Future<Void>>();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                var runbookContext = item.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS);

                item.put(PolicyEngineConstants.POLICY_ACTIONS, new JsonObject());

                if (item.containsKey("policy.email.notification.subject") && CommonUtil.isNotNullOrEmpty(item.getString("policy.email.notification.subject")))
                {
                    item.put(PolicyEngineConstants.POLICY_TITLE, item.getString("policy.email.notification.subject"));
                }

                // migrate runbook context
                if (runbookContext != null && !runbookContext.isEmpty())
                {
                    var runbook = new JsonObject();

                    var integration = new JsonObject();

                    LOGGER.info("migrating existing runbook context");

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("found existing runbooks attached : %s", runbookContext.encode()));
                    }

                    for (var severity : GlobalConstants.Severity.values())
                    {
                        if (runbookContext.containsKey(severity.name()) && !runbookContext.getJsonArray(severity.name()).isEmpty())
                        {
                            if (!runbook.containsKey(severity.name()))
                            {
                                runbook.put(severity.name(), new JsonArray());
                            }

                            if (severity != Severity.CLEAR && profiles != null && !profiles.isEmpty() && !integration.containsKey(severity.name()))
                            {
                                integration.put(severity.name(), new JsonArray());
                            }

                            for (var id : runbookContext.getJsonArray(severity.name()))
                            {
                                if (id.equals(Runbook.RunbookPluginId.SERVICEOPS_TICKET.getName())
                                        && profiles != null && !profiles.isEmpty() && profiles.containsKey(severity.name()) && profiles.getLong(severity.name()) != null)
                                {
                                    integration.getJsonArray(severity.name()).add(new JsonObject().put(ID, profiles.getLong(severity.name())));
                                }
                                else if (!id.equals(Runbook.RunbookPluginId.SERVICEOPS_TICKET.getName()))
                                {
                                    runbook.getJsonArray(severity.name()).add(new JsonObject().put(ID, CommonUtil.getLong(id)));
                                }
                            }
                        }
                    }

                    item.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS)
                            .put(PolicyEngineConstants.PolicyTriggerActionType.RUNBOOK.getName(), runbook);

                    if (!integration.isEmpty())
                    {
                        item.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS)
                                .put(PolicyEngineConstants.PolicyTriggerActionType.INTEGRATION.getName(), integration);
                    }

                    LOGGER.info("runbook context updated");

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("updated runbook context : %s", runbook.encode()));
                    }
                }

                // migrate email notification context
                var contexts = item.getJsonArray("policy.notification.context");

                if (contexts != null && !contexts.isEmpty())
                {
                    var email = new JsonObject();

                    LOGGER.info("migrating existing email notification context");

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("found existing notification context : %s", contexts.encode()));
                    }

                    if (!item.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS).containsKey(PolicyEngineConstants.PolicyTriggerActionType.NOTIFICATION.getName()))
                    {
                        item.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS).put(PolicyEngineConstants.PolicyTriggerActionType.NOTIFICATION.getName(), new JsonObject());
                    }

                    for (var i = 0; i < contexts.size(); i++)
                    {
                        var context = contexts.getJsonObject(i);

                        // severities
                        for (var severity : context.getJsonArray("policy.notification.severity"))
                        {
                            if (!email.containsKey(CommonUtil.getString(severity)))
                            {
                                email.put(CommonUtil.getString(severity), new JsonArray());
                            }

                            // email recipients
                            for (var recipient : context.getJsonArray("policy.email.notification.recipients"))
                            {
                                email.getJsonArray(CommonUtil.getString(severity))
                                        .add(new JsonObject().put(PolicyEngineConstants.RECIPIENT, CommonUtil.getString(recipient))
                                                .put(PolicyEngineConstants.RECIPIENT_TYPE, PolicyEngineConstants.RecipientType.EMAIl.getName())
                                                .put("index", i));
                            }

                            // user recipients
                            for (var recipient : context.getJsonArray("policy.user.notification.recipients"))
                            {
                                email.getJsonArray(CommonUtil.getString(severity))
                                        .add(new JsonObject().put(PolicyEngineConstants.RECIPIENT, CommonUtil.getString(recipient))
                                                .put(PolicyEngineConstants.RECIPIENT_TYPE, PolicyEngineConstants.RecipientType.USER.getName())
                                                .put("index", i));
                            }
                        }
                    }

                    item.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS)
                            .getJsonObject(PolicyEngineConstants.PolicyTriggerActionType.NOTIFICATION.getName())
                            .put(Notification.NotificationType.EMAIL.getName(), email);

                    LOGGER.info("email notification context updated");

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("updated email notification context : %s", email.encode()));
                    }
                }

                // migrate sound notification context
                var soundNotificationSeverities = item.getJsonArray("policy.sound.notification.severity");

                var soundNotification = item.containsKey("policy.sound.notification") && item.getString("policy.sound.notification").equalsIgnoreCase(YES);

                if (soundNotification && soundNotificationSeverities != null && !soundNotificationSeverities.isEmpty())
                {
                    var sound = new JsonObject();

                    LOGGER.info("migrating existing sound notification context");

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("found existing sound notification context : %s", soundNotificationSeverities.encode()));
                    }

                    if (!item.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS).containsKey(PolicyEngineConstants.PolicyTriggerActionType.NOTIFICATION.getName()))
                    {
                        item.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS).put(PolicyEngineConstants.PolicyTriggerActionType.NOTIFICATION.getName(), new JsonObject());
                    }

                    for (var severity : soundNotificationSeverities)
                    {
                        sound.put(CommonUtil.getString(severity), new JsonObject());
                    }

                    item.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS)
                            .getJsonObject(PolicyEngineConstants.PolicyTriggerActionType.NOTIFICATION.getName())
                            .put(Notification.NotificationType.SOUND.getName(), sound);

                    LOGGER.info("sound notification context updated");

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("updated sound notification context : %s", sound.encode()));
                    }
                }

                // migrate renotification context
                contexts = item.getJsonArray("policy.renotification.context");

                var policyRenotification = item.containsKey("policy.renotify") && item.getString("policy.renotify").equalsIgnoreCase(YES);

                if (policyRenotification && contexts != null && !contexts.isEmpty())
                {
                    var renotification = new JsonObject();

                    LOGGER.info("migrating existing renotification context");

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("found existing renotification context : %s", contexts.encode()));
                    }

                    renotification.put(PolicyEngineConstants.RENOTIFY_ACKNOWLEDGED, item.getString("policy.renotify.acknowledged"));

                    for (var i = 0; i < contexts.size(); i++)
                    {
                        var context = contexts.getJsonObject(i);

                        var severity = context.getString("policy.notification.severity");

                        if (!renotification.containsKey(severity))
                        {
                            renotification.put(severity, new JsonObject()
                                    .put(PolicyEngineConstants.RECIPIENTS, new JsonArray())
                                    .put(PolicyEngineConstants.TIMER_SECONDS, context.getInteger("policy.renotification.timer.seconds")));
                        }

                        if (context.containsKey("policy.email.notification.recipients") && context.getValue("policy.email.notification.recipients") != null)
                        {
                            // email recipients
                            for (var recipient : context.getJsonArray("policy.email.notification.recipients"))
                            {
                                renotification.getJsonObject(severity).getJsonArray(PolicyEngineConstants.RECIPIENTS)
                                        .add(new JsonObject().put(PolicyEngineConstants.RECIPIENT, CommonUtil.getString(recipient))
                                                .put(PolicyEngineConstants.RECIPIENT_TYPE, PolicyEngineConstants.RecipientType.EMAIl.getName()));
                            }
                        }

                        if (context.containsKey("policy.user.notification.recipients") && context.getValue("policy.user.notification.recipients") != null)
                        {
                            // user recipients
                            for (var recipient : context.getJsonArray("policy.user.notification.recipients"))
                            {
                                renotification.getJsonObject(severity).getJsonArray(PolicyEngineConstants.RECIPIENTS)
                                        .add(new JsonObject().put(PolicyEngineConstants.RECIPIENT, CommonUtil.getString(recipient))
                                                .put(PolicyEngineConstants.RECIPIENT_TYPE, PolicyEngineConstants.RecipientType.USER.getName()));
                            }
                        }
                    }

                    item.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS)
                            .put(PolicyEngineConstants.PolicyTriggerActionType.RENOTIFICATION.getName(), renotification);

                    LOGGER.info("renotification context updated");

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("updated renotification context : %s", renotification.encode()));
                    }
                }

                var future = Promise.<Void>promise();

                futures.add(future.future());

                Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_METRIC_POLICY,
                        new JsonObject().put(FIELD_NAME, GlobalConstants.ID).put(VALUE, item.getLong(ID)),
                        item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                        {
                            if (result.succeeded())
                            {
                                MetricPolicyConfigStore.getStore().updateItem(item.getLong(ID))
                                        .onComplete(asyncResult -> future.complete());
                            }
                            else
                            {
                                future.fail(result.cause());

                                LOGGER.error(result.cause());
                            }
                        });

            }

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    LOGGER.info("all metric policies updated successfully!");

                    promise.complete();
                }
                else
                {
                    promise.fail(result.cause());

                    LOGGER.error(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            if (!promise.future().isComplete())
            {
                promise.fail(exception);
            }

            LOGGER.error(exception);
        }

        return promise.future();
    }

    private Future<Void> updateEventPolicies()
    {
        var promise = Promise.<Void>promise();

        try
        {
            LOGGER.info("updating event policies");

            var items = EventPolicyConfigStore.getStore().getItems();

            var futures = new ArrayList<Future<Void>>();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                var runbookContexts = item.getValue(PolicyEngineConstants.POLICY_ACTIONS) instanceof JsonArray ? item.getJsonArray(PolicyEngineConstants.POLICY_ACTIONS) : null;

                item.put(PolicyEngineConstants.POLICY_ACTIONS, new JsonObject());

                if (item.containsKey("policy.email.notification.subject") && CommonUtil.isNotNullOrEmpty(item.getString("policy.email.notification.subject")))
                {
                    item.put(PolicyEngineConstants.POLICY_TITLE, item.getString("policy.email.notification.subject"));
                }

                // migrate runbook context
                if (runbookContexts != null && !runbookContexts.isEmpty())
                {
                    var runbook = new JsonObject();

                    LOGGER.info("migrating existing runbook context");

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("found existing runbooks attached : %s", runbookContexts.encode()));
                    }

                    if (item.containsKey(PolicyEngineConstants.POLICY_CONTEXT)
                            && item.getJsonObject(PolicyEngineConstants.POLICY_CONTEXT).containsKey(PolicyEngineConstants.POLICY_SEVERITY))
                    {
                        var severity = item.getJsonObject(PolicyEngineConstants.POLICY_CONTEXT).getString(PolicyEngineConstants.POLICY_SEVERITY);

                        if (!runbook.containsKey(severity))
                        {
                            runbook.put(severity, new JsonArray());
                        }

                        for (var id : runbookContexts)
                        {
                            runbook.getJsonArray(severity).add(new JsonObject().put(ID, CommonUtil.getLong(id)));
                        }

                        item.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS)
                                .put(PolicyEngineConstants.PolicyTriggerActionType.RUNBOOK.getName(), runbook);

                        LOGGER.info("runbook context updated");

                        if (CommonUtil.traceEnabled())
                        {
                            LOGGER.trace(String.format("updated runbook context : %s", runbook.encode()));
                        }
                    }
                }

                // migrate email notification context
                var contexts = item.getJsonArray("policy.notification.context");

                if (contexts != null && !contexts.isEmpty())
                {
                    var email = new JsonObject();

                    LOGGER.info("migrating existing email notification context");

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("found existing notification context : %s", contexts.encode()));
                    }

                    if (!item.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS).containsKey(PolicyEngineConstants.PolicyTriggerActionType.NOTIFICATION.getName()))
                    {
                        item.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS).put(PolicyEngineConstants.PolicyTriggerActionType.NOTIFICATION.getName(), new JsonObject());
                    }


                    for (var i = 0; i < contexts.size(); i++)
                    {
                        var context = contexts.getJsonObject(i);

                        if (item.containsKey(PolicyEngineConstants.POLICY_CONTEXT)
                                && item.getJsonObject(PolicyEngineConstants.POLICY_CONTEXT).containsKey(PolicyEngineConstants.POLICY_SEVERITY))
                        {
                            var severity = item.getJsonObject(PolicyEngineConstants.POLICY_CONTEXT).getString(PolicyEngineConstants.POLICY_SEVERITY);

                            if (!email.containsKey(CommonUtil.getString(severity)))
                            {
                                email.put(severity, new JsonArray());
                            }

                            // email recipients
                            for (var recipient : context.getJsonArray("policy.email.notification.recipients"))
                            {
                                email.getJsonArray(severity)
                                        .add(new JsonObject().put(PolicyEngineConstants.RECIPIENT, CommonUtil.getString(recipient))
                                                .put(PolicyEngineConstants.RECIPIENT_TYPE, PolicyEngineConstants.RecipientType.EMAIl.getName())
                                                .put("index", i));
                            }

                            // user recipients
                            for (var recipient : context.getJsonArray("policy.user.notification.recipients"))
                            {
                                email.getJsonArray(severity)
                                        .add(new JsonObject().put(PolicyEngineConstants.RECIPIENT, CommonUtil.getString(recipient))
                                                .put(PolicyEngineConstants.RECIPIENT_TYPE, PolicyEngineConstants.RecipientType.USER.getName())
                                                .put("index", i));
                            }
                        }
                    }

                    item.getJsonObject(PolicyEngineConstants.POLICY_ACTIONS)
                            .getJsonObject(PolicyEngineConstants.PolicyTriggerActionType.NOTIFICATION.getName())
                            .put(Notification.NotificationType.EMAIL.getName(), email);

                    LOGGER.info("email notification context updated");

                    if (CommonUtil.traceEnabled())
                    {
                        LOGGER.trace(String.format("updated email notification context : %s", email.encode()));
                    }
                }

                var future = Promise.<Void>promise();

                futures.add(future.future());

                Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_EVENT_POLICY,
                        new JsonObject().put(FIELD_NAME, GlobalConstants.ID).put(VALUE, item.getLong(ID)),
                        item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                        {
                            if (result.succeeded())
                            {
                                EventPolicyConfigStore.getStore().updateItem(item.getLong(ID))
                                        .onComplete(asyncResult -> future.complete());
                            }
                            else
                            {
                                future.fail(result.cause());

                                LOGGER.error(result.cause());
                            }
                        });

            }

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    LOGGER.info("all event policies updated successfully!");

                    promise.complete();
                }
                else
                {
                    promise.fail(result.cause());

                    LOGGER.error(result.cause());
                }
            });
        }
        catch (Exception exception)
        {
            if (!promise.future().isComplete())
            {
                promise.fail(exception);
            }

            LOGGER.error(exception);
        }

        return promise.future();
    }

    private Future<JsonObject> getServiceOpsIntegrationProfiles()
    {
        var promise = Promise.<JsonObject>promise();

        var profiles = new JsonObject();

        try
        {
            var item = IntegrationConfigStore.getStore().getItemByValue(Integration.INTEGRATION_TYPE, IntegrationConstants.IntegrationType.SERVICEOPS.getName());

            if (item != null && !item.isEmpty())
            {
                if (item.containsKey("integration.server.url"))
                {
                    var urgencies = item.getJsonObject("urgency");

                    var futures = new ArrayList<Future<Void>>();

                    for (var urgency : urgencies.getMap().entrySet())
                    {
                        var future = Promise.<Void>promise();

                        futures.add(future.future());

                        var context = new JsonObject().put(IntegrationProfile.INTEGRATION_PROFILE_NAME, "ServiceOps Integration Profile - " + urgency.getValue())
                                .put(IntegrationProfile.INTEGRATION, item.getLong(ID))
                                .put(IntegrationProfile.INTEGRATION_PROFILE_CONTEXT, new JsonObject()
                                        .put("urgencyName", urgency.getKey())
                                        .put("impactName", "On department")
                                        .put(IntegrationProfile.AUTO_CLOSE_TICKET_STATUS, item.getString("integration.auto.close.ticket.status", NO))
                                        .put(IntegrationProfile.AUTO_CLOSE_TICKET_STATE, item.getString("integration.ticket.resolution.state", ServiceOpsIntegration.STATUS_CLOSED)));

                        Bootstrap.configDBService().save(COLLECTION_INTEGRATION_PROFILE, context, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                        {
                            if (result.succeeded())
                            {
                                IntegrationProfileConfigStore.getStore().addItem(result.result()).onComplete(asyncResult ->
                                {
                                    if (asyncResult.succeeded())
                                    {
                                        LOGGER.info(String.format("successfully created integration profile : %s", context.getString(IntegrationProfile.INTEGRATION_PROFILE_NAME)));

                                        profiles.put(urgency.getValue().toString(), result.result());

                                        future.complete();
                                    }
                                    else
                                    {
                                        LOGGER.error(asyncResult.cause());

                                        future.fail(asyncResult.cause());
                                    }
                                });
                            }
                            else
                            {
                                LOGGER.error(result.cause());

                                future.fail(result.cause());
                            }
                        });
                    }

                    Future.join(futures).onComplete(result ->
                    {
                        if (!item.containsKey(Integration.INTEGRATION_CONTEXT))
                        {
                            item.put(Integration.INTEGRATION_CONTEXT, new JsonObject());
                        }

                        var profileContext = item.getJsonObject(Integration.INTEGRATION_CONTEXT);

                        profileContext.put(GlobalConstants.TARGET, item.getString("integration.server.url", EMPTY_VALUE));

                        profileContext.put(TIMEOUT, item.getLong("integration.server.url.timeout.sec", 60L));

                        profileContext.put(USERNAME, item.getString(USERNAME, EMPTY_VALUE));

                        profileContext.put(PASSWORD, item.getString(PASSWORD, EMPTY_VALUE));

                        profileContext.put(APIConstants.CLIENT_ID, item.getString(APIConstants.CLIENT_ID, EMPTY_VALUE));

                        profileContext.put("client.secret", item.getString("client.secret", EMPTY_VALUE));

                        profileContext.put("source", item.getString("source", EMPTY_VALUE));

                        profileContext.put(Notification.EMAIL_NOTIFICATION_RECIPIENTS, item.getJsonArray("integration.failed.notification.email.recipient", new JsonArray()));

                        profileContext.put(Integration.ALERT_REOCCURRENCE_ACTION, "create");

                        profileContext.put(Integration.AUTO_SYNC, NO);

                        profileContext.put(Integration.SYNC_INTERVAL, 2);

                        item.put(Integration.INTEGRATION_ACCESS_TOKEN, item.getString("access_token", EMPTY_VALUE));

                        Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_INTEGRATION,
                                new JsonObject().put(ConfigDBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, item.getLong(ID)),
                                item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
                                {
                                    if (asyncResult.succeeded())
                                    {
                                        IntegrationConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResponse ->
                                        {
                                            if (asyncResponse.succeeded())
                                            {
                                                LOGGER.info("integration context updated successfully : " + item.encode());

                                                promise.complete(profiles);
                                            }
                                            else
                                            {
                                                LOGGER.error(asyncResult.cause());

                                                promise.fail(asyncResult.cause());
                                            }
                                        });
                                    }
                                    else
                                    {
                                        LOGGER.error(asyncResult.cause());

                                        promise.fail(asyncResult.cause());
                                    }
                                });
                    });
                }
                else
                {
                    LOGGER.info("serviceops integration not configured!");

                    promise.complete(profiles);
                }
            }
            else
            {
                LOGGER.warn("no serviceops configuration found!");

                promise.complete(profiles);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception.getCause());
        }

        return promise.future();
    }

    // custom tab patch
    private Future<Void> executeCustomTabPatch()
    {
        var promise = Promise.<Void>promise();

        try
        {
            var items = TemplateConfigStore.getStore().getItems();

            var futures = new ArrayList<Future<Void>>();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                if (item.containsKey("custom.tabs") && !item.getJsonArray("custom.tabs").isEmpty())
                {
                    var iterator = item.getJsonArray("custom.tabs").iterator();

                    while (iterator.hasNext())
                    {
                        var customTab = (JsonObject) iterator.next();

                        var customTemplate = TemplateConfigStore.getStore().getItem(customTab.getLong("tab.id"));

                        if (customTemplate == null) // found template which was deleted but entry left in parent template
                        {
                            var future = Promise.<Void>promise();

                            futures.add(future.future());

                            iterator.remove();  // remove entry of custom tab from the parent template

                            Bootstrap.configDBService.update(ConfigDBConstants.COLLECTION_TEMPLATE,
                                    new JsonObject().put(FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                                    item,
                                    DEFAULT_USER, MOTADATA_SYSTEM, result ->
                                    {
                                        if (result.succeeded())
                                        {
                                            TemplateConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                                            {
                                                if (asyncResult.succeeded())
                                                {
                                                    LOGGER.info(String.format("updated template : %s : item: %s", item.getLong(ID), TemplateConfigStore.getStore().getItem(item.getLong(ID))));

                                                    future.complete();
                                                }
                                                else
                                                {
                                                    LOGGER.warn(asyncResult.cause().getMessage());

                                                    future.fail(asyncResult.cause());
                                                }
                                            });
                                        }
                                        else
                                        {
                                            LOGGER.warn(String.format("failed to update template : %s because of : %s ", item.getLong(ID), result.cause()));

                                            future.fail(result.cause());
                                        }
                                    });
                        }
                    }
                }
            }

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    LOGGER.info("custom tabs updated successfully!");

                    promise.complete();
                }
                else
                {
                    LOGGER.info("Failed to update custom tabs : " + result.cause());

                    promise.fail(result.cause());
                }
            });

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    private Future<Void> executeMailServerConfigurationPatch()
    {
        var promise = Promise.<Void>promise();

        try
        {
            LOGGER.info("executing patch to update mail server configuration...");

            var item = MailServerConfigStore.getStore().getItem();

            var valid = false;

            JsonObject credentialProfile = new JsonObject();

            if (!item.containsKey(MAIL_SERVER_CREDENTIAL_PROFILE))
            {
                valid = true;

                item.put(MAIL_SERVER_CREDENTIAL_PROFILE, DEFAULT_EMAIL_CREDENTIAL_PROFILE);

                credentialProfile.mergeIn(CredentialProfileConfigStore.getStore().getItem(DEFAULT_EMAIL_CREDENTIAL_PROFILE));

                credentialProfile.getJsonObject(CREDENTIAL_PROFILE_CONTEXT).put(USERNAME, item.getString(MAIL_SERVER_USERNAME));

                credentialProfile.getJsonObject(CREDENTIAL_PROFILE_CONTEXT).put(PASSWORD, item.getString(MAIL_SERVER_PASSWORD));

                item.remove(MAIL_SERVER_USERNAME);

                item.remove(MAIL_SERVER_PASSWORD);

            }

            if (valid)
            {

                Bootstrap.configDBService.update(ConfigDBConstants.COLLECTION_CREDENTIAL_PROFILE,
                        new JsonObject().put(FIELD_NAME, ID).put(VALUE, DEFAULT_EMAIL_CREDENTIAL_PROFILE),
                        credentialProfile,
                        DEFAULT_USER, MOTADATA_SYSTEM, response ->
                        {
                            if (response.succeeded())
                            {
                                CredentialProfileConfigStore.getStore().updateItem(DEFAULT_EMAIL_CREDENTIAL_PROFILE).onComplete(asyncResponse ->
                                {
                                    if (asyncResponse.succeeded())
                                    {
                                        LOGGER.info(String.format("updated Credential Profile : %s : item: %s", DEFAULT_EMAIL_CREDENTIAL_PROFILE, CredentialProfileConfigStore.getStore().getItem(DEFAULT_EMAIL_CREDENTIAL_PROFILE)));

                                        Bootstrap.configDBService.update(ConfigDBConstants.COLLECTION_MAIL_SERVER,
                                                new JsonObject().put(FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                                                item,
                                                DEFAULT_USER, MOTADATA_SYSTEM, result ->
                                                {
                                                    if (result.succeeded())
                                                    {
                                                        MailServerConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(asyncResult ->
                                                        {
                                                            if (asyncResult.succeeded())
                                                            {
                                                                LOGGER.info(String.format("updated Mail Server Configuration : %s : item: %s", item.getLong(ID), MailServerConfigStore.getStore().getItem(item.getLong(ID))));

                                                                promise.complete();
                                                            }
                                                            else
                                                            {
                                                                LOGGER.warn(asyncResult.cause().getMessage());

                                                                promise.fail(asyncResult.cause());
                                                            }
                                                        });
                                                    }
                                                    else
                                                    {
                                                        LOGGER.warn(String.format("failed to update Mail Server Configuration : %s because of : %s ", item.getLong(ID), result.cause()));

                                                        promise.fail(result.cause());
                                                    }
                                                });
                                    }
                                    else
                                    {
                                        promise.fail(asyncResponse.cause());
                                    }

                                });
                            }
                            else
                            {
                                promise.fail(response.cause());
                            }

                        });
            }
            else
            {
                promise.complete();
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }
}
