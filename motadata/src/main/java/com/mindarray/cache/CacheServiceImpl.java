/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.cache;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.SNMPTrapProcessor;
import com.mindarray.store.ObjectConfigStore;
import com.mindarray.store.TrapCacheStore;
import com.mindarray.util.CodecUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.*;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.Date;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.ENTITY_ID;
import static com.mindarray.eventbus.EventBusConstants.EVENT_SOURCE;
import static com.mindarray.nms.SNMPTrapProcessor.SNMP_TRAP_OID;
import static com.mindarray.policy.PolicyEngineConstants.POLICY_ID;

/**
 * Implementation of the {@link CacheService} interface that provides methods for retrieving
 * cached data related to metrics, correlation worklogs, and trap acknowledgments.
 * <p>
 * This class reads cached data from files in the cache directory or from cache stores,
 * processes the data as needed, and returns it to the caller. It uses Vert.x's executeBlocking
 * method to perform file I/O operations without blocking the event loop.
 * <p>
 * The cache files are organized by entity ID, policy ID, and other identifiers to allow
 * for efficient retrieval of specific data.
 *
 * @see CacheService
 * @see CacheServiceProvider
 */
public class CacheServiceImpl implements CacheService
{
    /**
     * Logger instance for this class.
     */
    private static final Logger LOGGER = new Logger(CacheServiceImpl.class, MOTADATA_CACHE, "Cache Service");

    /**
     * The Vert.x instance used for executing blocking operations.
     */
    private final Vertx vertx;

    /**
     * Constructs a new CacheServiceImpl instance.
     *
     * @param vertx   The Vert.x instance
     * @param handler The handler to be called when the service is created
     */
    CacheServiceImpl(Vertx vertx, Handler<AsyncResult
            <CacheService>> handler)
    {
        this.vertx = vertx;
        handler.handle(Future.succeededFuture(this));
    }

    /**
     * {@inheritDoc}
     * <p>
     * This implementation executes a blocking operation to build the correlated metric result
     * by reading cached data from files in the cache directory.
     */
    @Override
    public CacheService getCorrelatedMetrics(JsonObject context, Handler<AsyncResult<JsonObject>> handler)
    {
        // Execute the operation in a blocking context to avoid blocking the event loop during file I/O
        vertx.<JsonObject>executeBlocking(future ->
                        // Build the correlated metric result and handle the completion
                        buildCorrelatedMetricResult(context, context.getJsonArray(ENTITIES)).onComplete(result ->
                        {
                            if (result.succeeded() && !result.result().isEmpty())
                            {
                                // If successful and result is not empty, return the result
                                handler.handle(Future.succeededFuture(result.result()));
                            }
                            else
                            {
                                // If failed or result is empty, return an error
                                handler.handle(Future.failedFuture(ErrorMessageConstants.INVALID_DATA_SOURCE));
                            }

                            // Complete the future with the result
                            future.complete(result.result());
                        }), false, // Don't order the operations
                result ->
                {
                    if (result.succeeded())
                    {
                        // If the blocking operation succeeded, return the result
                        handler.handle(Future.succeededFuture(result.result()));
                    }
                    else
                    {
                        // If the blocking operation failed, log the error and return it
                        LOGGER.error(result.cause());
                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });
        return this;
    }

    /**
     * {@inheritDoc}
     * <p>
     * This implementation executes a blocking operation to retrieve correlation worklogs
     * from a file in the cache directory. The file path is constructed using the entity ID,
     * policy ID, and instance from the context.
     */
    @Override
    public CacheService getCorrelationWorklogs(JsonObject context, Handler<AsyncResult<JsonObject>> handler)
    {
        // Execute the operation in a blocking context to avoid blocking the event loop during file I/O
        vertx.<JsonObject>executeBlocking(future ->
                {
                    // Initialize the result object
                    var result = new JsonObject();

                    // Split the instance string to extract the identifier
                    var tokens = context.getString(INSTANCE).split(DASH_SEPARATOR);

                    // Construct the path to the correlation map file
                    var file = new File(GlobalConstants.CURRENT_DIR + PATH_SEPARATOR + CACHE_DIR + PATH_SEPARATOR +
                            context.getLong(ENTITY_ID) + DASH_SEPARATOR +
                            context.getLong(POLICY_ID) + GlobalConstants.DASH_SEPARATOR +
                            tokens[tokens.length - 1] + DASH_SEPARATOR +
                            AIOpsConstants.CORRELATION_MAP);

                    if (file.exists())
                    {
                        // Get the data points from the context
                        var dataPoints = context.getJsonArray(VisualizationConstants.DATA_POINTS);

                        try
                        {
                            // Read and parse the correlation worklog file
                            var correlationWorklog = new JsonObject(
                                    Buffer.buffer(
                                            CodecUtil.toBytes(
                                                    Bootstrap.vertx().fileSystem().readFileBlocking(file.getPath()).getBytes()
                                            )
                                    )
                            ).getJsonObject(RESULT);

                            // Process each data point
                            for (var index = 0; index < dataPoints.size(); index++)
                            {
                                // Get the data point name
                                var dataPoint = dataPoints.getJsonObject(index).getString(VisualizationConstants.DATA_POINT);

                                // Initialize the array for this data point if it doesn't exist
                                result.getMap().computeIfAbsent(dataPoint, value -> new JsonArray());

                                // Add the value for this data point if it exists in the worklog, otherwise add an empty value
                                if (correlationWorklog.containsKey(dataPoint))
                                {
                                    result.getJsonArray(dataPoint).add(correlationWorklog.getValue(CommonUtil.getString(dataPoint)));
                                }
                                else
                                {
                                    result.getJsonArray(dataPoint).add(EMPTY_VALUE);
                                }
                            }
                        }
                        catch (Exception exception)
                        {
                            // If there's an error reading the file, delete it as it may be corrupted
                            Bootstrap.vertx().fileSystem().deleteBlocking(file.getPath());
                        }
                    }

                    // Return the result if it's not empty, otherwise return an error
                    if (!result.isEmpty())
                    {
                        handler.handle(Future.succeededFuture(result));
                    }
                    else
                    {
                        handler.handle(Future.failedFuture(ErrorMessageConstants.INVALID_DATA_SOURCE));
                    }

                    // Complete the future with the result
                    future.complete(result);
                }, false, // Don't order the operations
                result ->
                {
                    if (result.succeeded())
                    {
                        // If the blocking operation succeeded, return the result
                        handler.handle(Future.succeededFuture(result.result()));
                    }
                    else
                    {
                        // If the blocking operation failed, log the error and return it
                        LOGGER.error(result.cause());
                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });
        return this;
    }

    /**
     * Builds the result for correlated metrics by reading from cache files.
     * <p>
     * This method reads cached metric data from files in the cache directory for each entity
     * in the items array. It then processes the data to extract the values for the specified
     * data points and returns them in a structured format.
     *
     * @param context The context containing the data points and mapper group
     * @param items   The array of entity IDs for which to retrieve metrics
     * @return A future that will be completed with the result
     */
    private Future<JsonObject> buildCorrelatedMetricResult(JsonObject context, JsonArray items)
    {
        // Create a promise to return the result
        var promise = Promise.<JsonObject>promise();

        // Initialize the result object
        var result = new JsonObject();

        // Query the event column mapper to get information about data point types
        Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_EVENT_COLUMN_MAPPER_QUERY, EMPTY_VALUE, asyncResult ->
        {
            if (asyncResult.succeeded())
            {
                try
                {
                    // Process each entity in the items array
                    for (var index = 0; index < items.size(); index++)
                    {
                        // Construct the path to the cache file for this entity
                        var file = new File(GlobalConstants.CURRENT_DIR + PATH_SEPARATOR + CACHE_DIR + PATH_SEPARATOR +
                                ObjectConfigStore.getStore().getObjectId(items.getLong(index)) + DASH_SEPARATOR +
                                context.getString(DatastoreConstants.MAPPER_GROUP) + GlobalConstants.DASH_SEPARATOR +
                                new SimpleDateFormat(DateTimeUtil.CORRELATED_METRIC_CACHE_FILE_TIMESTAMP_FORMAT).format(new Date()));

                        if (file.exists())
                        {
                            // Get the data points from the context
                            var dataPoints = context.getJsonArray(VisualizationConstants.DATA_POINTS);

                            try
                            {
                                // Read and parse the cache file
                                var rows = new JsonObject(
                                        Buffer.buffer(
                                                CodecUtil.toBytes(
                                                        Bootstrap.vertx().fileSystem().readFileBlocking(file.getPath()).getBytes()
                                                )
                                        )
                                ).getJsonArray(RESULT);

                                // Process each row in the cache file
                                for (var j = 0; j < rows.size(); j++)
                                {
                                    var row = rows.getJsonObject(j);

                                    // Process each data point
                                    for (var i = 0; i < dataPoints.size(); i++)
                                    {
                                        // Get the data point name
                                        var dataPoint = dataPoints.getJsonObject(i).getString(VisualizationConstants.DATA_POINT);

                                        // Initialize the array for this data point if it doesn't exist
                                        result.getMap().computeIfAbsent(dataPoint, value -> new JsonArray());

                                        // Add the value for this data point if it exists in the row
                                        if (row.getValue(CommonUtil.getString(dataPoint)) != null)
                                        {
                                            result.getJsonArray(dataPoint).add(row.getValue(CommonUtil.getString(dataPoint)));
                                        }
                                        else
                                        {
                                            // If the value doesn't exist, determine the appropriate default value based on the data type
                                            if (asyncResult.result().body().getJsonObject(DatastoreConstants.EVENT_COLUMNS) != null &&
                                                    asyncResult.result().body().getJsonObject(DatastoreConstants.EVENT_COLUMNS).getJsonObject(CommonUtil.getString(dataPoint)) != null)
                                            {
                                                // Get the data categories for this data point
                                                var categories = asyncResult.result().body().getJsonObject(DatastoreConstants.EVENT_COLUMNS)
                                                        .getJsonObject(CommonUtil.getString(dataPoint))
                                                        .getJsonArray(DatastoreConstants.MAPPER_DATA_CATEGORIES);

                                                // Add an empty string for string data points, or a dummy numeric value for numeric data points
                                                result.getJsonArray(dataPoint).add(categories.contains(DatastoreConstants.DataCategory.STRING.ordinal()) ?
                                                        EMPTY_VALUE : DUMMY_NUMERIC_VALUE);
                                            }
                                            else
                                            {
                                                // If we don't have type information, default to an empty string
                                                result.getJsonArray(dataPoint).add(EMPTY_VALUE);
                                            }
                                        }
                                    }
                                }
                            }
                            catch (Exception exception)
                            {
                                // If there's an error reading the file, delete it as it may be corrupted
                                Bootstrap.vertx().fileSystem().deleteBlocking(file.getPath());
                            }
                        }
                    }
                }
                catch (Exception exception)
                {
                    // Log any exceptions that occur during processing
                    LOGGER.error(exception);
                }
                finally
                {
                    // Complete the promise with the result, even if there were errors
                    promise.complete(result);
                }
            }
            else
            {
                // If the event column mapper query failed, fail the promise
                promise.fail(asyncResult.cause());
            }
        });

        return promise.future();
    }

    /**
     * {@inheritDoc}
     * <p>
     * This implementation executes a blocking operation to retrieve trap acknowledgments
     * from the TrapCacheStore. It formats the data into arrays of event sources, trap OIDs,
     * and acknowledgment statuses.
     */
    @Override
    public CacheService getTrapAcks(Handler<AsyncResult<JsonObject>> handler)
    {
        // Execute the operation in a blocking context
        vertx.<JsonObject>executeBlocking(future ->
                {
                    // Initialize the result object
                    var result = new JsonObject();

                    // Get all items from the TrapCacheStore
                    var items = TrapCacheStore.getStore().getItems();

                    // Initialize arrays for event sources, trap OIDs, and acknowledgment statuses
                    result.put(SNMP_TRAP_OID, new JsonArray())
                            .put(EVENT_SOURCE, new JsonArray())
                            .put(SNMPTrapProcessor.SNMP_TRAP_ACK_STATUS, new JsonArray());

                    // Process each item in the store
                    items.forEach((key, value) ->
                    {
                        // Split the key to extract the event source and trap OID
                        var tokens = key.split(DASH_SEPARATOR);

                        // Add the event source to the result
                        result.getJsonArray(EVENT_SOURCE).add(tokens[0]);

                        // Add the trap OID to the result
                        result.getJsonArray(SNMP_TRAP_OID).add(tokens[1]);

                        // Add the acknowledgment status to the result
                        result.getJsonArray(SNMPTrapProcessor.SNMP_TRAP_ACK_STATUS).add(value);
                    });

                    // Complete the future with the result
                    future.complete(result);
                }, false, // Don't order the operations
                result ->
                {
                    if (result.succeeded())
                    {
                        // If the blocking operation succeeded, return the result
                        handler.handle(Future.succeededFuture(result.result()));
                    }
                    else
                    {
                        // If the blocking operation failed, log the error and return it
                        LOGGER.error(result.cause());
                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });
        return this;
    }
}
