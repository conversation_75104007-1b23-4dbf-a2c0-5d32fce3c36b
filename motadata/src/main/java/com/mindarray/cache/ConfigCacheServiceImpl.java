/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.cache;

import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.api.*;
import com.mindarray.config.ConfigConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.AsyncResult;
import io.vertx.core.Future;
import io.vertx.core.Handler;
import io.vertx.core.Vertx;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.policy.PolicyEngineConstants.INSTANCE_TYPE;

/**
 * Implementation of the {@link ConfigCacheService} interface that provides methods for retrieving
 * cached data related to device configuration summaries.
 * <p>
 * This class reads cached data from configuration stores, processes the data as needed, and returns it
 * to the caller. It uses Vert.x's executeBlocking method to perform operations without blocking
 * the event loop.
 * <p>
 * The configuration data is organized by device, template, and other identifiers to allow
 * for efficient retrieval and visualization of specific configuration information.
 *
 * @see ConfigCacheService
 * @see CacheServiceProvider
 */
public class ConfigCacheServiceImpl implements ConfigCacheService
{
    /**
     * Logger instance for this class.
     */
    private static final Logger LOGGER = new Logger(ConfigCacheServiceImpl.class, MOTADATA_CACHE, "Config Cache Service");

    /**
     * The Vert.x instance used for executing blocking operations.
     */
    private final Vertx vertx;

    /**
     * Constructs a new ConfigCacheServiceImpl instance.
     *
     * @param vertx   The Vert.x instance
     * @param handler The handler to be called when the service is created
     */
    ConfigCacheServiceImpl(Vertx vertx, Handler<AsyncResult
            <ConfigCacheService>> handler)
    {
        this.vertx = vertx;

        handler.handle(Future.succeededFuture(this));
    }

    /**
     * {@inheritDoc}
     * <p>
     * This implementation executes a blocking operation to build the device configuration summary
     * by retrieving data from the ConfigurationConfigStore. It processes the data based on the
     * context parameters and returns a summary of device configurations.
     */
    @Override
    public ConfigCacheService getDeviceConfigSummary(JsonObject context, Handler<AsyncResult<JsonObject>> handler)
    {
        vertx.<JsonObject>executeBlocking(future ->
                {
                    var result = buildConfigSummary(context, context.getJsonArray(ENTITIES));

                    if (!result.isEmpty())
                    {
                        handler.handle(Future.succeededFuture(result));
                    }
                    else
                    {
                        handler.handle(Future.failedFuture(ErrorMessageConstants.INVALID_DATA_SOURCE));
                    }

                    future.complete(result);
                }, false,
                result ->
                {
                    if (result.succeeded())
                    {
                        handler.handle(Future.succeededFuture(result.result()));
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        handler.handle(Future.failedFuture(result.cause()));
                    }
                });
        return this;
    }

    /**
     * Builds the configuration summary based on the provided context and entity IDs.
     * <p>
     * This method retrieves configuration items from the ConfigurationConfigStore, filters them
     * based on the context, and processes them to create a summary of device configurations.
     * It supports different types of summaries based on the context parameters, including
     * startup and baseline conflict gauges, device overviews, device summaries, and firmware
     * upgrade overviews.
     *
     * @param context The context containing the parameters for the query
     * @param ids     The array of entity IDs for which to retrieve configuration summaries
     * @return A JsonObject containing the configuration summary
     */
    private JsonObject buildConfigSummary(JsonObject context, JsonArray ids)
    {
        var result = new JsonObject();

        if (ids == null || ids.isEmpty())
        {
            return result;
        }

        var items = filterItems(ConfigurationConfigStore.getStore().getItemsByStatus(ids, new JsonArray().add(NMSConstants.Category.NETWORK.getName()), GlobalConstants.YES), context);

        if (items.isEmpty())
        {
            return result;
        }

        //startup and baseline conflict gauge widget
        if (context.getString(INSTANCE_TYPE).contains("startup") || context.getString(INSTANCE_TYPE).contains("baseline") || context.getString(INSTANCE_TYPE).contains("backup.status"))
        {
            buildConfigStats(items, result, context.getString(INSTANCE_TYPE).contains("startup") ?
                    Configuration.CONFIG_BACKUP_FILE_RUNNING_WITH_STARTUP_CONFLICT_STATUS : context.getString(INSTANCE_TYPE).contains("baseline") ? Configuration.CONFIG_BACKUP_FILE_RUNNING_WITH_BASELINE_CONFLICT_STATUS : Configuration.CONFIG_LAST_BACKUP_STATUS, false);

            context.getJsonArray(STATUS).stream().filter(status -> !result.containsKey(CommonUtil.getString(status))).forEach(status -> result.put(CommonUtil.getString(status), 0));
        }
        else if (context.getString(VisualizationConstants.VISUALIZATION_NAME) != null && context.getString(VisualizationConstants.VISUALIZATION_NAME).equals("Device Overview"))
        {
            buildConfigStats(items, result, null, true);
        }
        else if (context.getString(VisualizationConstants.VISUALIZATION_NAME) != null && context.getString(VisualizationConstants.VISUALIZATION_NAME).equals("Device Summary"))
        {
            buildDeviceSummary(items, context, result);
        }
        else if (context.getString(VisualizationConstants.VISUALIZATION_NAME) != null && context.getString(VisualizationConstants.VISUALIZATION_NAME).equals("Firmware Upgrade Overview"))
        {
            buildConfigStats(items, result, Configuration.CONFIG_LAST_UPGRADE_STATUS, false);
        }
        else
        {
            buildRecentBackupSummary(items, result, context);
        }

        return result;
    }

    /**
     * Builds a summary of recent backups for the qualified items.
     * <p>
     * This method processes the qualified configuration items and adds their data to the result
     * based on the data points specified in the context. It retrieves additional information
     * from the ObjectConfigStore and ConfigTemplateConfigStore as needed.
     *
     * @param qualifiedItems The array of qualified configuration items
     * @param result         The result object to which to add the backup summary
     * @param context        The context containing the parameters for the query
     */
    private void buildRecentBackupSummary(JsonArray qualifiedItems, JsonObject result, JsonObject context)
    {
        try
        {
            var dataPoints = context.getJsonArray(VisualizationConstants.DATA_POINTS);

            for (var index = 0; index < qualifiedItems.size(); index++)
            {
                var object = ObjectConfigStore.getStore().getItemByObjectId(qualifiedItems.getJsonObject(index).getInteger(Configuration.CONFIG_OBJECT));

                ConfigConstants.removeGarbageFields(object);

                var item = qualifiedItems.getJsonObject(index).mergeIn(object);

                if (item.containsKey(Configuration.CONFIG_TEMPLATE))
                {
                    var template = ConfigTemplateConfigStore.getStore().getItem(item.getLong(Configuration.CONFIG_TEMPLATE));

                    template.remove(ID);

                    item.mergeIn(template);
                }

                setObjectColumns(item);

                for (var start = 0; start < dataPoints.size(); start++)
                {
                    var dataPoint = dataPoints.getJsonObject(start).getString(VisualizationConstants.DATA_POINT);

                    result.getMap().computeIfAbsent(dataPoint, value -> new JsonArray());

                    if (item.containsKey(dataPoint))
                    {
                        result.getJsonArray(dataPoint).add(item.getValue(CommonUtil.getString(dataPoint)));
                    }
                    else
                    {
                        result.getJsonArray(dataPoint).add(EMPTY_VALUE);
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Builds a summary of devices based on the qualified items.
     * <p>
     * This method processes the qualified configuration items and groups them by device type,
     * vendor, and OS type. It then adds the grouped data to the result based on the data points
     * specified in the context.
     *
     * @param qualifiedItems The array of qualified configuration items
     * @param context        The context containing the parameters for the query
     * @param result         The result object to which to add the device summary
     */
    private void buildDeviceSummary(JsonArray qualifiedItems, JsonObject context, JsonObject result)
    {
        try
        {
            var configDeviceByKey = new JsonObject();

            for (var index = 0; index < qualifiedItems.size(); index++)
            {
                var object = ObjectConfigStore.getStore().getItemByObjectId(qualifiedItems.getJsonObject(index).getInteger(Configuration.CONFIG_OBJECT));

                ConfigConstants.removeGarbageFields(object);

                var item = qualifiedItems.getJsonObject(index).mergeIn(object);

                if (item.containsKey(Configuration.CONFIG_TEMPLATE))
                {
                    var template = ConfigTemplateConfigStore.getStore().getItem(item.getLong(Configuration.CONFIG_TEMPLATE));

                    template.remove(ID);

                    item.mergeIn(template);
                }

                setObjectColumns(item);

                var key = item.getString(AIOpsObject.OBJECT_TYPE) + SEPARATOR +
                        item.getString(AIOpsObject.OBJECT_VENDOR) + SEPARATOR +
                        (item.containsKey(ConfigTemplate.CONFIG_TEMPLATE_OS_TYPE) ? item.getString(ConfigTemplate.CONFIG_TEMPLATE_OS_TYPE) : " ");

                configDeviceByKey.getMap().put(key, configDeviceByKey.getInteger(key, 0) + 1);
            }

            if (!configDeviceByKey.isEmpty())
            {
                var dataPoints = context.getJsonArray(VisualizationConstants.DATA_POINTS);

                var iterator = configDeviceByKey.stream().iterator();

                while (iterator.hasNext())
                {
                    var entry = iterator.next();

                    var values = entry.getKey().split(SEPARATOR_WITH_ESCAPE);

                    for (var index = 0; index < dataPoints.size(); index++)
                    {
                        var dataPoint = dataPoints.getJsonObject(index).getString(VisualizationConstants.DATA_POINT);

                        result.getMap().computeIfAbsent(dataPoint, v -> new JsonArray());

                        var columnValue = dataPoint.equalsIgnoreCase("devices") ? CommonUtil.getString(entry.getValue()) : index < values.length ? values[index] : EMPTY_VALUE;

                        if (CommonUtil.isNotNullOrEmpty(columnValue))
                        {
                            result.getJsonArray(dataPoint).add(columnValue);
                        }
                        else
                        {
                            result.getJsonArray(dataPoint).add(EMPTY_VALUE);
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Sets all the object-specific details in the configuration item.
     * <p>
     * This method adds object-specific information to the configuration item, including tags,
     * groups, processors, and status. It formats the data as needed for display in the visualization.
     * <p>
     * In the future, need to add columns here which are required to be exported in NCM Report.
     *
     * @param item The configuration item to which to add the object-specific details
     */
    private void setObjectColumns(JsonObject item)
    {
        var builder = new StringBuilder();

        // Setting Object Tags
        if (item.containsKey(AIOpsObject.OBJECT_TAGS))
        {

            var tags = item.getJsonArray(AIOpsObject.OBJECT_TAGS);

            if (tags != null && !tags.isEmpty())
            {
                for (var tag : tags)
                {
                    if (!builder.isEmpty())
                    {
                        builder.append(COMMA_SEPARATOR);
                    }

                    builder.append(TagConfigStore.getStore().getTag(CommonUtil.getLong(tag)));
                }
            }

            item.put(AIOpsObject.OBJECT_TAGS, builder.toString());

            builder.setLength(0);
        }

        // Setting Object Groups
        if (item.containsKey(AIOpsObject.OBJECT_GROUPS))
        {
            var groups = item.getJsonArray(AIOpsObject.OBJECT_GROUPS);

            if (groups != null && !groups.isEmpty())
            {
                for (var group : groups)
                {
                    if (!builder.isEmpty())
                    {
                        builder.append(COMMA_SEPARATOR);
                    }

                    builder.append(GroupConfigStore.getStore().getItem(CommonUtil.getLong(group), false).getString(Group.FIELD_GROUP_NAME));
                }
            }

            item.put(AIOpsObject.OBJECT_GROUPS, builder.toString());

            builder.setLength(0);
        }

        // Setting Object Processors
        if (item.containsKey(AIOpsObject.OBJECT_EVENT_PROCESSORS))
        {
            var processors = item.getJsonArray(AIOpsObject.OBJECT_EVENT_PROCESSORS);

            builder.setLength(0);

            if (processors != null && !processors.isEmpty())
            {
                for (var objectEventProcessor : processors)
                {
                    if (!builder.isEmpty())
                    {
                        builder.append(COMMA_SEPARATOR);
                    }

                    builder.append(RemoteEventProcessorConfigStore.getStore().getItem(CommonUtil.getLong(objectEventProcessor)).getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_IP));
                }
            }

            item.put(AIOpsObject.OBJECT_EVENT_PROCESSORS, builder.toString());

            builder.setLength(0);
        }

        // Object Status
        item.put(STATUS, ObjectStatusCacheStore.getStore().existItem(item.getLong(ConfigConstants.CONFIG_OBJECT_ID)) ? ObjectStatusCacheStore.getStore().getItem(item.getLong(ConfigConstants.CONFIG_OBJECT_ID)) : STATUS_UNKNOWN);
    }

    /**
     * Builds configuration statistics based on the qualified items.
     * <p>
     * This method processes the qualified configuration items and counts them based on the
     * specified field or object type. It adds the counts to the result object.
     *
     * @param qualifiedItems The array of qualified configuration items
     * @param result         The result object to which to add the configuration statistics
     * @param field          The field to use for grouping the items, or null for widget mode
     * @param widget         Whether to use widget mode, which groups by object type
     */
    private void buildConfigStats(JsonArray qualifiedItems, JsonObject result, String field, boolean widget)
    {
        try
        {
            for (var index = 0; index < qualifiedItems.size(); index++)
            {
                var column = widget ? ObjectConfigStore.getStore().getItemByObjectId(qualifiedItems.getJsonObject(index).getInteger(Configuration.CONFIG_OBJECT)).getString(AIOpsObject.OBJECT_TYPE) : qualifiedItems.getJsonObject(index).getString(field);

                if (CommonUtil.isNotNullOrEmpty(column))
                {
                    result.put(column, result.getInteger(column, 0) + 1);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Filters configuration items based on the context.
     * <p>
     * This method filters the configuration items to include only those that have management
     * status ON and match any additional filters specified in the context. Config Overview
     * will always show items which have manage status ON.
     *
     * @param items   The array of configuration items to filter
     * @param context The context containing the filter parameters
     * @return An array of qualified configuration items
     */
    private JsonArray filterItems(JsonArray items, JsonObject context)
    {
        var qualifiedItems = new JsonArray();

        try
        {
            if (items != null && !items.isEmpty())
            {
                var dataFilter = context.containsKey(GlobalConstants.FILTERS) ? context.getJsonObject(GlobalConstants.FILTERS).getJsonObject(GlobalConstants.DATA_FILTER) : new JsonObject();

                var filterKeys = dataFilter != null && !dataFilter.isEmpty() ? dataFilter.getMap().keySet() : null;

                for (var index = 0; index < items.size(); index++)
                {
                    var item = items.getJsonObject(index);

                    if (item != null && item.getString(Configuration.CONFIG_MANAGEMENT_STATUS).equals(YES) && item.containsKey(Configuration.CONFIG_LAST_BACKUP_STATUS))
                    {
                        if (filterKeys != null)
                        {
                            for (var key : filterKeys)
                            {
                                if (item.containsKey(key) && item.getString(key).equals(CommonUtil.getString(dataFilter.getString(key))))
                                {
                                    qualifiedItems.add(item);
                                }
                            }
                        }
                        else
                        {
                            qualifiedItems.add(item);
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return qualifiedItems;
    }
}
