/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 */
package com.mindarray.log;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.LogCollectorConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonObject;

import java.util.HashMap;
import java.util.Map;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.LogCollector.*;
import static com.mindarray.eventbus.EventBusConstants.*;

/**
 * A verticle responsible for collecting logs from various sources at configured intervals.
 * <p>
 * The LogCollector manages the execution of log collection tasks based on configured intervals.
 * It maintains a list of log collectors and their timer contexts, and periodically triggers
 * log collection based on the configured intervals for each collector.
 * <p>
 * Key features:
 * <ul>
 *   <li>Manages log collectors and their timer contexts</li>
 *   <li>Periodically triggers log collection based on configured intervals</li>
 *   <li>Handles add, update, and delete notifications for log collectors</li>
 *   <li>Tracks the health status of log collectors</li>
 * </ul>
 * <p>
 * The LogCollector communicates with other components via the Vert.x event bus.
 */
public class LogCollector extends AbstractVerticle
{
    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(LogCollector.class, MOTADATA_LOG, "Log Collector");

    /**
     * Interval (in seconds) for checking log collection timers
     */
    private static final int INTERVAL_SECONDS = MotadataConfigUtil.getLogCollectionTimerSeconds();

    /**
     * Delivery options for event bus communication with timeout set to 400 seconds
     */
    private static final DeliveryOptions DELIVERY_OPTIONS = new DeliveryOptions().setSendTimeout(400000L);

    /**
     * Map of log collectors indexed by their IDs
     */
    private final Map<Long, JsonObject> logCollectors = new HashMap<>();

    /**
     * Map of log collector timer contexts indexed by their IDs
     */
    private final Map<Long, Integer> logCollectorTimerContexts = new HashMap<>();

    /**
     * Initializes and starts the log collector.
     * <p>
     * This method performs the following tasks:
     * <ol>
     *   <li>Loads existing log collectors from the configuration store</li>
     *   <li>Sets up event bus consumers for change notifications (add, update, delete)</li>
     *   <li>Sets up a periodic timer to check and trigger log collection based on configured intervals</li>
     * </ol>
     * <p>
     * The method handles notifications for adding, updating, and deleting log collectors,
     * and manages their timer contexts accordingly.
     *
     * @param promise A promise that will be completed when initialization is done or failed if an error occurs
     * @throws Exception If an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            var items = LogCollectorConfigStore.getStore().getItems();

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                if (item.getString(LOG_COLLECTOR_STATE).equalsIgnoreCase(YES))
                {
                    logCollectors.put(item.getLong(ID), item);

                    logCollectorTimerContexts.put(item.getLong(ID), item.getInteger(LOG_COLLECTOR_INTERVAL));
                }
            }

            vertx.eventBus().<JsonObject>localConsumer(EVENT_CHANGE_NOTIFICATION, message ->
            {
                var event = message.body();

                var id = event.getLong(ID);

                switch (ChangeNotificationType.valueOf(event.getString(CHANGE_NOTIFICATION_TYPE)))
                {
                    case ADD_LOG_COLLECTOR ->
                    {
                        var item = LogCollectorConfigStore.getStore().getItem(id);

                        logCollectors.put(id, item);

                        logCollectorTimerContexts.put(id, item.getInteger(LOG_COLLECTOR_INTERVAL));
                    }

                    case UPDATE_LOG_COLLECTOR ->
                    {
                        var item = LogCollectorConfigStore.getStore().getItem(id);

                        if (item.getString(LOG_COLLECTOR_STATE).equalsIgnoreCase(NO))
                        {
                            logCollectors.remove(id);

                            logCollectorTimerContexts.remove(id);
                        }
                        else
                        {
                            logCollectors.put(id, item);

                            logCollectorTimerContexts.put(id, item.getInteger(LOG_COLLECTOR_INTERVAL));
                        }
                    }

                    case DELETE_LOG_COLLECTOR ->
                    {
                        logCollectors.remove(id);

                        logCollectorTimerContexts.remove(id);
                    }
                    default ->
                    {
                        // do nothing
                    }
                }
            }).exceptionHandler(LOGGER::error);

            vertx.setPeriodic(INTERVAL_SECONDS * 1000L, handler ->
            {

                for (var entry : logCollectorTimerContexts.entrySet())
                {
                    var profile = logCollectors.get(entry.getKey());

                    var interval = entry.getValue() - INTERVAL_SECONDS;

                    if (interval <= 0)
                    {
                        entry.setValue(profile.getInteger(LOG_COLLECTOR_INTERVAL));

                        send(entry.getKey());
                    }
                    else
                    {
                        entry.setValue(interval);
                    }
                }
            });

            if (CommonUtil.traceEnabled())
            {
                LOGGER.trace(String.format("For Bootstrap type: %s", Bootstrap.bootstrapType()));
            }

            LOGGER.debug(config().getString(EVENT_TYPE) + " started successfully!!!");

            promise.complete();
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }
    }

    /**
     * Sends a log collection request for a specific log collector.
     * <p>
     * This method retrieves the log collector configuration from the store and sends a request
     * to execute the associated runbook via the event bus. It then processes the response and
     * updates the status of the log collector based on the result.
     * <p>
     * If the log collection is successful, the log collector's status is set to "healthy".
     * If it fails, the status is set to the error message from the response.
     * If there's an internal error or exception, appropriate error handling is performed.
     *
     * @param id The ID of the log collector to send the request for
     */
    private void send(long id)
    {
        var item = LogCollectorConfigStore.getStore().getItem(id);

        Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_RUNBOOK, new JsonObject().put(ID, item.getLong(LOG_COLLECTOR_RUNBOOK)).put(LOG_COLLECTOR_ID, item.getLong(ID)).put(EventBusConstants.EVENT_REPLY, YES), DELIVERY_OPTIONS, reply ->
        {
            try
            {
                if (reply.succeeded() && reply.result().body() != null)
                {
                    var result = reply.result().body();

                    if (result != null && result.containsKey(EventBusConstants.EVENT_REPLY_CONTEXTS) && !result.getJsonArray(EventBusConstants.EVENT_REPLY_CONTEXTS).isEmpty())
                    {
                        var context = result.getJsonArray(EventBusConstants.EVENT_REPLY_CONTEXTS).getJsonObject(0);

                        if (context != null && CommonUtil.isNotNullOrEmpty(context.getString(STATUS)))
                        {
                            if (context.getString(STATUS).equalsIgnoreCase(STATUS_SUCCEED))
                            {
                                LogCollectorConfigStore.getStore().setStatus(context.getLong(LOG_COLLECTOR_ID), LOG_COLLECTOR_STATE_HEALTHY);
                            }
                            else
                            {
                                LogCollectorConfigStore.getStore().setStatus(item.getLong(ID), context.getString(MESSAGE));
                            }
                        }
                        else
                        {
                            LogCollectorConfigStore.getStore().setStatus(item.getLong(ID), ErrorMessageConstants.INTERNAL_ERROR);
                        }
                    }
                }
                else
                {
                    LOGGER.warn(reply.cause());
                }
            }

            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });
    }
}
