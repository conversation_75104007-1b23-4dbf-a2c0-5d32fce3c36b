/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.flow;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.datastore.DatastoreConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.eventbus.EventEngine;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonObject;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static com.mindarray.GlobalConstants.SEPARATOR_WITH_ESCAPE;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.flow.FlowEngineConstants.*;

/**
 * Calculates statistical information from flow data for analysis and reporting.
 * <p>
 * The FlowStatCalculator is responsible for processing flow data to generate statistical
 * information that provides insights into network behavior and performance. It aggregates
 * flow data over time periods and calculates metrics such as traffic volume, flow rates,
 * and other statistical measures.
 * <p>
 * Key responsibilities of this class include:
 * <ul>
 *   <li>Aggregating flow data by source to calculate statistics</li>
 *   <li>Computing flow counts and volume metrics</li>
 *   <li>Calculating rates such as flows per second and bytes per second</li>
 *   <li>Periodically flushing calculated statistics to storage</li>
 *   <li>Formatting statistical data for analysis and visualization</li>
 * </ul>
 * <p>
 * The FlowStatCalculator works with other components in the flow package:
 * <ul>
 *   <li>It receives processed flow data from the FlowProcessor</li>
 *   <li>It publishes calculated statistics to the datastore for storage and analysis</li>
 * </ul>
 * <p>
 * This class is deployed as a Vert.x verticle and integrates with the event bus system
 * for communication with other components. It uses a periodic timer to calculate and
 * flush statistics at regular intervals.
 */
public class FlowStatCalculator extends AbstractVerticle
{
    /**
     * Logger for this class
     */
    private static final Logger LOGGER = new Logger(FlowStatCalculator.class, GlobalConstants.MOTADATA_FLOW, "Flow Stat Calculator");
    private static final int FLOW_STAT_FLUSH_TIMER_SECONDS = MotadataConfigUtil.getFlowStatFlushTimerSeconds();
    private final Map<String, int[]> stats = new HashMap<>();
    private final JsonObject event = new JsonObject();
    private final StringBuilder builder = new StringBuilder(0);
    private EventEngine eventEngine;
    private Set<String> mappers;


    /**
     * Initializes the flow statistics calculator and sets up event handlers.
     * <p>
     * This method performs the following initialization tasks:
     * <ul>
     *   <li>Checks if the bootstrap type is appropriate for flow statistics calculation</li>
     *   <li>Initializes data structures for storing flow statistics</li>
     *   <li>Sets up the event engine to receive and process flow events</li>
     *   <li>Configures a periodic timer to calculate and flush statistics at regular intervals</li>
     * </ul>
     * <p>
     * The method sets up an event handler that aggregates flow data by source,
     * counting the number of flows and accumulating the volume of bytes. These
     * aggregated statistics are then periodically calculated and flushed to storage
     * by the timer.
     * <p>
     * The flow statistics calculator only runs in the APP bootstrap type, as it
     * requires access to the full flow data for statistical analysis.
     *
     * @param promise the promise to complete when initialization is finished
     * @throws Exception if an error occurs during initialization
     */
    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        try
        {
            if (Bootstrap.bootstrapType() == GlobalConstants.BootstrapType.APP)
            {
                mappers = new HashSet<>();

                eventEngine = new EventEngine().setLogger(LOGGER)
                        .setEventType(config().getString(EventBusConstants.EVENT_TYPE)).setPersistEventOffset(true)
                        .setEventHandler(event ->
                        {
                            try
                            {
                                stats.computeIfAbsent(event.getString(EVENT_SOURCE), value -> new int[2]);

                                var values = stats.get(event.getString(EVENT_SOURCE));

                                values[0]++; //count

                                values[1] += event.getInteger(EVENT_VOLUME_BYTES); //volume bytes
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }
                        }).start(vertx, promise);

                vertx.setPeriodic(FLOW_STAT_FLUSH_TIMER_SECONDS * 1000L, timer -> calculate());

                promise.future().onComplete(result -> LOGGER.debug(config().getString(EventBusConstants.EVENT_TYPE) + " started successfully!!!"));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }
    }

    /**
     * Calculates flow statistics and writes them to the datastore.
     * <p>
     * This method is called periodically by a timer to calculate flow statistics
     * based on the accumulated flow data. For each source in the stats map, it:
     * <ul>
     *   <li>Retrieves the accumulated flow count and volume bytes</li>
     *   <li>Extracts the source identifier from the key</li>
     *   <li>Calculates derived metrics such as flows per second and bytes per second</li>
     *   <li>Creates a JSON event with the calculated statistics</li>
     *   <li>Writes the event to the datastore for storage and analysis</li>
     *   <li>Resets the counters for the next calculation period</li>
     * </ul>
     * <p>
     * The method only writes statistics to the datastore if there are actual flows
     * to report (count > 0), avoiding unnecessary storage of empty statistics.
     * <p>
     * After writing the statistics, the counters are reset to zero to prepare for
     * the next calculation period, ensuring that statistics accurately reflect
     * the flow activity during each period.
     */
    private void calculate()
    {
        stats.keySet().forEach(key ->
        {
            var values = stats.get(key);

            var tokens = key.split(SEPARATOR_WITH_ESCAPE);

            if (values[0] > 0) //count
            {
                DatastoreConstants.write(this.event.put(EVENT_SOURCE, tokens[0])
                        .put(FLOW_VOLUME_BYTES, values[1])
                        .put(FLOWS_PER_SEC, Math.round(CommonUtil.getDouble(values[0] / FLOW_STAT_FLUSH_TIMER_SECONDS)))
                        .put(FLOW_VOLUME_BYTES_PER_SEC, Math.round(CommonUtil.getDouble(values[1] / FLOW_STAT_FLUSH_TIMER_SECONDS)))
                        .put(GlobalConstants.PLUGIN_ID, DatastoreConstants.PluginId.FLOW_EVENT_STAT.getName())
                        .put(EVENT_TIMESTAMP, DateTimeUtil.currentSeconds())
                        .put(DatastoreConstants.DATASTORE_TYPE, DatastoreConstants.DatastoreType.FLOW.ordinal()), "flow", mappers, builder);
            }

            values[0] = 0; //reset count

            values[1] = 0; // reset volume bytes

            this.event.clear();
        });
    }


    /**
     * Stops the flow statistics calculator and releases resources.
     * <p>
     * This method is called when the verticle is undeployed. It stops the event engine
     * and releases any resources used by the flow statistics calculator.
     * <p>
     * The method ensures a clean shutdown of the flow statistics calculator by properly
     * stopping the event engine, which handles the graceful termination of event processing.
     * This prevents resource leaks and ensures that any pending statistics are properly
     * flushed before shutdown.
     *
     * @param promise the promise to complete when the flow statistics calculator has stopped
     */
    @Override
    public void stop(Promise<Void> promise)
    {
        eventEngine.stop(vertx, promise);
    }
}
