/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
	Change Logs:
	Date			Author			    Notes
	24-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
*/

package com.mindarray.db;

import com.mindarray.GlobalConstants;
import com.mindarray.api.AIOpsObject;
import com.mindarray.api.Metric;
import com.mindarray.api.User;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.LicenseUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.AbstractVerticle;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.eventbus.EventBusConstants.*;

public class ConfigDBChangeEventProcessor extends AbstractVerticle
{
    private static final Logger LOGGER = new Logger(ConfigDBChangeEventProcessor.class, GlobalConstants.MOTADATA_DB, "Config DB Change Event Processor");

    private final JsonArray configChanges = new JsonArray();

    private final Map<String, Boolean> statuesBySessionId = new HashMap<>();

    private final Map<String, Long> ticksBySessionId = new HashMap<>();

    private final JsonArray OBJECT_ITEMS = ObjectManagerCacheStore.getStore().getItems();

    private boolean timerHandler = false;

    @Override
    public void start(Promise<Void> promise) throws Exception
    {
        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CONFIG_CHANGE, message ->
        {
            if (!timerHandler)
            {
                timerHandler = true;

                vertx.setTimer(3000, timer ->
                {
                    vertx.cancelTimer(timer);

                    try
                    {
                        var objects = ObjectConfigStore.getStore().getFilteredItems();

                        for (var index = 0; index < objects.size(); index++)
                        {
                            var object = objects.getJsonObject(index);

                            if (object.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVER.getName()))
                            {
                                var result = NMSConstants.getServerApps(MetricConfigStore.getStore().getItemsByObject(objects.getJsonObject(index).getLong(ID)));

                                object.put(NMSConstants.APPS, result.getJsonArray(NMSConstants.APPS));

                                object.put(NMSConstants.APP_PROCESS, result.getJsonObject(NMSConstants.APP_PROCESS));
                            }

                            else if (object.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.CLOUD.getName()))
                            {
                                var item = ObjectConfigStore.getStore().getItem(object.getLong(ID));

                                //#24621 AWS Cloud | AWS ELB | Network and Application sub category required. object.make.model only available in AWS ELB remaining cloud instance same as it's.

                                if (item.containsKey(AIOpsObject.OBJECT_MAKE_MODEL))
                                {
                                    object.put(AIOpsObject.OBJECT_MAKE_MODEL, StringUtils.capitalize(ObjectConfigStore.getStore().getItem(object.getLong(ID)).getString(AIOpsObject.OBJECT_MAKE_MODEL).toLowerCase()));
                                }
                            }

                            else if (object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.VMWARE_ESXI.getName()) || object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.CITRIX_XEN.getName()) || object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.HYPER_V.getName()) || object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.NUTANIX.getName()))
                            {
                                object.put(NMSConstants.VMS, NMSConstants.getInstances(objects.getJsonObject(index).getLong(ID), NMSConstants.VMS));
                            }

                            else if (object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.CISCO_WIRELESS.getName()) || object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.RUCKUS_WIRELESS.getName()) || object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.ARUBA_WIRELESS.getName()))
                            {
                                object.put(NMSConstants.ACCESS_POINTS, NMSConstants.getInstances(objects.getJsonObject(index).getLong(ID), NMSConstants.ACCESS_POINTS));
                            }

                            else if (object.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVICE_CHECK.getName()) && object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(PORT))
                            {

                                //24616
                                object.put(AIOpsObject.OBJECT_TARGET, object.getString(AIOpsObject.OBJECT_TARGET) + ":" + ObjectConfigStore.getStore().getItem(object.getLong(ID)).getJsonObject(AIOpsObject.OBJECT_CONTEXT).getInteger(PORT));

                            }

                            if (object.getJsonArray(AIOpsObject.OBJECT_TAGS) != null)
                            {
                                object.put(AIOpsObject.OBJECT_TAGS, TagConfigStore.getStore().getItems(object.getJsonArray(AIOpsObject.OBJECT_TAGS)));
                            }
                        }

                        configChanges.clear();

                        configChanges.addAll(objects);

                        statuesBySessionId.forEach((key, value) -> statuesBySessionId.put(key, true));

                        timerHandler = false;
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);
                    }
                });
            }
        }).exceptionHandler(LOGGER::error);

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_USER_PING, message ->
        {
            var event = message.body();

            try
            {
                var sessionId = event.getString(SESSION_ID);

                ticksBySessionId.put(sessionId, System.currentTimeMillis());

                statuesBySessionId.computeIfAbsent(sessionId, value -> false);

                var user = UserConfigStore.getStore().getItemByValue(User.USER_NAME, event.getString(User.USER_NAME));

                if (statuesBySessionId.get(sessionId))
                {
                    if (user.getLong(ID).equals(DEFAULT_ID))//admin user full rights of all objects
                    {
                        if (!configChanges.isEmpty())
                        {
                            statuesBySessionId.put(sessionId, false);

                            publish(sessionId, EventBusConstants.EVENT_CONFIG_QUERY, getConfigContext().put(NMSConstants.METRIC_DATABASE, OBJECT_ITEMS)
                                    .put(NMSConstants.OBJECTS, MotadataConfigUtil.archivedObjectEnabled() ? configChanges.addAll(ArchivedObjectConfigStore.getStore().getArchivedObjects()) : configChanges));
                        }
                    }
                    else
                    {
                        var qualifiedObjects = new JsonArray();

                        if (!configChanges.isEmpty())
                        {
                            for (var i = 0; i < configChanges.size(); i++)
                            {
                                var object = configChanges.getJsonObject(i);

                                if (user.getJsonArray(User.USER_GROUPS).stream().anyMatch(object.getJsonArray(AIOpsObject.OBJECT_GROUPS)::contains))
                                {
                                    qualifiedObjects.add(object);
                                }
                            }
                        }

                        statuesBySessionId.put(sessionId, false);

                        publish(sessionId, EventBusConstants.EVENT_CONFIG_QUERY, getConfigContext().put(NMSConstants.METRIC_DATABASE, OBJECT_ITEMS)
                                .put(NMSConstants.OBJECTS, MotadataConfigUtil.archivedObjectEnabled() ? qualifiedObjects.addAll(ArchivedObjectConfigStore.getStore().getArchivedObjects()) : qualifiedObjects));
                    }
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(EventBusConstants.EVENT_CONFIG_QUERY, message ->
        {
            var event = message.body();

            try
            {
                var user = UserConfigStore.getStore().getItemByValue(User.USER_NAME, event.getString(User.USER_NAME));

                var sessionId = event.getString(SESSION_ID);

                ticksBySessionId.put(sessionId, System.currentTimeMillis());

                statuesBySessionId.put(sessionId, false);

                var objects = ObjectConfigStore.getStore().getFilteredItems();

                for (var index = 0; index < objects.size(); index++)
                {
                    var object = objects.getJsonObject(index);

                    if (object.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVER.getName()))
                    {
                        var result = NMSConstants.getServerApps(MetricConfigStore.getStore().getItemsByObject(objects.getJsonObject(index).getLong(ID)));

                        object.put(NMSConstants.APPS, result.getJsonArray(NMSConstants.APPS));

                        object.put(NMSConstants.APP_PROCESS, result.getJsonObject(NMSConstants.APP_PROCESS));
                    }

                    else if (object.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.CLOUD.getName()))
                    {
                        var item = ObjectConfigStore.getStore().getItem(object.getLong(ID));

                        //#24621 AWS Cloud | AWS ELB | Network and Application sub category required. object.make.model only available in AWS ELB remaining cloud instance same as it's.

                        if (item.containsKey(AIOpsObject.OBJECT_MAKE_MODEL))
                        {
                            object.put(AIOpsObject.OBJECT_MAKE_MODEL, StringUtils.capitalize(ObjectConfigStore.getStore().getItem(object.getLong(ID)).getString(AIOpsObject.OBJECT_MAKE_MODEL).toLowerCase()));
                        }
                    }

                    else if (object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.VMWARE_ESXI.getName()) || object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.CITRIX_XEN.getName()) || object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.HYPER_V.getName()) || object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.NUTANIX.getName()))
                    {
                        object.put(NMSConstants.VMS, NMSConstants.getInstances(objects.getJsonObject(index).getLong(ID), NMSConstants.VMS));
                    }

                    else if (object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.CISCO_WIRELESS.getName()) || object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.RUCKUS_WIRELESS.getName()) || object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.ARUBA_WIRELESS.getName()))
                    {
                        object.put(NMSConstants.ACCESS_POINTS, NMSConstants.getInstances(objects.getJsonObject(index).getLong(ID), NMSConstants.ACCESS_POINTS));
                    }

                    else if (object.getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVICE_CHECK.getName()) && object.getString(AIOpsObject.OBJECT_TYPE).equalsIgnoreCase(PORT))
                    {

                        //24616
                        object.put(AIOpsObject.OBJECT_TARGET, object.getString(AIOpsObject.OBJECT_TARGET) + ":" + ObjectConfigStore.getStore().getItem(object.getLong(ID)).getJsonObject(AIOpsObject.OBJECT_CONTEXT).getInteger(PORT));

                    }

                    if (object.getJsonArray(AIOpsObject.OBJECT_TAGS) != null)
                    {
                        object.put(AIOpsObject.OBJECT_TAGS, TagConfigStore.getStore().getItems(object.getJsonArray(AIOpsObject.OBJECT_TAGS)));
                    }
                }

                if (user.getLong(ID).equals(DEFAULT_ID))//admin user full rights of all objects
                {
                    publish(event.getString(SESSION_ID), EventBusConstants.EVENT_CONFIG_QUERY, getConfigContext().put(NMSConstants.METRIC_DATABASE, OBJECT_ITEMS)
                            .put(NMSConstants.OBJECTS, MotadataConfigUtil.archivedObjectEnabled() ? objects.addAll(ArchivedObjectConfigStore.getStore().getArchivedObjects()) : objects));
                }

                else
                {
                    var qualifiedObjects = new JsonArray();

                    for (var i = 0; i < objects.size(); i++)
                    {
                        try
                        {
                            var object = objects.getJsonObject(i);

                            if (user.getJsonArray(User.USER_GROUPS).stream().anyMatch(object.getJsonArray(AIOpsObject.OBJECT_GROUPS)::contains))
                            {
                                qualifiedObjects.add(object);
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);
                        }
                    }

                    publish(event.getString(SESSION_ID), EventBusConstants.EVENT_CONFIG_QUERY, getConfigContext().put(NMSConstants.METRIC_DATABASE, OBJECT_ITEMS)
                            .put(NMSConstants.OBJECTS, MotadataConfigUtil.archivedObjectEnabled() ? qualifiedObjects.addAll(ArchivedObjectConfigStore.getStore().getArchivedObjects()) : qualifiedObjects));
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);
            }
        });

        vertx.eventBus().<JsonObject>localConsumer(UI_NOTIFICATION_USER_NOTIFICATION_CLEAR, message ->
        {
            var event = message.body();

            vertx.eventBus().<JsonObject>request(EVENT_USER_NOTIFICATION_CLEAR, event, reply ->
                    publish(event.getString(SESSION_ID), UI_NOTIFICATION_USER_NOTIFICATION_CLEAR, reply.result().body()));

        }).exceptionHandler(LOGGER::error);

        vertx.setPeriodic(TimeUnit.MINUTES.toMillis(MotadataConfigUtil.getUISessionTimeoutMinutes()), timer ->
        {
            var iterator = ticksBySessionId.entrySet().iterator();

            while (iterator.hasNext())
            {
                var entry = iterator.next();

                if (entry.getValue() < System.currentTimeMillis() - TimeUnit.MINUTES.toMillis(5)) // user is inactive for more than 5 minutes
                {
                    if (CommonUtil.debugEnabled())
                    {
                        LOGGER.debug("removing " + entry.getKey());
                    }

                    statuesBySessionId.remove(entry.getKey());

                    iterator.remove();
                }
            }
        });

        promise.complete();
    }

    private JsonObject getConfigContext()
    {
        var metricTypes = MetricConfigStore.getStore().getMetricTypes();

        //add agent metric type like if agent is linux based than add linux metric type for custom metric plugin to avoid agent metric provision
        ObjectConfigStore.getStore().getItemsByAgentIds().stream().filter(item -> !metricTypes.contains(item.getString(AIOpsObject.OBJECT_TYPE)))
                .map(item -> item.getString(AIOpsObject.OBJECT_TYPE)).distinct().toList().forEach(metricTypes::add);

        return new JsonObject().put(AIOpsObject.OBJECT_TYPE, ObjectConfigStore.getStore().getObjectTypes())
                .put(Metric.METRIC_TYPE, metricTypes)
                .put("license", LicenseUtil.getLicenseDetails());
    }
}
