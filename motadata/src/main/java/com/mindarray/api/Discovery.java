/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.aiops.AIOpsConstants;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.REQUEST_GET;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.User.USER_NAME;

public class Discovery extends AbstractAPI
{
    public static final String DISCOVERY_CREDENTIAL_PROFILES = "discovery.credential.profiles";
    public static final String DISCOVERY_CATEGORY = "discovery.category";
    public static final String DISCOVERY_OBJECT_TYPE = "discovery.object.type";
    public static final String DISCOVERY_TYPE = "discovery.type";
    public static final String DISCOVERY_TYPE_IP_ADDRESS = "ip.address";
    public static final String DISCOVERY_TYPE_IP_ADDRESS_RANGE = "ip.address.range";
    public static final String DISCOVERY_TYPE_CSV = "csv";
    public static final String DISCOVERY_TYPE_CIDR = "cidr";
    public static final String DISCOVERY_GROUPS = "discovery.groups";
    public static final String DISCOVERY_EVENT_PROCESSORS = "discovery.event.processors";
    public static final String DISCOVERY_CONTEXT = "discovery.context";
    public static final String DISCOVERY_NAME = "discovery.name";
    public static final String DISCOVERY_TARGET = "discovery.target";
    public static final String DISCOVERY_TARGET_TYPE = "discovery.target.type";
    public static final String DISCOVERY_AGENTS = "discovery.agents";
    public static final String DISCOVERY_EMAIL_RECIPIENTS = "discovery.email.recipients";
    public static final String DISCOVERY_SMS_RECIPIENTS = "discovery.sms.recipients";
    public static final String DISCOVERY_STATUS = "discovery.status";
    public static final String DISCOVERY_METHOD = "discovery.method";
    public static final String DISCOVERY_PROGRESS = "discovery.progress";
    public static final String DISCOVERY_TOTAL_OBJECTS = "discovery.total.objects";
    public static final String DISCOVERY_DISCOVERED_OBJECTS = "discovery.discovered.objects";
    public static final String DISCOVERY_FAILED_OBJECTS = "discovery.failed.objects";
    public static final String DISCOVERY_SCHEDULER = "discovery.scheduler";
    public static final String DISCOVERY_STATISTICS = "discovery.statistics";
    public static final String DISCOVERY_TARGET_NAME = "discovery.target.name"; // ui team will use key to display discovery target
    public static final String DISCOVERY_EXCLUDE_TARGET_TYPE = "discovery.exclude.target.type";
    public static final String DISCOVERY_EXCLUDE_TARGETS = "discovery.exclude.targets";
    public static final Integer DISCOVERY_CSV_MAX_PROBE_LIMIT = 512;
    public static final String DISCOVERY_USER_TAGS = "discovery.user.tags";
    public static final String DISCOVERY_CONFIG_MANAGEMENT_STATUS = "discovery.config.management.status";
    public static final String DISCOVER_DOWN_INTERFACE_STATUS = "discover.down.interface.status";
    private static final Logger LOGGER = new Logger(Discovery.class, MOTADATA_API, "Discovery API");

    public Discovery()
    {
        super("discoveries", DiscoveryConfigStore.getStore(), LOGGER);

    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router, Set.of(REQUEST_GET));

            router.get("/" + endpoint + "/:id").handler(this::get);

            router.get("/" + endpoint).handler(this::getAll);

            router.post("/" + endpoint + "/application/run").handler(this::runApplicationDiscovery); // Run application discovery API

            router.post("/" + endpoint + "/:id/run").handler(this::runDiscovery); // Run discovery API

            router.post("/" + endpoint + "/:id/abort").handler(this::abortDiscovery); // Abort running discovery API

            router.get("/" + endpoint + "/:id/progress").handler(this::getProgress); // Get discovery progress API

            router.get("/" + endpoint + "/:id/result").handler(this::getResult); // Get discovery result API

            router.put("/" + endpoint + "/:id/result").handler(this::updateResult); // Update discovery result API

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    /**
     * Once discovery profile deleted motadata will check if any scheduler created on it than it will delete that schedulers as well as remove cron job for it.
     */
    @Override
    protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
    {
        JobScheduler.deleteSchedulers(entity, JobScheduler.JobType.DISCOVERY.getName());

        return super.afterDelete(entity, routingContext);
    }

    @Override
    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        return qualify(routingContext, APIConstants.REQUEST_CREATE);
    }

    @Override
    protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
    {
        return qualify(routingContext, APIConstants.REQUEST_UPDATE);
    }

    private Future<JsonObject> qualify(RoutingContext routingContext, String requestType)
    {
        var promise = Promise.<JsonObject>promise();

        var requestParameters = routingContext.body().asJsonObject();

        if (requestParameters.getJsonArray(DISCOVERY_USER_TAGS) != null && !requestParameters.getJsonArray(DISCOVERY_USER_TAGS).isEmpty())
        {
            requestParameters.put(DISCOVERY_USER_TAGS, TagConfigStore.getStore().addItems(requestParameters.getJsonArray(DISCOVERY_USER_TAGS), Tag.TagType.OBJECT.getName(), ConfigDBConstants.ENTITY_TYPE_USER));
        }

        if (requestType.equalsIgnoreCase(APIConstants.REQUEST_CREATE))
        {
            promise.complete(format(requestParameters.put(DISCOVERY_STATUS, "Not Run Yet").
                    put(DISCOVERY_TOTAL_OBJECTS, 0).put(DISCOVERY_DISCOVERED_OBJECTS, 0)));
        }
        else
        {
            //#24527
            promise.complete(DiscoveryConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID))).mergeIn(format(requestParameters)));
        }

        return promise.future();
    }

    private JsonObject format(JsonObject requestParameters)
    {
        var context = new JsonObject().mergeIn(requestParameters);

        if (context.containsKey(DISCOVERY_TARGET_TYPE) &&
                NMSConstants.DiscoveryTargetType.valueOf(context.getString(DISCOVERY_TARGET_TYPE)) == NMSConstants.DiscoveryTargetType.OBJECT)
        {
            context.put(DISCOVERY_TARGET_NAME, ObjectConfigStore.getStore().getItem(CommonUtil.getLong(context.getString(DISCOVERY_TARGET))).getString(AIOpsObject.OBJECT_NAME));
        }
        else if (context.containsKey(DISCOVERY_TARGET) && !context.containsKey(DISCOVERY_TARGET_NAME))
        {
            context.put(DISCOVERY_TARGET_NAME, context.getValue(DISCOVERY_TARGET));
        }

        return context;
    }

    @Override
    protected void get(RoutingContext routingContext)
    {
        try
        {
            var id = CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID));

            var item = DiscoveryConfigStore.getStore().getItem(id);

            if (item != null && !item.isEmpty())
            {
                item.put(DISCOVERY_USER_TAGS, TagConfigStore.getStore().getItems(item.getJsonArray(DISCOVERY_USER_TAGS)));

                if (item.getString(Discovery.DISCOVERY_OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.FTP.getName())
                        || item.getString(Discovery.DISCOVERY_OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.RADIUS.getName()))
                {
                    item = CommonUtil.removeSensitiveFields(item, true);
                }

                var schedulers = SchedulerConfigStore.getStore().getSchedulerObjects(Scheduler.SCHEDULER_CONTEXT, NMSConstants.OBJECTS, new JsonArray().add(id), JobScheduler.JobType.DISCOVERY.getName());

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                        .put(GlobalConstants.RESULT,
                                item.put(NMSConstants.STATE, DiscoveryCacheStore.getStore().discoveryRunning(item.getLong(GlobalConstants.ID)) ? NMSConstants.STATE_RUNNING : NMSConstants.STATE_NOT_RUNNING)
                                        .put(DISCOVERY_SCHEDULER, schedulers.contains(item.getLong(ID)) ? YES : NO)));

            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, new JsonObject()));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected void getAll(RoutingContext routingContext)
    {
        try
        {
            var entities = new JsonArray();

            var items = DiscoveryConfigStore.getStore().getItems();

            //will return schedule discovery ids

            var schedulers = SchedulerConfigStore.getStore().getSchedulerObjects(Scheduler.SCHEDULER_CONTEXT, NMSConstants.OBJECTS, DiscoveryConfigStore.getStore().getIds(), JobScheduler.JobType.DISCOVERY.getName());

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                if (item.getString(Discovery.DISCOVERY_OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.FTP.getName())
                        || item.getString(Discovery.DISCOVERY_OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.RADIUS.getName()))
                {
                    item = CommonUtil.removeSensitiveFields(item, true);
                }

                item.put(DISCOVERY_SCHEDULER, schedulers.contains(item.getLong(GlobalConstants.ID)) ? GlobalConstants.YES : GlobalConstants.NO).
                        put(NMSConstants.STATE, DiscoveryCacheStore.getStore().discoveryRunning(item.getLong(GlobalConstants.ID)) ? NMSConstants.STATE_RUNNING : NMSConstants.STATE_NOT_RUNNING);

                entities.add(item);
            }

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, entities));

        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void abortDiscovery(RoutingContext routingContext)
    {
        try
        {
            var discoveryId = CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID));

            if (DiscoveryCacheStore.getStore().discoveryRunning(discoveryId))
            {
                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_DISCOVERY_ABORT, discoveryId);

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.DISCOVERY_ABORT_SUCCEEDED, DiscoveryConfigStore.getStore().getDiscoveryName(discoveryId))));
            }
            else
            {
                this.send(routingContext, new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.DISCOVERY_ABORT_FAILED_NOT_RUNNING, DiscoveryConfigStore.getStore().getDiscoveryName(discoveryId))));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void runDiscovery(RoutingContext routingContext)
    {
        try
        {
            var discoveryId = CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID));

            if (!DiscoveryCacheStore.getStore().discoveryRunning(discoveryId))
            {
                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_DISCOVERY_RUN, new JsonObject().put(ID, discoveryId)
                        .put(USER_NAME, routingContext.user().principal().getString(USER_NAME)));

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.DISCOVERY_START_SUCCEEDED, DiscoveryConfigStore.getStore().getDiscoveryName(discoveryId))));
            }
            else
            {
                this.send(routingContext, new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.DISCOVERY_RUN_FAILED_ALREADY_RUNNING, DiscoveryConfigStore.getStore().getDiscoveryName(discoveryId))));
            }

        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    /**
     * Application discovery
     * 1. Add metric plugin based on metric.type
     * 2. If request parameters contains credential profile id  -> build credential contexts and merge into request parameters
     * 3. Else check all metric credential profiles and if application credential protocol and server credential protocol is same than only merge credential profile (for HTTP/HTTPS and JMS protocol credential profile is optional)
     */
    private void runApplicationDiscovery(RoutingContext routingContext)
    {
        try
        {
            var requestParameters = routingContext.body().asJsonObject();

            if (CommonUtil.debugEnabled())
            {
                LOGGER.debug(String.format("application discovery request %s", CommonUtil.removeSensitiveFields(requestParameters, true)));
            }

            requestParameters.mergeIn(requestParameters.getJsonObject(DISCOVERY_CONTEXT));

            requestParameters.put(Metric.METRIC_PLUGIN, NMSConstants.getMetricPlugin(NMSConstants.Type.valueOfName(requestParameters.getString(Metric.METRIC_TYPE))));

            var targets = requestParameters.getJsonArray(DISCOVERY_TARGET);

            var credentialProfiles = new JsonArray();

            if (requestParameters.containsKey(DISCOVERY_CREDENTIAL_PROFILES))
            {
                var credentials = requestParameters.getJsonArray(DISCOVERY_CREDENTIAL_PROFILES);

                requestParameters.remove(DISCOVERY_CREDENTIAL_PROFILES);

                credentialProfiles.addAll(CredentialProfileConfigStore.getStore().getItems(credentials));

                for (var index = 0; index < credentialProfiles.size(); index++)
                {
                    credentialProfiles.getJsonObject(index).mergeIn(credentialProfiles.getJsonObject(index).getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

                    credentialProfiles.getJsonObject(index).remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);
                }
            }

            var probes = 0;

            for (var index = 0; index < targets.size(); index++)
            {
                var object = ObjectConfigStore.getStore().getItem(targets.getLong(index));

                if (object.getJsonObject(AIOpsObject.OBJECT_CONTEXT) != null)
                {
                    object.mergeIn(object.getJsonObject(AIOpsObject.OBJECT_CONTEXT));

                    object.remove(AIOpsObject.OBJECT_CONTEXT);
                }

                if (!credentialProfiles.isEmpty())
                {
                    object.put(DISCOVERY_CREDENTIAL_PROFILES, credentialProfiles);
                }
                else
                {
                    var items = MetricConfigStore.getStore().getItemsByObjectId(object.getLong(ID));

                    for (var item = 0; item < items.size(); item++)
                    {
                        var metric = items.getJsonObject(item);

                        if (metric.getString(Metric.METRIC_TYPE).equalsIgnoreCase(object.getString(AIOpsObject.OBJECT_TYPE))
                                && metric.getLong(Metric.METRIC_CREDENTIAL_PROFILE) != NOT_AVAILABLE)
                        {
                            var credentialProfile = CredentialProfileConfigStore.getStore().getItem(metric.getLong(Metric.METRIC_CREDENTIAL_PROFILE));

                            if (credentialProfile != null)
                            {
                                // add server credential only if requested application credential protocol and server credential protocol is same
                                if (NMSConstants.getProtocol(NMSConstants.Type.valueOfName(requestParameters.getString(Metric.METRIC_TYPE)))
                                        == NMSConstants.Protocol.valueOfName(credentialProfile.getString(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL)))
                                {
                                    credentialProfile.mergeIn(credentialProfile.getJsonObject(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT));

                                    credentialProfile.remove(CredentialProfile.CREDENTIAL_PROFILE_CONTEXT);

                                    object.put(DISCOVERY_CREDENTIAL_PROFILES, new JsonArray(new ArrayList<JsonObject>(1)).add(credentialProfile));
                                }

                                break;
                            }
                        }
                    }
                }

                if (object.containsKey(AIOpsObject.OBJECT_AGENT))
                {
                    if (requestParameters.getString(DISCOVERY_METHOD).equalsIgnoreCase(NMSConstants.DiscoveryMethod.AGENT.name()))
                    {
                        object.put(AIOpsObject.OBJECT_DISCOVERY_METHOD, NMSConstants.DiscoveryMethod.AGENT.name());
                    }
                    // if object is agent based and credential available for that target or user provide credentials then always run that discovery remotely
                    else if (object.getJsonArray(DISCOVERY_CREDENTIAL_PROFILES) != null && !object.getJsonArray(DISCOVERY_CREDENTIAL_PROFILES).isEmpty())
                    {
                        object.remove(AIOpsObject.OBJECT_AGENT); //remove as now we put condition in plugin engine for this key instead of localhost/127.0.0.1 in object.ip key

                        object.put(AIOpsObject.OBJECT_DISCOVERY_METHOD, NMSConstants.DiscoveryMethod.REMOTE.name());
                    }
                }

                if (object.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.ENABLE.name()))
                {
                    var valid = !object.containsKey(AIOpsObject.OBJECT_AGENT) || NMSConstants.getCategory(NMSConstants.Type.valueOfName(object.getString(AIOpsObject.OBJECT_TYPE))) != NMSConstants.Category.SERVER
                            || ObjectStatusCacheStore.getStore().getItem(object.getLong(ID)) == null
                            || ObjectStatusCacheStore.getStore().getItem(object.getLong(ID)).equalsIgnoreCase(STATUS_UP);

                    //bug-4206

                    if (valid)
                    {
                        object.mergeIn(requestParameters);

                        probes++;

                        route(object.put(AIOpsObject.OBJECT_TYPE, requestParameters.getString(Metric.METRIC_TYPE)).put(Metric.METRIC_OBJECT, targets.getLong(index))
                                .put(DISCOVERY_CATEGORY, object.getString(AIOpsObject.OBJECT_CATEGORY))
                                .put(APIConstants.SESSION_ID, routingContext.user().principal().getString(APIConstants.SESSION_ID)), routingContext.user().principal().getString(USER_NAME));
                    }
                }
            }

            if (probes > 0)
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                        .put(GlobalConstants.MESSAGE, InfoMessageConstants.DISCOVERY_APP_START_SUCCEEDED));
            }
            else
            {
                this.send(routingContext, new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL)
                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.NO_MONITOR_AVAILABLE, requestParameters.getString(Discovery.DISCOVERY_METHOD).equalsIgnoreCase(NMSConstants.DiscoveryMethod.AGENT.name()) ? APIConstants.Entity.AGENT.getName() : APIConstants.Entity.OBJECT.getName())));
            }
        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void route(JsonObject event, String userName)
    {
        var eventId = CommonUtil.newEventId();

        event.put(EventBusConstants.EVENT_ID, eventId).put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_DISCOVERY).put(ID, DUMMY_ID)
                .put(NMSConstants.REDISCOVER_JOB, NMSConstants.RediscoverJob.APP.getName());

        // for dependency mapping put process/service name
        if (event.getJsonObject(NMSConstants.OBJECTS) != null)
        {
            event.put(AIOpsConstants.DEPENDENCY_DEPENDENT_FIELD, event.getJsonObject(NMSConstants.OBJECTS).containsKey(SystemProcess.SYSTEM_PROCESS) ? event.getJsonObject(NMSConstants.OBJECTS).getString(SystemProcess.SYSTEM_PROCESS) : event.getJsonObject(NMSConstants.OBJECTS).getString(SystemService.SYSTEM_SERVICE));
        }

        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_ADD, new JsonObject().put(EventBusConstants.EVENT_CONTEXT, event)
                .put(EventBusConstants.EVENT_ID, eventId)
                .put(USER_NAME, userName)
                .put(EventBusConstants.EVENT_STATE, EventBusConstants.EVENT_STATE_QUEUED)
                .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_DISCOVERY));

        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_ROUTER, event);
    }

    private void getResult(RoutingContext routingContext)
    {
        try
        {
            Bootstrap.configDBService().getAll(ConfigDBConstants.COLLECTION_DISCOVERY_RESULT + CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID)),
                    future ->
                    {
                        var items = new JsonArray();

                        if (future.succeeded() && !future.result().isEmpty())
                        {
                            var discovery = DiscoveryConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID)));

                            var type = NMSConstants.Type.valueOfName(discovery.getString(DISCOVERY_OBJECT_TYPE));

                            if (discovery.getString(DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVICE_CHECK.getName()))
                            {
                                if (NMSConstants.DiscoveryMethod.valueOf(discovery.getString(DISCOVERY_METHOD)) == NMSConstants.DiscoveryMethod.AGENT)
                                {
                                    if (discovery.getString(DISCOVERY_OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.PORT.getName()))
                                    {
                                        var objects = ObjectConfigStore.getStore().getItemsByType(type, NMSConstants.DiscoveryMethod.AGENT.name())
                                                .stream().collect(Collectors.groupingBy(item -> item.getString(AIOpsObject.OBJECT_TARGET),
                                                        Collectors.groupingBy(item -> item.getLong(AIOpsObject.OBJECT_AGENT),
                                                                Collectors.mapping(item -> item.getJsonObject(AIOpsObject.OBJECT_CONTEXT).getInteger(PORT), Collectors.toList()))));

                                        for (var index = 0; index < future.result().size(); index++)
                                        {
                                            var result = future.result().getJsonObject(index);

                                            if (objects.containsKey(result.getString(AIOpsObject.OBJECT_TARGET)) &&
                                                    objects.get(result.getString(AIOpsObject.OBJECT_TARGET)).containsKey(result.getLong(AIOpsObject.OBJECT_AGENT))
                                                    && objects.get(result.getString(AIOpsObject.OBJECT_TARGET)).get(result.getLong(AIOpsObject.OBJECT_AGENT)).contains(result.getJsonObject(AIOpsObject.OBJECT_CONTEXT).getInteger(PORT)))
                                            {
                                                result.put(AIOpsObject.OBJECT_STATE, NMSConstants.State.PROVISION.name());
                                            }
                                            else if (result.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.PROVISION.name()))
                                            {
                                                result.put(AIOpsObject.OBJECT_STATE, NMSConstants.State.UNPROVISION.name());
                                            }

                                            items.add(result);
                                        }

                                    }
                                    else
                                    {
                                        var objects = ObjectConfigStore.getStore().getItemsByType(type, NMSConstants.DiscoveryMethod.AGENT.name())
                                                .stream().collect(Collectors.groupingBy(item -> item.getString(AIOpsObject.OBJECT_TARGET),
                                                        Collectors.mapping(item -> item.getLong(AIOpsObject.OBJECT_AGENT), Collectors.toList())));

                                        for (var index = 0; index < future.result().size(); index++)
                                        {
                                            var item = future.result().getJsonObject(index);

                                            if (objects.containsKey(item.getString(AIOpsObject.OBJECT_TARGET)) &&
                                                    objects.get(item.getString(AIOpsObject.OBJECT_TARGET)).contains(item.getLong(AIOpsObject.OBJECT_AGENT)))
                                            {
                                                item.put(AIOpsObject.OBJECT_STATE, NMSConstants.State.PROVISION.name());
                                            }

                                            else if (item.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.PROVISION.name()))
                                            {
                                                item.put(AIOpsObject.OBJECT_STATE, NMSConstants.State.UNPROVISION.name());
                                            }

                                            items.add(item);
                                        }
                                    }
                                }
                                else
                                {
                                    if (discovery.getString(DISCOVERY_OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.PORT.getName()))
                                    {
                                        var objects = ObjectConfigStore.getStore().getItemsByType(type, NMSConstants.DiscoveryMethod.REMOTE.name())
                                                .stream().collect(Collectors.groupingBy(item -> JsonObject.mapFrom(item).getString(AIOpsObject.OBJECT_TARGET),
                                                        Collectors.mapping(item -> JsonObject.mapFrom(item).getJsonObject(AIOpsObject.OBJECT_CONTEXT).getInteger(PORT), Collectors.toList())));

                                        for (var index = 0; index < future.result().size(); index++)
                                        {
                                            var item = future.result().getJsonObject(index);

                                            if (objects.containsKey(item.getString(AIOpsObject.OBJECT_TARGET)) &&
                                                    objects.get(item.getString(AIOpsObject.OBJECT_TARGET)).contains(item.getJsonObject(AIOpsObject.OBJECT_CONTEXT).getInteger(PORT)))
                                            {
                                                item.put(AIOpsObject.OBJECT_STATE, NMSConstants.State.PROVISION.name());
                                            }

                                            else if (item.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.PROVISION.name()))
                                            {
                                                item.put(AIOpsObject.OBJECT_STATE, NMSConstants.State.UNPROVISION.name());
                                            }

                                            items.add(item);
                                        }
                                    }

                                    else
                                    {
                                        enrich(future.result(), discovery.getString(DISCOVERY_OBJECT_TYPE).equalsIgnoreCase(NMSConstants.Type.PING.getName())
                                                        ? ObjectConfigStore.getStore().getItemsByType(type, NMSConstants.DiscoveryMethod.REMOTE.name()).stream()
                                                        .filter(item -> discovery.getString(Discovery.DISCOVERY_CATEGORY).equalsIgnoreCase(item.getString(AIOpsObject.OBJECT_CATEGORY)))
                                                        .map(item -> item.getString(AIOpsObject.OBJECT_TARGET)).collect(Collectors.toList())
                                                        : ObjectConfigStore.getStore().getItemsByType(type, NMSConstants.DiscoveryMethod.REMOTE.name())
                                                        .stream().map(item -> item.getString(AIOpsObject.OBJECT_TARGET)).collect(Collectors.toList()),
                                                items, null);
                                    }
                                }
                            }

                            else
                            {
                                var category = type == NMSConstants.Type.PING && discovery.containsKey(DISCOVERY_CATEGORY)
                                        && discovery.getString(DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.OTHER.getName())
                                        ? NMSConstants.Category.valueOfName(discovery.getString(DISCOVERY_CATEGORY)) : NMSConstants.getCategory(type);

                                var objects = new JsonArray();

                                if (category == NMSConstants.Category.OTHER && type == NMSConstants.Type.PING)
                                {
                                    objects.addAll(ObjectConfigStore.getStore().getItemsByValues(AIOpsObject.OBJECT_CATEGORY, new JsonArray()
                                            .add(NMSConstants.Category.SERVER.getName()).add(NMSConstants.Category.NETWORK.getName())
                                            .add(NMSConstants.Category.OTHER.getName()).add(NMSConstants.Category.VIRTUALIZATION.getName())
                                            .add(NMSConstants.Category.HCI.getName())
                                            .add(NMSConstants.Category.SDN.getName()).add(NMSConstants.Category.STORAGE.getName())));
                                }

                                else
                                {
                                    objects.addAll(category == NMSConstants.Category.NETWORK || category == NMSConstants.Category.CLOUD || category == NMSConstants.Category.SERVER || category == NMSConstants.Category.HCI || category == NMSConstants.Category.SDN
                                            || category == NMSConstants.Category.STORAGE
                                            ? ObjectConfigStore.getStore().getItems(ObjectConfigStore.getStore().getItemsByCategory(category))
                                            : ObjectConfigStore.getStore().getItems(ObjectConfigStore.getStore().getItemsByType(type)));

                                    if (category == NMSConstants.Category.NETWORK || category == NMSConstants.Category.SERVER || category == NMSConstants.Category.VIRTUALIZATION || category == NMSConstants.Category.HCI || category == NMSConstants.Category.SDN
                                            || category == NMSConstants.Category.STORAGE || category == NMSConstants.Category.OTHER)
                                    {
                                        objects.addAll(ObjectConfigStore.getStore().flatItemsByValue(AIOpsObject.OBJECT_CATEGORY, NMSConstants.Category.OTHER.getName(), AIOpsObject.OBJECT_TYPE, NMSConstants.Type.PING.getName()));
                                    }
                                }

                                enrich(future.result(), objects.stream().map(item -> JsonObject.mapFrom(item).getString(AIOpsObject.OBJECT_TARGET)).collect(Collectors.toList()), items,
                                        category == NMSConstants.Category.CLOUD ? objects.stream().collect(Collectors.groupingBy(item -> JsonObject.mapFrom(item).getString(AIOpsObject.OBJECT_TARGET),
                                                Collectors.mapping(item -> JsonObject.mapFrom(item).getString(AIOpsObject.OBJECT_TYPE), Collectors.toList()))) : null);
                            }
                        }

                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, items));
                    });
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void enrich(JsonArray probes, List<String> objects, JsonArray items, Map<String, List<String>> qualifiedObjects)
    {

        for (var index = 0; index < probes.size(); index++)
        {

            var probe = probes.getJsonObject(index);

            var discoveryState = probe.getString(AIOpsObject.OBJECT_STATE);

            // Now object state "failed" will supersede all other states as in case of failed discovery provisioned objects should not be listed in result

            if (discoveryState != null && !discoveryState.equalsIgnoreCase(STATUS_FAILED))
            {
                var target = probe.getString(AIOpsObject.OBJECT_TARGET);

                if ((objects.contains(target) || objects.contains(probe.getString(AIOpsObject.OBJECT_IP)))
                        && (qualifiedObjects == null || (qualifiedObjects.containsKey(target) && qualifiedObjects.get(target).contains(probe.getString(AIOpsObject.OBJECT_TYPE))))) // for cloud category only we have qualifiedObjects else it's null (#bug - 20659)
                {
                    probe.put(AIOpsObject.OBJECT_STATE, NMSConstants.State.PROVISION.name());
                }

                else if (probe.getString(AIOpsObject.OBJECT_STATE).equalsIgnoreCase(NMSConstants.State.PROVISION.name()))
                {
                    probe.put(AIOpsObject.OBJECT_STATE, NMSConstants.State.UNPROVISION.name());
                }
            }

            items.add(probe);
        }
    }

    private void updateResult(RoutingContext routingContext)
    {
        try
        {
            Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_DISCOVERY_RESULT + CommonUtil.getLong(routingContext.request().getParam(ID)),
                    new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, routingContext.body().asJsonObject().getLong(ID)),
                    routingContext.body().asJsonObject(),
                    routingContext.user().principal().getString(USER_NAME),
                    routingContext.request().remoteAddress().host(),
                    result ->
                    {
                        if (result.succeeded())
                        {
                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                    .put(ID, routingContext.body().asJsonObject().getLong(ID))
                                    .put(MESSAGE, String.format(InfoMessageConstants.DISCOVERY_RESULT_UPDATE_SUCCEEDED, DiscoveryConfigStore.getStore().getDiscoveryName(CommonUtil.getLong(routingContext.request().getParam(ID))))));

                        }
                        else
                        {
                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL)
                                    .put(MESSAGE, String.format(ErrorMessageConstants.DISCOVERY_RESULT_UPDATE_FAILED, DiscoveryConfigStore.getStore().getDiscoveryName(CommonUtil.getLong(routingContext.request().getParam(ID))))));

                        }
                    });
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void getProgress(RoutingContext routingContext)
    {
        try
        {
            var progresses = DiscoveryCacheStore.getStore().getDiscoveryProgressEvents(CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID)));

            var discovery = DiscoveryConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID)));

            //why this condition...bcz cloud discovery hasn't any probes/target and if user come into progress page from any other page at that time it will throw an error...so need this condition
            if (discovery != null && !discovery.getString(DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.CLOUD.getName()) && !discovery.getString(DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.HCI.getName())
                    && !discovery.getString(DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SDN.getName()) && !discovery.getString(DISCOVERY_CATEGORY).equalsIgnoreCase(NMSConstants.Category.STORAGE.getName()))
            {
                var probes = DiscoveryCacheStore.getStore().getProbes(CommonUtil.getLong(routingContext.request().getParam(ID)));

                if (discovery.containsKey(DISCOVERY_METHOD) && discovery.getString(DISCOVERY_METHOD).equalsIgnoreCase(NMSConstants.DiscoveryMethod.AGENT.name()))
                {
                    for (var index = 0; index < progresses.size(); index++)
                    {
                        var progress = progresses.getJsonObject(index);

                        if (probes.getJsonArray(progress.getString(AIOpsObject.OBJECT_TARGET)) != null)
                        {
                            var iterator = probes.getJsonArray(progress.getString(AIOpsObject.OBJECT_TARGET)).iterator();

                            while (iterator.hasNext())
                            {
                                if (progress.getLong(AIOpsObject.OBJECT_AGENT).equals(JsonObject.mapFrom(iterator.next()).getLong(AIOpsObject.OBJECT_AGENT)))
                                {
                                    iterator.remove();
                                }
                            }
                        }
                    }
                }

                else
                {
                    for (var index = 0; index < progresses.size(); index++)
                    {
                        var progress = progresses.getJsonObject(index);

                        if (probes.containsKey(progress.getString(AIOpsObject.OBJECT_TARGET)))
                        {
                            probes.remove(progress.getString(AIOpsObject.OBJECT_TARGET));
                        }
                    }
                }

                if (!probes.isEmpty())
                {
                    probes.getMap().values().forEach(probe -> ((JsonArray) probe).forEach(progresses::add));
                }
            }

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                    .put(GlobalConstants.RESULT, new JsonObject().put(RESULT, progresses)
                            .put(DISCOVERY_STATISTICS, DiscoveryCacheStore.getStore().getDiscoveryStatistics(CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID))))));
        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }
}
