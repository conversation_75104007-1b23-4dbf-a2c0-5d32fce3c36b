/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*

 *   Change Logs:
 *   Date            Author              Notes
 *   4-Feb-2025      Deven       Added Template as an entity.
 *   28-Feb-2025     Bharat      Added Integration in credential profile relation
 *   4-Mar-2025      Bharat      MOTADATA-4740: Two factor authentication 2FA
 *   2-Apr-2025      Bharat      MOTADATA-5637: Domain Mapping in Flow
 *   20-Feb-2025     Pruthviraj   NetRoute and NetRoute policy entity added , NetRoute policy constant added in REF_PROPS_BY_ENTITY
 *   23-May-2025     Pruthviraj   NetRoute entity added , NetRoute constant added in REF_PROPS_BY_ENTITY
 */

package com.mindarray.api;

import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.http.Cookie;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.mindarray.visualization.VisualizationConstants.*;

public class APIConstants
{


    public static final String CONTENT_TYPE = "content-type";

    // Active User Refresh timeout
    public static final int USER_SESSION_INACTIVE_TIMEOUT_SECONDS = MotadataConfigUtil.getUserSessionInactiveTimeoutSeconds();

    //API server constants
    public static final String CONTENT_TYPE_APPLICATION_JSON = "application/json";
    public static final String CONTENT_TYPE_APPLICATION_STREAM = "application/octet-stream";
    public static final String CONTENT_TYPE_TEXT_HTML = "text/html";
    public static final String TOKEN_ID = "token_id";
    public static final String TOKEN_TYPE = "token.type";
    public static final String TOKEN_USER = "user.id";
    public static final String ALGO_RS256 = "RS256";
    public static final String ALGO_RS512 = "RS512";
    public static final String USER_PERMISSIONS = "permissions";
    public static final String SESSION_ID = "session-id";
    public static final String CLIENT_ID = "client.id";
    public static final String UI_CLIENT_ID = "Q5VZ97naQhyLIH0Vz4MSXvzbMyCYTjPwz+1hVJ643pA=";
    public static final String COOKIE = Cookie.cookie(CLIENT_ID, UI_CLIENT_ID).encode();
    // default file upload size is 1gb
    public static final Long MAX_BODY_LIMIT_BYTES = 1048576000L;
    public static final int MAX_ACCESS_TOKEN_VALIDITY_TIME_MINUTES = 10;
    public static final int MAX_REFRESH_TOKEN_VALIDITY_TIME_MINUTES = 30 * 60 * 24;
    public static final String API_VERSION = "Motadata API v1";
    public static final String API_AUTHOR = "Motadata";
    public static final String FILENAME = "filename";
    public static final String AUTH_REFRESH_TOKEN = "refresh.token";
    public static final String AUTH_ACCESS_TOKEN = "access.token";
    public static final String LAST_ACTIVE_TIMESTAMP = "last.active.timestamp";
    public static final String AUTH_TOTP = "totp";

    // hsts validation period - for secure token
    public static final long HSTS_VALIDATION_PERIOD = 31536000L;

    // API Entity Constants
    public static final String ENTITY_COLLECTION = "collection";
    public static final String ENTITY_NAME = "entity";
    public static final String ENTITY_ID = "entity.id";
    public static final String ENTITY_PROPERTY = "props";
    public static final String ENTITY_PROPERTY_NAME = "name";
    public static final String ENTITY_PROPERTY_TITLE = "title";
    public static final String ENTITY_PROPERTY_RULES = "rules";
    public static final String ENTITY_PROPERTY_VALUES = "values";
    public static final String ENTITY_PROPERTY_DEFAULT = "default";
    public static final String ENTITY_PROPERTY_TYPE = "type";
    public static final String ENTITY_PROPERTY_PRIVATE = "private";
    public static final String ENTITY_PROPERTY_DIVIDE_VALUE = "divide-value";
    public static final String ENTITY_PROPERTY_PREREQUISITES = "prerequisites";
    public static final String ENTITY_PROPERTY_COUNT = "count";
    public static final String FIELD_TYPE_NUMERIC = "numeric";
    public static final String FIELD_TYPE_LIST = "list";
    public static final String FIELD_TYPE_MAP = "map";
    public static final String FIELD_TYPE_SECURED = "password";
    public static final String RESPONSE_CODE = "response-code";
    //only use when needs to pass bulk ids in parameters
    public static final String REQUEST_PARAM_IDS = "ids"; // for bulk id
    // only use when needs to pass bulk body in parameters
    public static final String REQUEST_PARAMS = "params"; // for bulk params
    public static final String REQUEST_PARAM_TYPE = "type";
    public static final String REQUEST = "request";
    public static final String REQUEST_GET = "get";
    public static final String REQUEST_UPDATE = "update";

    public static final String REQUEST_CREATE = "create";
    public static final String REQUEST_DELETE = "delete";
    public static final String REQUEST_DROP = "drop";
    public static final String REQUEST_LOGIN = "login";
    public static final String CONFIG_UPDATED_PROPS = "new.props";
    public static final String CONFIG_OLD_PROPS = "old.props";
    public static final String READ_PERMISSION = "read";

    /* API Permission Constant */
    public static final String READ_WRITE_PERMISSION = "read-write";
    public static final String DELETE_PERMISSION = "delete";
    public static final Pattern PATTERN_IP_ADDRESS = Pattern.compile("^([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\.([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\.([01]?\\d\\d?|2[0-4]\\d|25[0-5])\\.([01]?\\d\\d?|2[0-4]\\d|25[0-5])$");
    public static final Pattern PATTERN_IPV6_ADDRESS = Pattern.compile("\\b^((([0-9A-Fa-f]{1,4}:){7}([0-9A-Fa-f]{1,4}|:))|(([0-9A-Fa-f]{1,4}:){6}(:[0-9A-Fa-f]{1,4}|((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){5}(((:[0-9A-Fa-f]{1,4}){1,2})|:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3})|:))|(([0-9A-Fa-f]{1,4}:){4}(((:[0-9A-Fa-f]{1,4}){1,3})|((:[0-9A-Fa-f]{1,4})?:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){3}(((:[0-9A-Fa-f]{1,4}){1,4})|((:[0-9A-Fa-f]{1,4}){0,2}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){2}(((:[0-9A-Fa-f]{1,4}){1,5})|((:[0-9A-Fa-f]{1,4}){0,3}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(([0-9A-Fa-f]{1,4}:){1}(((:[0-9A-Fa-f]{1,4}){1,6})|((:[0-9A-Fa-f]{1,4}){0,4}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:))|(:(((:[0-9A-Fa-f]{1,4}){1,7})|((:[0-9A-Fa-f]{1,4}){0,5}:((25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)(\\.(25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]?\\d)){3}))|:)))\\s*$\\b");


    /* Validation Rule Constant */
    public static final String SHALLOW_DELETE = "shallow.delete";
    public static final String REFERENCE_ENTITY = "ref.entity";
    public static final String REFERENCE_ENTITY_STORE = "ref.entity.store";
    public static final String REFERENCE_ENTITY_PROPERTY = "ref.entity.prop";
    public static final String REFERENCE_ENTITY_NESTED_PROPERTY = "ref.entity.nested.prop";


    // api modules
    public static final String REFERENCE_ENTITY_PROPERTY_TYPE = "ref.entity.prop.type";
    public static final String REFERENCE_ENTITY_CONTEXT_PROP_KEY = "ref.entity.context.prop.key";
    public static final String REFERENCE_ENTITY_CONTEXT_PROP_VALUE = "ref.entity.context.prop.value";
    public static final String REFERENCE_ENTITY_CONTEXT_PROP_CONDITION = "ref.entity.context.prop.condition";
    public static final Map<String, String> REF_PROPS_BY_ENTITY = Map.ofEntries(Map.entry(NMSConstants.APPS, Metric.METRIC_NAME),
            Map.entry(Entity.OBJECT.getName(), AIOpsObject.OBJECT_NAME),
            Map.entry(Entity.DISCOVERY.getName(), Discovery.DISCOVERY_NAME),
            Map.entry(Entity.METRIC_PLUGIN.getName(), MetricPlugin.METRIC_PLUGIN_NAME),
            Map.entry(Entity.METRIC.getName(), Metric.METRIC_NAME),
            Map.entry(Entity.METRIC_POLICY.getName(), PolicyEngineConstants.POLICY_NAME),
            Map.entry(Entity.EVENT_POLICY.getName(), PolicyEngineConstants.POLICY_NAME),
            Map.entry(Entity.GROUP.getName(), Group.FIELD_GROUP_NAME),
            Map.entry(Entity.USER.getName(), User.USER_NAME),
            Map.entry(Entity.USER_ROLE.getName(), UserRole.USER_ROLE_NAME),
            Map.entry(Entity.CREDENTIAL_PROFILE.getName(), CredentialProfile.CREDENTIAL_PROFILE_NAME),
            Map.entry(Entity.MAIL_SERVER_CONFIGURATION.getName(), MailServerConfiguration.MAIL_SERVER_SENDER),
            Map.entry(Entity.INTEGRATION.getName(), Integration.INTEGRATION_TYPE),
            Map.entry(Entity.SNMP_TRAP_FORWARDER.getName(), SNMPTrapForwarder.SNMP_TRAP_FORWARDER_NAME),
            Map.entry(Entity.RUNBOOK_PLUGIN.getName(), RunbookPlugin.RUNBOOK_PLUGIN_NAME),
            Map.entry(Entity.DASHBOARD.getName(), Dashboard.DASHBOARD_NAME),
            Map.entry(Entity.TEMPLATE.getName(), Template.TEMPLATE_NAME),
            Map.entry(Entity.TOPOLOGY_PLUGIN.getName(), TopologyPlugin.TOPOLOGY_PLUGIN_NAME),
            Map.entry(Entity.LOG_PARSER.getName(), LogParser.LOG_PARSER_NAME),
            Map.entry(Entity.CONFIG_TEMPLATE.getName(), ConfigTemplate.CONFIG_TEMPLATE_NAME),
            Map.entry(Entity.CONFIGURATION.getName(), AIOpsObject.OBJECT_NAME),
            Map.entry(Entity.PERSONAL_ACCESS_TOKEN.getName(), PersonalAccessToken.PERSONAL_ACCESS_TOKEN_NAME),
            Map.entry(Entity.EVENT_SOURCE.getName(), EventBusConstants.EVENT_SOURCE),
            Map.entry(Entity.STORAGE_PROFILE.getName(), StorageProfile.STORAGE_PROFILE_NAME),
            Map.entry(Entity.COMPLIANCE_RULE.getName(), ComplianceRule.COMPLIANCE_RULE_NAME),
            Map.entry(Entity.COMPLIANCE_BENCHMARK.getName(), ComplianceBenchmark.COMPLIANCE_BENCHMARK_NAME),
            Map.entry(Entity.COMPLIANCE_POLICY.getName(), CompliancePolicy.COMPLIANCE_POLICY_NAME),
            Map.entry(Entity.NETROUTE.getName(), NetRoute.NETROUTE_NAME),
            Map.entry(Entity.NETROUTE_POLICY.getName(), PolicyEngineConstants.POLICY_NAME));
    // in api payload as json object , we can validate field by this map by categories
    // ex : API payload must have 'queries' field and "queries" json must have aggregator,data.point field
    // as of now it is validating enough requirement in chart/grid requests
    public static final Map<String, Map<String, Set<String>>> QUERY_RULES = Map.ofEntries(
            Map.entry(VisualizationCategory.HISTOGRAM.getName(), Map.ofEntries(
                    Map.entry("queries", Set.of(AGGREGATOR, DATA_POINT)),
                    Map.entry("timeline", Set.of(FROM_DATE, FROM_TIME, TO_DATE, TO_TIME)),
                    Map.entry("granularity", Set.of()),
                    Map.entry(TYPE, Set.of()))),
            Map.entry(VisualizationCategory.GRID.getName(), Map.ofEntries(
                    Map.entry("queries", Set.of(AGGREGATOR, DATA_POINT)),
                    Map.entry("timeline", Set.of(FROM_DATE, FROM_TIME, TO_DATE, TO_TIME)),
                    Map.entry("result.by", Set.of()),
                    Map.entry(TYPE, Set.of()))));

    static final String RULE_REQUIRED = "required";
    static final String RULE_UNIQUE = "unique";
    static final String RULE_MAXIMUM = "maximum";
    static final String RULE_MINIMUM = "minimum";
    static final String RULE_RANGE = "range";
    static final String RULE_RANGE_DIVISIBLE = "range-divisible";
    static final String USER_SETTINGS = "user-settings";
    static final String AUDIT_SETTINGS = "audit-settings";
    static final String NOTIFICATION_SETTINGS = "notification-settings";
    // Ref entity const
    static final String GROUP_SETTINGS = "group-settings";
    static final String DISCOVERY_SETTINGS = "discovery-settings";
    static final String MONITOR_SETTINGS = "monitor-settings";
    static final String AGENT_SETTINGS = "agent-settings";
    static final String SYSTEM_SETTINGS = "system-settings";
    static final String INTEGRATIONS = "integrations";
    static final String SNMP_TRAP_SETTINGS = "snmp-trap-settings";
    static final String PLUGIN_LIBRARY_SETTINGS = "plugin-library-settings";
    static final String MY_ACCOUNT_SETTINGS = "my-account-settings";
    static final String AIOPS_SETTINGS = "aiops-settings";
    static final String POLICY_SETTINGS = "policy-settings";
    static final String LOG_SETTINGS = "log-settings";
    static final String FLOW_SETTINGS = "flow-settings";
    static final String CONFIG = "config";
    static final String QUERY = "query";
    static final String COMPLIANCE_SETTINGS = "compliance-settings";
    static final String NETROUTE_SETTINGS = "netroute-settings";


    // using for file name while upgrading agent/collector/master

    private APIConstants()
    {

    }

    public enum ReferenceEntityPropertyType
    {
        VALUE,
        MULTI_VALUE,
        CONTEXT_VALUE,
        MULTIMAP_VALUE
    }

    public enum Entity
    {
        OBJECT("Monitor"), //we have mapped object to monitor in UI (for ease of use)

        AGENT("Agent"),

        REMOTE_EVENT_PROCESSOR("Remote Event Processor"),

        DISCOVERY("Discovery Profile"),

        USER("User"),

        PERSONAL_ACCESS_TOKEN("Personal Access Token"),

        MY_ACCOUNT("My Account"),

        METRIC("Metric"),

        CREDENTIAL_PROFILE("Credential Profile"),

        SCHEDULER("Scheduler"),

        METRIC_POLICY("Metric Policy"),

        EVENT_POLICY("Event Policy"),

        USER_ROLE("User Role"),

        GROUP("Group"),

        SNMP_TRAP_FORWARDER("SNMP Trap Forwarder"),

        APPLICATION_MAPPER("Application Mapper"),

        FLOW_AS_MAPPER("Flow AS Mapper"),

        FLOW_DOMAIN_MAPPER("Flow Domain Mapper"),

        FLOW_IP_MAPPER("Flow IP Mapper"),

        FLOW_GEOLOCATION_MAPPER("Flow Geolocation Mapper"),

        SNMP_OID_GROUP("SNMP OID Group"),

        TOPOLOGY_PLUGIN("Topology Plugin"),

        METRIC_PLUGIN("Metric Plugin"),

        BUSINESS_HOUR("Monitoring Hour"),

        RUNBOOK_PLUGIN("Runbook Plugin"),

        DEPENDENCY_MAPPER("Dependency Mapper"),

        DASHBOARD("Dashboard"),

        WIDGET("Widget"),

        LOG_PARSER("Log Parser"),

        LOG_PARSER_PLUGIN("Log Parser Plugin"),

        EVENT_SOURCE("Event Source"),

        CONFIG_TEMPLATE("Config Template"),

        CONFIGURATION("Configuration"),

        BACKUP_PROFILE("Backup Profile"),

        STORAGE_PROFILE("Storage Profile"),

        COMPLIANCE_RULE("Compliance Rule"),

        COMPLIANCE_BENCHMARK("Compliance Benchmark"),

        COMPLIANCE_POLICY("Compliance Policy"),

        MAIL_SERVER_CONFIGURATION("Mail Server Configuration"),

        INTEGRATION("Integration"),

        INTEGRATION_PROFILE("Integration Profile"),

        TEMPLATE("Template"),

        NETROUTE("NetRoute"),

        NETROUTE_POLICY("NetRoute Policy");

        private static final Map<String, Entity> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(Entity::getName, e -> e)));
        private final String name;

        Entity(String name)
        {
            this.name = name;
        }

        public static Entity valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }

    public enum RequestType
    {
        POST(READ_WRITE_PERMISSION),

        PUT(READ_WRITE_PERMISSION),

        DELETE(DELETE_PERMISSION),

        GET(READ_PERMISSION);

        private final String name;

        RequestType(String name)
        {
            this.name = name;
        }

        public String getName()
        {
            return name;
        }
    }

    public enum TokenType
    {
        PERSONAL_ACCESS_TOKEN("Personal Access Token"),
        AUTH_TOKEN("Auth Access Token"),
        REFRESH_TOKEN("Refresh Token");

        private final String name;

        TokenType(String name)
        {
            this.name = name;
        }

        public String getName()
        {
            return name;
        }
    }

}
