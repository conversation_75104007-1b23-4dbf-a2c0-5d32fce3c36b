/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.ErrorCodes;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.store.AbstractConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.visualization.VisualizationConstants;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.RoutingContext;

import java.net.InetAddress;
import java.util.List;
import java.util.Set;

import static com.mindarray.ErrorMessageConstants.*;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.*;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_INTERNAL_SERVER_ERROR;

public class APIUtil
{
    private static final Logger LOGGER = new Logger(APIUtil.class, GlobalConstants.MOTADATA_API, "API Util");

    private APIUtil()
    {

    }

    /* This method validates prerequisites for the specified property. */

    static boolean testPrerequisites(JsonArray prerequisites, JsonObject requestParameters, String type)
    {
        var result = true;

        for (var index = 0; index < prerequisites.size(); index++)
        {
            if (result)
            {
                var prerequisite = prerequisites.getJsonObject(index);

                var rule = prerequisite.getString("rule");

                if (CommonUtil.isNotNullOrEmpty(rule) && requestParameters.containsKey(rule))
                {
                    if (prerequisite.getValue(VALUE) instanceof JsonArray)
                    {
                        var propValues = prerequisite.getJsonArray(VALUE);

                        var valid = false;

                        for (var i = 0; index < propValues.size(); i++)
                        {

                            //Check for List type and prerequisite field is List or String

                            valid = (type.equalsIgnoreCase(FIELD_TYPE_LIST) && (requestParameters.getValue(rule) instanceof List && requestParameters.getJsonArray(rule).contains(propValues.getString(i))) || (requestParameters.getValue(rule) instanceof String && requestParameters.getString(rule).equalsIgnoreCase(propValues.getString(i))))
                                    || (CommonUtil.isNotNullOrEmpty(propValues.getString(i)) && CommonUtil.getString(requestParameters.getValue(rule)).equalsIgnoreCase(propValues.getString(i)));
                        }

                        if (!valid)
                        {
                            result = false;
                        }
                    }

                    else
                    {
                        var value = CommonUtil.getString(prerequisite.getValue(VALUE));

                        //Check for List type and prerequisite field is List or String
                        if (type.equalsIgnoreCase(FIELD_TYPE_LIST) && (requestParameters.getValue(rule) instanceof List && !requestParameters.getJsonArray(rule).contains(value)) || (requestParameters.getValue(rule) instanceof String && !requestParameters.getString(rule).equalsIgnoreCase(value)))
                        {
                            return false;
                        }
                        else if (CommonUtil.isNotNullOrEmpty(value) && !CommonUtil.getString(requestParameters.getValue(rule)).equalsIgnoreCase(value))
                        {
                            result = false;
                        }
                    }
                }
            }
        }

        return result;
    }

    /* This method validates maximum rule for the specified value of entity property. If value is null or is greater than it will send bad request response to the client.*/

    static JsonObject testMaximumRule(String field, String title, Long value, JsonObject requestParameters)
    {
        if (value != null)
        {
            if (requestParameters.getLong(field) > value)
            {
                return new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.API_MAXIMUM_VALUE_RULE, title, value));
            }
        }

        else
        {
            return new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.API_FIELD_REQUIRED, VALUE));
        }

        return null;
    }

    /* This method validates minimum rule for the specified value of entity property. If value is null or is less than it will send bad request response to the client.*/

    static JsonObject testMinimumRule(String field, String title, Long value, JsonObject requestParameters)
    {
        if (value != null)
        {
            if (requestParameters.getLong(field) < value)
            {
                return new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.API_MINIMUM_VALUE_RULE, title, value));
            }
        }

        else
        {
            return new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.API_FIELD_REQUIRED, VALUE));
        }

        return null;
    }

    public static JsonObject testRangeRule(String field, String title, JsonArray value, JsonObject requestParameters)
    {
        if (value != null && !value.isEmpty())
        {
            if (requestParameters.getLong(field) < value.getLong(0) || requestParameters.getLong(field) > value.getLong(1))
            {
                return new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.API_RANGE_VALUE_RULE, title, value));
            }
        }

        else
        {
            return new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.API_FIELD_REQUIRED, VALUE));
        }

        return null;
    }

    public static JsonObject testRangeDivideRule(String field, String title, JsonArray value, JsonObject requestParameters, int divideValue)
    {
        if (value != null && !value.isEmpty())
        {
            if (requestParameters.getLong(field) < value.getLong(0) || requestParameters.getLong(field) > value.getLong(1))
            {
                return new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.API_RANGE_VALUE_RULE, title, value));
            }
            else if (requestParameters.getLong(field) % divideValue > 0)
            {
                return new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.API_RANGE_DIVIDE_VALUE_RULE, title, divideValue));
            }
        }

        else
        {
            return new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.API_FIELD_REQUIRED, VALUE));
        }

        return null;
    }

    /* This method validates that specified value is same as values context. If value is not same as values context then it will send bad request response to the client.*/

    static JsonObject testValueExist(String field, String title, String type, JsonObject requestParameters, JsonArray values)
    {
        var invalidResult = false;

        if (FIELD_TYPE_LIST.equalsIgnoreCase(type))
        {
            for (var parameter : requestParameters.getJsonArray(field))
            {
                if (!values.contains(parameter))
                {
                    invalidResult = true;

                    break;
                }
            }
        }

        else if ((FIELD_TYPE_NUMERIC.equalsIgnoreCase(type) && !values.contains(requestParameters.getInteger(field))) || ("string".equalsIgnoreCase(type) && !values.contains(requestParameters.getString(field))))
        {
            invalidResult = true;
        }

        if (invalidResult)
        {
            return new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.API_INVALID_VALUE_RULE, title));
        }

        return null;
    }

    /* This method validates required rule for the specified property. If value is null or blank then it will send bad request response to the client.*/

    static JsonObject testRequiredRule(String field, String title, String entityType, JsonObject requestParameters)
    {
        if (requestParameters.getValue(field) == null || CommonUtil.getString(requestParameters.getValue(field)).isBlank() || (entityType.equalsIgnoreCase(FIELD_TYPE_LIST) && requestParameters.getJsonArray(field).isEmpty()))
        {
            return new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.API_FIELD_REQUIRED, title));
        }

        return null;
    }

    /* This method validates numeric rule for the specified property. If value is not numeric then it will send bad request response to the client.*/

    static JsonObject testNumericRule(String field, String title, JsonObject requestParameters)
    {
        try
        {
            requestParameters.getLong(field);
        }

        catch (ClassCastException exception)
        {
            return new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.API_INVALID_TYPE_VALUE, title, FIELD_TYPE_NUMERIC));
        }

        return null;
    }

    /* This method validates list rule for the specified property. If value is not json array then it will send bad request response to the client.*/

    static JsonObject testListRule(String field, String title, JsonObject requestParameters)
    {
        try
        {
            if (requestParameters.containsKey(field)) //Verify type only if it exists, if not, then no need to fail validation here.
            {
                new JsonArray(CommonUtil.getString(requestParameters.getValue(field)));
            }
        }

        catch (Exception exception)
        {
            return new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.API_INVALID_TYPE_VALUE, title, FIELD_TYPE_LIST));
        }

        return null;
    }

    /* This method validates list rule for the specified property. If value is not json array then it will send bad request response to the client.*/

    static JsonObject testMapRule(String field, String title, JsonObject requestParameters)
    {
        try
        {
            if (requestParameters.containsKey(field)) //Verify type only if it exists, if not, then no need to fail validation here.
            {
                new JsonObject(CommonUtil.getString(requestParameters.getValue(field)));
            }
        }

        catch (Exception exception)
        {
            return new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.API_INVALID_TYPE_VALUE, title, FIELD_TYPE_MAP));
        }

        return null;
    }

    /* This method validates unique rule for the specified property. Same value must not be inserted in database collection for the specified field,otherwise it will send bad request response to the client..*/

    static Future<JsonObject> testUniqueRule(RoutingContext routingContext, String requestType, String field, String title, JsonObject requestParameters, AbstractConfigStore configStore)
    {
        var promise = Promise.<JsonObject>promise();

        var numeric = true;

        if (requestParameters.getValue(field) instanceof String)
        {
            numeric = false;

            requestParameters.getString(field);
        }

        else
        {
            requestParameters.getInteger(field);
        }

        try
        {
            var item = numeric ? configStore.getItemByValue(field, requestParameters.getInteger(field)) : configStore.getItemByValue(field, requestParameters.getString(field));

            if (item != null && !item.isEmpty())
            {
                /*Return empty json object in success case else with response code and proper message.*/

                var response = new JsonObject();

                if (REQUEST_UPDATE.equalsIgnoreCase(requestType))
                {
                    var id = routingContext.request().getParam(GlobalConstants.ID);

                    if (!CommonUtil.getString(item.getValue(GlobalConstants.ID)).equalsIgnoreCase(id))
                    {
                        response.put(RESPONSE_CODE, SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                                .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, title));
                    }
                }

                else
                {
                    if (requestParameters.getValue(field) instanceof String ? item.getString(field) != null && item.getString(field).equalsIgnoreCase(requestParameters.getString(field))
                            : item.getInteger(field) != null && item.getInteger(field).equals(requestParameters.getInteger(field)))
                    {
                        response.put(RESPONSE_CODE, SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, title));
                    }
                }

                promise.complete(response);
            }

            else
            {
                // means, not found the same object

                promise.complete(new JsonObject());
            }
        }

        catch (Exception ignored) // if item not found then return empty object
        {
            promise.complete(new JsonObject());
        }

        return promise.future();
    }

    public static boolean testNumericRule(String id, RoutingContext routingContext)
    {
        var result = true;

        try
        {
            CommonUtil.getLong(id);
        }

        catch (Exception exception)
        {
            result = false;

            RequestValidator.sendResponse(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, ErrorMessageConstants.API_INVALID_ID));
        }

        return result;
    }

    /**
     * This method is used to remove default parameters from requested body
     *
     * @param requestParameters requested body
     */

    public static void removeDefaultParameters(JsonObject requestParameters)
    {
        requestParameters.remove(ConfigDBConstants.FIELD_TYPE);

        requestParameters.remove(GlobalConstants.ID);

    }

    /**
     * @param startIp start ip
     * @param endIp   end ip
     * @return boolean of valid ip range
     */
    public static boolean validateRange(String startIp, String endIp)
    {
        try
        {
            return CommonUtil.convertIPToLong(InetAddress.getByName(startIp)) <= CommonUtil.convertIPToLong(InetAddress.getByName(endIp));
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
        return false;
    }

    public static void removeSpace(JsonObject requestParameters, RoutingContext routingContext, boolean setBody)
    {
        requestParameters.getMap().forEach((key, value) ->
        {
            if (value instanceof String)
            {
                requestParameters.put(key, CommonUtil.getString(value));
            }
        });

        if (setBody)
        {
            routingContext.setBody(Buffer.buffer(requestParameters.toString()));
        }
    }

    public static void sendResponse(Exception exception, RoutingContext routingContext)
    {
        LOGGER.error(exception);

        RequestValidator.sendResponse(routingContext, new JsonObject().put(RESPONSE_CODE, SC_INTERNAL_SERVER_ERROR)
                .put(STATUS, GlobalConstants.STATUS_FAIL)
                .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage()))
                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace())));
    }

    // this is for query api payload validation
    // it will be used for chart/grid validation both
    public static String testQueryRequest(JsonObject context, Set<String> fields)
    {
        var message = EMPTY_VALUE;

        try
        {
            for (var field : fields)
            {
                if (!context.containsKey(field))
                {
                    message = String.format(API_FIELD_REQUIRED, field);

                    break;
                }
                else
                {
                    if (context.getValue(field) instanceof String)
                    {
                        if (CommonUtil.isNullOrEmpty(context.getString(field)))
                        {
                            message = String.format(API_INVALID_VALUE_RULE, field);

                            break;
                        }
                    }
                    else if (context.getValue(field) instanceof JsonObject)
                    {
                        if (!context.isEmpty())
                        {
                            message = testQueryRequest(context.getJsonObject(field), QUERY_RULES.get(context.getString(VisualizationConstants.VISUALIZATION_CATEGORY)).get(field));

                            if (!message.equalsIgnoreCase(EMPTY_VALUE))
                            {
                                break;
                            }
                        }
                        else
                        {
                            message = String.format(API_INVALID_VALUE_RULE, field);

                            break;
                        }
                    }
                    else if (context.getValue(field) instanceof JsonArray)
                    {
                        if (!context.getJsonArray(field).isEmpty())
                        {
                            for (var index = 0; index < context.getJsonArray(field).size(); index++)
                            {
                                var params = context.getJsonArray(field);

                                if (!params.isEmpty())
                                {
                                    if (params.getValue(index) instanceof JsonObject)
                                    {
                                        message = testQueryRequest(params.getJsonObject(index), QUERY_RULES.get(context.getString(VisualizationConstants.VISUALIZATION_CATEGORY)).get(field));

                                        if (!message.equalsIgnoreCase(EMPTY_VALUE))
                                        {
                                            break;
                                        }
                                    }
                                }
                                else
                                {
                                    message = String.format(API_INVALID_VALUE_RULE, field);

                                    break;
                                }
                            }
                        }
                        else
                        {
                            message = String.format(API_INVALID_VALUE_RULE, field);

                            break;
                        }
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            message = exception.getMessage();
        }

        return message;
    }
}
