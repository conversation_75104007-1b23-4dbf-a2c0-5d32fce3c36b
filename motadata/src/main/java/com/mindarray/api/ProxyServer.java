/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.store.ProxyServerConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;

import static com.mindarray.GlobalConstants.*;

public class ProxyServer extends AbstractAPI
{
    public static final String PROXY_SERVER_HOST = "proxy.server.host";

    public static final String PROXY_SERVER_PORT = "proxy.server.port";

    public static final String PROXY_SERVER_TYPE = "proxy.server.type";

    public static final String PROXY_SERVER_USERNAME = "proxy.server.username";

    public static final String PROXY_SERVER_PASSWORD = "proxy.server.password";

    public static final String PROXY_SERVER_TIME_OUT = "proxy.server.timeout";

    public static final String PROXY_ENABLED = "proxy.enabled";

    public ProxyServer()
    {
        super("proxy-server", ProxyServerConfigStore.getStore(), new Logger(ProxyServer.class, MOTADATA_API, "Proxy Server API"));
    }

    @Override
    public void init(Router router)
    {
        router.get("/" + endpoint).handler(this::get);

        router.put("/" + endpoint).handler(this::create);

        router.delete("/" + endpoint).handler(this::delete);
    }

    @Override
    protected void get(RoutingContext routingContext)
    {
        try
        {
            this.beforeGet().compose(handler ->

                    Future.<JsonObject>future(promise ->
                    {
                        var item = this.configStore.getItem(DEFAULT_ID);

                        if (item != null && !item.isEmpty())
                        {
                            promise.complete(CommonUtil.removeSensitiveFields(item, true));
                        }
                        else
                        {
                            promise.complete(new JsonObject());
                        }
                    }).compose(entity -> this.afterGet(entity, routingContext)));
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        routingContext.body().asJsonObject().put(ID, DEFAULT_ID);

        return super.beforeCreate(routingContext);
    }

    @Override
    protected Future<Void> afterCreate(JsonObject entity, RoutingContext routingContext)
    {
        ProxyServerConfigStore.getStore().updateStore();

        return super.afterCreate(entity, routingContext);
    }

    @Override
    protected Future<JsonObject> beforeDelete(RoutingContext routingContext)
    {
        routingContext.request().params().add(ID, CommonUtil.getString(DEFAULT_ID));

        return super.beforeDelete(routingContext);
    }

    @Override
    protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
    {
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION,
                new JsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DISABLE_PROXY_SERVER.name()));

        return super.afterDelete(entity, routingContext);
    }
}
