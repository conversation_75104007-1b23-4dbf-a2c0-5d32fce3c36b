/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 */
package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CipherUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.MotadataConfigUtil;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.util.Set;
import java.util.zip.ZipFile;

import static com.mindarray.ErrorMessageConstants.RESTORE_FAILED;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.Agent.AGENT_DISABLE;
import static com.mindarray.api.Agent.AGENT_UUID;
import static com.mindarray.api.BackupProfile.BACKUP_PROFILE_NAME;
import static com.mindarray.api.BackupProfile.BACKUP_STORAGE_PROFILE;
import static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_TYPE;
import static com.mindarray.api.RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.db.ConfigDBConstants.BACKUP_FILE;
import static com.mindarray.db.ConfigDBConstants.COLLECTION_BACKUP_SNAPSHOTS;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.manager.MotadataAppManager.HEARTBEAT_STATE;
import static com.mindarray.manager.MotadataAppManager.PATCH_ARTIFACT_FILE;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;
import static org.apache.http.HttpStatus.SC_OK;

public class MotadataApp extends AbstractAPI
{
    public static final Logger LOGGER = new Logger(MotadataApp.class, GlobalConstants.MOTADATA_API, "Motadata App API");

    public static final String ARTIFACT_TYPE = "artifact.type";

    public static final String ARTIFACT_MODE = "artifact.mode";
    public static final String ARTIFACT_ID = "artifact.id";
    public static final String ARTIFACT_VERSION = "artifact.version";
    public static final String ARTIFACT_UPGRADE_STATUS = "artifact.upgrade.status";
    public static final long MAX_VERSION_FILE_SIZE_BYTES = 1073741824;

    public MotadataApp()
    {
        super("motadata-app", ArtifactConfigStore.getStore(), LOGGER, new CipherUtil());
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router, Set.of(REQUEST_GET));

            router.get("/" + endpoint + "/artifacts").handler(this::getArtifacts);

            router.get("/" + endpoint + "/backup-snapshots/:id").handler(this::getBackupSnapshots);

            router.get("/" + endpoint + "/version").handler(routingContext -> send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, MotadataConfigUtil.getVersion())));

            router.post("/" + endpoint + "/restore").handler(this::restore);

            router.post("/" + endpoint + "/:id/upgrade").handler(this::validateAction).handler(this::validateArtifacts).handler(this::upgrade);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    protected void getArtifacts(RoutingContext routingContext)
    {
        try
        {
            var parameters = JsonObject.mapFrom(Json.decodeValue(routingContext.request().params().get(FILTER)));

            var artifactType = parameters.getJsonArray(ARTIFACT_TYPE);

            var artifactMode = parameters.getJsonArray(ARTIFACT_MODE);

            var items = ArtifactConfigStore.getStore().flatItemsByMultipleValues(ARTIFACT_TYPE, artifactType, ARTIFACT_MODE, artifactMode);

            var entities = new JsonArray();

            var uuid = RemoteEventProcessorConfigStore.getStore().getItemByValue(REMOTE_EVENT_PROCESSOR_TYPE, BootstrapType.APP.name()).getString(REMOTE_EVENT_PROCESSOR_UUID);

            for (var index = 0; index < items.size(); index++)
            {
                var item = ArtifactConfigStore.getStore().getItem(items.getLong(index));

                var entity = new JsonObject().mergeIn(item);

                if (item.getString(ARTIFACT_TYPE).equalsIgnoreCase(BootstrapType.APP.name()) || !item.getString(ARTIFACT_ID).equalsIgnoreCase(uuid))
                {
                    if (item.getString(ARTIFACT_TYPE).equalsIgnoreCase(BootstrapType.AGENT.name()))
                    {
                        if (RemoteEventProcessorConfigStore.getStore().getItemByValue(REMOTE_EVENT_PROCESSOR_UUID, item.getString(ARTIFACT_ID)) == null)
                        {
                            if (AgentConfigStore.getStore().existItem(item.getLong(ID)))
                            {
                                entity.mergeIn(AgentConfigStore.getStore().getItem(item.getLong(ID)));

                                var object = ObjectConfigStore.getStore().getItemByAgentId(item.getLong(ID));

                                if (object != null)
                                {
                                    object.put(ENTITY_ID, object.getLong(ID));

                                    APIUtil.removeDefaultParameters(object);

                                    entity.mergeIn(object);
                                }

                                var progress = AgentCacheStore.getStore().getProgress(item.getLong(ID));

                                if (progress != null)
                                {
                                    entity.put(PROGRESS, progress);
                                }

                                entities.add(entity);
                            }
                            else
                            {
                                Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.DELETE_ARTIFACT.name()).put(ID, item.getLong(ID)).put(MotadataApp.ARTIFACT_TYPE, item.getString(ARTIFACT_TYPE)));
                            }
                        }
                    }
                    else
                    {
                        if (RemoteEventProcessorConfigStore.getStore().existItem(item.getLong(ID)))
                        {
                            entity.mergeIn(RemoteEventProcessorConfigStore.getStore().getItem(item.getLong(ID)));

                            var progress = RemoteEventProcessorCacheStore.getStore().getProgress(item.getLong(ID));

                            if (progress != null)
                            {
                                entity.put(PROGRESS, progress);
                            }

                            entities.add(entity);
                        }
                        else
                        {
                            Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION, new JsonObject().put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.DELETE_ARTIFACT.name()).put(ID, item.getLong(ID)).put(MotadataApp.ARTIFACT_TYPE, item.getString(ARTIFACT_TYPE)));
                        }
                    }
                }
            }

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, entities));
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void upgrade(RoutingContext routingContext)
    {
        try
        {
            var version = getVersion(routingContext.body().asJsonObject().getString(PATCH_ARTIFACT_FILE));

            var id = CommonUtil.getLong(routingContext.request().getParam(ID));

            if (version != null && !version.isEmpty())
            {
                var artifact = ArtifactConfigStore.getStore().getItem(id);

                if (artifact != null && !artifact.isEmpty())
                {
                    if (artifact.getString(ARTIFACT_TYPE).equalsIgnoreCase(BootstrapType.AGENT.name()))
                    {
                        var item = AgentConfigStore.getStore().getItem(id);

                        if (item != null && !item.isEmpty())
                        {
                            if (AgentCacheStore.getStore().getItem(item.getLong(ID)).containsKey(HEARTBEAT_STATE) && !AgentCacheStore.getStore().getItem(item.getLong(ID)).getString(HEARTBEAT_STATE).equalsIgnoreCase(NMSConstants.STATE_NOT_REACHABLE))
                            {
                                if (updateAvailable(routingContext.body().asJsonObject().getString(PATCH_ARTIFACT_FILE)))
                                {
                                    LOGGER.info(String.format("updating agent %s", id));

                                    publishEvent(new JsonObject().put(EventBusConstants.EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC).put(EVENT_TYPE, EventBusConstants.EVENT_AGENT_UPGRADE)
                                            .put(ID, id).put(AGENT_UUID, item.getString(AGENT_UUID)).put(AGENT_DISABLE, YES)
                                            .put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                                            .put(USER_NAME, routingContext.user().principal().getString(USER_NAME))
                                            .put(PATCH_ARTIFACT_FILE, routingContext.body().asJsonObject().getString(PATCH_ARTIFACT_FILE)));

                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_OK).put(STATUS, STATUS_SUCCEED).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(RESULT, String.format(InfoMessageConstants.UPGRADE_REQUESTED, Entity.AGENT.getName())));
                                }
                                else
                                {
                                    LOGGER.info(String.format("updating manager of %s", CommonUtil.getString(id)));

                                    Bootstrap.vertx().eventBus().send(EVENT_PUBLICATION_MOTADATA_MANAGER, new JsonObject()
                                            .put(EventBusConstants.EVENT_TOPIC, MOTADATA_MANAGER_TOPIC)
                                            .put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                                            .put(USER_NAME, routingContext.user().principal().getString(USER_NAME))
                                            .put(EVENT_TYPE, EVENT_MANAGER_UPGRADE)
                                            .put(PATCH_ARTIFACT_FILE, routingContext.body().asJsonObject().getString(PATCH_ARTIFACT_FILE))
                                            .put(AGENT_UUID, artifact.getString(ARTIFACT_ID))
                                            .put(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.MANAGER.name()));

                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_OK).put(STATUS, STATUS_SUCCEED).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(RESULT, String.format(InfoMessageConstants.UPGRADE_REQUESTED, Entity.AGENT.getName())));
                                }
                            }
                            else
                            {
                                LOGGER.info(String.format(ErrorMessageConstants.AGENT_NOT_REACHABLE, CommonUtil.getString(id)));

                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_AGENT_BUSY).put(STATUS, GlobalConstants.STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                                        .put(MESSAGE, String.format(ErrorMessageConstants.AGENT_NOT_REACHABLE, ObjectConfigStore.getStore().getAgentObjectName(id))));
                            }
                        }
                        else
                        {
                            LOGGER.info(String.format(ErrorMessageConstants.AGENT_NOT_FOUND, CommonUtil.getString(id)));

                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(STATUS, GlobalConstants.STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                                    .put(MESSAGE, String.format(ErrorMessageConstants.AGENT_NOT_FOUND, CommonUtil.getString(id))));
                        }
                    }
                    else
                    {
                        var item = RemoteEventProcessorConfigStore.getStore().getItem(id);

                        if (item != null && !item.isEmpty())
                        {
                            if (RemoteEventProcessorCacheStore.getStore().getItem(item.getLong(ID)).containsKey(HEARTBEAT_STATE) && !RemoteEventProcessorCacheStore.getStore().getItem(item.getLong(ID)).getString(HEARTBEAT_STATE).equalsIgnoreCase(NMSConstants.STATE_NOT_REACHABLE))
                            {
                                if (updateAvailable(routingContext.body().asJsonObject().getString(PATCH_ARTIFACT_FILE)))
                                {
                                    LOGGER.info(String.format("updating remote event processor %s", id));

                                    var event = new JsonObject().put(EventBusConstants.EVENT_TOPIC, MOTADATA_MANAGER_TOPIC).put(ID, id)
                                            .put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                                            .put(USER_NAME, routingContext.user().principal().getString(USER_NAME))
                                            .put(PATCH_ARTIFACT_FILE, routingContext.body().asJsonObject().getString(PATCH_ARTIFACT_FILE));

                                    if (item.getString(REMOTE_EVENT_PROCESSOR_TYPE).equalsIgnoreCase(BootstrapType.APP.name()) && (item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE).equalsIgnoreCase(InstallationMode.STANDALONE.name()) || item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE).equalsIgnoreCase(InstallationMode.PRIMARY.name())))
                                    {
                                        Bootstrap.vertx().eventBus().send(EVENT_PUBLICATION_MOTADATA_MANAGER, event.put(EVENT_TYPE, EVENT_MASTER_UPGRADE).put(REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId()).put(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.APP.name())
                                                .put(INSTALLATION_MODE, item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_INSTALLATION_MODE)));
                                    }
                                    else
                                    {
                                        publishEvent(event.put(EVENT_TYPE, EVENT_REMOTE_PROCESSOR_UPGRADE).put(DISABLED, YES).put(REMOTE_EVENT_PROCESSOR_UUID, item.getString(REMOTE_EVENT_PROCESSOR_UUID)).put(SYSTEM_BOOTSTRAP_TYPE, item.getString(REMOTE_EVENT_PROCESSOR_TYPE)));
                                    }

                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_OK).put(STATUS, STATUS_SUCCEED).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(RESULT, String.format(InfoMessageConstants.UPGRADE_REQUESTED, Entity.REMOTE_EVENT_PROCESSOR.getName())));
                                }
                                else
                                {
                                    LOGGER.info(String.format("updating manager of %s", CommonUtil.getString(id)));

                                    Bootstrap.vertx().eventBus().send(EVENT_PUBLICATION_MOTADATA_MANAGER, new JsonObject()
                                            .put(EventBusConstants.EVENT_TOPIC, MOTADATA_MANAGER_TOPIC)
                                            .put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                                            .put(USER_NAME, routingContext.user().principal().getString(USER_NAME))
                                            .put(EVENT_TYPE, EVENT_MANAGER_UPGRADE)
                                            .put(PATCH_ARTIFACT_FILE, routingContext.body().asJsonObject().getString(PATCH_ARTIFACT_FILE))
                                            .put(REMOTE_EVENT_PROCESSOR_UUID, artifact.getString(ARTIFACT_ID))
                                            .put(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.MANAGER.name()));

                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_OK).put(STATUS, STATUS_SUCCEED).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID)).put(RESULT, String.format(InfoMessageConstants.UPGRADE_REQUESTED, Entity.REMOTE_EVENT_PROCESSOR.getName())));
                                }
                            }
                            else
                            {
                                LOGGER.debug(String.format(ErrorMessageConstants.REMOTE_EVENT_PROCESSOR_NOT_REACHABLE, CommonUtil.getString(id)));

                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_REMOTE_EVENT_PROCESSOR_BUSY).put(STATUS, GlobalConstants.STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                                        .put(MESSAGE, String.format(ErrorMessageConstants.REMOTE_EVENT_PROCESSOR_NOT_REACHABLE, RemoteEventProcessorConfigStore.getStore().getItem(id).getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_HOST))));
                            }
                        }
                        else
                        {
                            LOGGER.debug(String.format(ErrorMessageConstants.REMOTE_EVENT_PROCESSOR_NOT_FOUND, CommonUtil.getString(id)));

                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(STATUS, GlobalConstants.STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(MESSAGE, String.format(ErrorMessageConstants.REMOTE_EVENT_PROCESSOR_NOT_FOUND, CommonUtil.getString(id))));
                        }
                    }
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(STATUS, GlobalConstants.STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(MESSAGE, String.format(ErrorMessageConstants.UPGRADE_FAILED_ARTIFACTS_NOT_FOUND, routingContext.body().asJsonObject().getString(ARTIFACT_TYPE).equalsIgnoreCase(BootstrapType.AGENT.name()) ? Entity.AGENT.getName() : Entity.REMOTE_EVENT_PROCESSOR.getName(), CommonUtil.getString(id))));
                }
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(STATUS, GlobalConstants.STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                        .put(MESSAGE, String.format(ErrorMessageConstants.UPGRADE_FAILED_VERSION_NOT_FOUND, routingContext.body().asJsonObject().getString(ARTIFACT_TYPE).equalsIgnoreCase(BootstrapType.AGENT.name()) ? Entity.AGENT.getName() : Entity.REMOTE_EVENT_PROCESSOR.getName(), CommonUtil.getString(id))));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private String getVersion(String fileName)
    {
        String version = null;

        try (var file = new ZipFile(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.UPLOADS + GlobalConstants.PATH_SEPARATOR + fileName))
        {
            var entry = file.getEntry(VERSION_FILE);

            if (entry != null && entry.getSize() < MAX_VERSION_FILE_SIZE_BYTES)
            {
                var reader = new BufferedReader(new InputStreamReader(file.getInputStream(entry)));

                var line = reader.readLine();

                if (CommonUtil.isNotNullOrEmpty(line))
                {
                    version = line.trim().split(NEW_LINE)[0].trim();
                }

                reader.close();
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return version;
    }

    private void publishEvent(JsonObject event)
    {
        event.put(EVENT_ID, CommonUtil.newEventId());

        if (CommonUtil.isNotNullOrEmpty(event.getString(DISABLED))) // prevent user to perform any action
        {
            RemoteEventProcessorCacheStore.getStore().updateDuration(event.getLong(ID), event.getString(DISABLED));

            EventBusConstants.publish(UI_NOTIFICATION_REMOTE_PROCESSOR, new JsonObject().put(EVENT_TYPE, EVENT_REMOTE_EVENT_PROCESSOR_ACTION).put(ID, event.getLong(ID))
                    .put(REMOTE_EVENT_PROCESSOR_UUID, event.getString(REMOTE_EVENT_PROCESSOR_UUID)).put(DISABLED, event.getString(DISABLED)));
        }

        if (CommonUtil.isNotNullOrEmpty(event.getString(AGENT_DISABLE))) //improvements - 3636 -> prevent user to perform any action
        {
            AgentCacheStore.getStore().updateItem(event.getLong(ID), AgentCacheStore.getStore().getItem(event.getLong(ID)).put(AGENT_DISABLE, event.getString(AGENT_DISABLE)));

            AgentCacheStore.getStore().updateDuration(event.getLong(ID), event.getString(AGENT_DISABLE));

            EventBusConstants.publish(UI_NOTIFICATION_AGENT, new JsonObject().put(EVENT_TYPE, EVENT_AGENT_ACTION).put(ID, event.getLong(ID))
                    .put(AGENT_UUID, event.getString(AGENT_UUID)).put(AGENT_DISABLE, event.getString(AGENT_DISABLE)));
        }

        var context = new JsonObject().put(EventBusConstants.EVENT_CONTEXT, event)
                .put(EventBusConstants.EVENT_ID, event.getLong(EVENT_ID))
                .put(USER_NAME, event.remove(USER_NAME))
                .put(EventBusConstants.EVENT_TYPE, event.getString(EVENT_TYPE));

        if (event.getString(UI_EVENT_UUID) != null)
        {
            context.put(UI_EVENT_UUID, event.getString(UI_EVENT_UUID));
        }

        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_ADD, context.put(EVENT_COPY_REQUIRED, false));

        Bootstrap.vertx().eventBus().send(EVENT_PUBLICATION_MOTADATA_MANAGER, event.put(EVENT_COPY_REQUIRED, false));
    }

    private void validateArtifacts(RoutingContext routingContext)
    {
        try
        {
            var body = routingContext.body().asJsonObject();

            if (body != null && !body.isEmpty())
            {
                var fileName = body.getString(PATCH_ARTIFACT_FILE);

                if (CommonUtil.isNotNullOrEmpty(fileName))
                {
                    Bootstrap.vertx().fileSystem().exists(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.UPLOADS + GlobalConstants.PATH_SEPARATOR + fileName, result ->
                    {
                        if (result.succeeded())
                        {
                            routingContext.next();
                        }

                        else
                        {
                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND).put(STATUS, GlobalConstants.STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                                    .put(MESSAGE, String.format(ErrorMessageConstants.UPGRADE_FAILED_ARTIFACTS_NOT_FOUND, Entity.AGENT.getName(), fileName)));
                        }
                    });

                }
                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(STATUS, GlobalConstants.STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                            .put(MESSAGE, String.format(ErrorMessageConstants.UPGRADE_FAILED_INVALID_ARTIFACT_NAME, Entity.AGENT.getName())));
                }
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(STATUS, GlobalConstants.STATUS_FAIL).put(SESSION_ID, routingContext.user().principal().getString(SESSION_ID))
                        .put(MESSAGE, ErrorMessageConstants.API_REQUEST_BODY_MISSING));
            }
        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void validateAction(RoutingContext routingContext)
    {
        if (routingContext.body().asJsonObject().getString(ARTIFACT_TYPE).equalsIgnoreCase(BootstrapType.AGENT.name()))
        {
            var item = AgentCacheStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID)));

            if (item.containsKey(AGENT_DISABLE) && item.getString(AGENT_DISABLE).equalsIgnoreCase(YES)) //if any operation is running on agent system will not allow user to perform operation
            {
                this.send(routingContext, new JsonObject().put(ID, CommonUtil.getLong(routingContext.request().getParam(ID)))
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_AGENT_BUSY)
                        .put(STATUS, STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(MESSAGE, String.format(ErrorMessageConstants.AGENT_BUSY,
                                ObjectConfigStore.getStore().getAgentObjectName(CommonUtil.getLong(routingContext.request().getParam(ID))))));
            }

            else
            {
                routingContext.next();
            }
        }
        else
        {
            var item = RemoteEventProcessorCacheStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID)));

            if (item == null)
            {
                this.send(routingContext, new JsonObject().put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND)
                        .put(STATUS, STATUS_FAIL).put(RESPONSE_CODE, SC_BAD_REQUEST).put(MESSAGE, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, Entity.REMOTE_EVENT_PROCESSOR.getName())));
            }
            else if (item.containsKey(DISABLED) && item.getString(DISABLED).equalsIgnoreCase(YES)) //if any operation is running on remote event processor system will not allow user to perform operation
            {
                this.send(routingContext, new JsonObject().put(ID, CommonUtil.getLong(routingContext.request().getParam(ID)))
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_REMOTE_EVENT_PROCESSOR_BUSY)
                        .put(STATUS, STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(MESSAGE, String.format(ErrorMessageConstants.REMOTE_EVENT_PROCESSOR_BUSY,
                                item.getString(RemoteEventProcessor.REMOTE_EVENT_PROCESSOR_UUID))));
            }
            else
            {
                routingContext.next();
            }
        }
    }

    private void getBackupSnapshots(RoutingContext routingContext)
    {
        try
        {
            var snapshots = new JsonArray();

            var item = BackupProfileConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID)));

            Bootstrap.configDBService().getAll(COLLECTION_BACKUP_SNAPSHOTS + item.getLong(ID), result ->
            {
                if (result.succeeded())
                {
                    var filter = !routingContext.request().getParam(FILTER, EMPTY_VALUE).isEmpty() ? JsonObject.mapFrom(Json.decodeValue(routingContext.request().getParam(FILTER))) : null;

                    var items = result.result();

                    for (var index = 0; index < items.size(); index++)
                    {
                        var snapshot = items.getJsonObject(index);

                        var tokens = snapshot.getString(GlobalConstants.SRC_FILE_PATH).split(PATH_SEPARATOR);

                        snapshot.put(GlobalConstants.SRC_FILE_PATH, tokens[tokens.length - 1]);

                        snapshot.put(StorageProfile.STORAGE_PROFILE_PROTOCOL, StorageProfileConfigStore.getStore().getItem(snapshot.getLong(BACKUP_STORAGE_PROFILE)).getString(StorageProfile.STORAGE_PROFILE_PROTOCOL));

                        snapshot.put(BACKUP_PROFILE_NAME, item.getString(BACKUP_PROFILE_NAME));

                        if (filter == null || snapshot.getString(STATUS).equalsIgnoreCase(filter.getString(STATUS)))
                        {
                            snapshots.add(snapshot);
                        }
                    }

                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(RESULT, snapshots));
                }
                else
                {
                    LOGGER.error(result.cause());

                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL).put(MESSAGE, result.cause()).put(RESULT, snapshots));
                }
            });
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void restore(RoutingContext routingContext)
    {
        try (var zipFile = new ZipFile(CURRENT_DIR + PATH_SEPARATOR + UPLOADS + PATH_SEPARATOR + routingContext.body().asJsonObject().getString(BACKUP_FILE)))
        {
            if (zipFile.size() > 0)
            {
                var versionEntry = zipFile.getEntry(VERSION_FILE);

                if (versionEntry != null && versionEntry.getSize() > 0)
                {
                    LOGGER.info(String.format("%s files available in zip", zipFile.size()));

                    if (UserConfigStore.getStore().getItem(DEFAULT_ID).getString(User.USER_PASSWORD).equalsIgnoreCase(cipherUtil.encrypt(routingContext.body().asJsonObject().getString(User.USER_PASSWORD))))
                    {
                        Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_PUBLICATION_MOTADATA_MANAGER, new JsonObject()
                                .put(BACKUP_FILE, routingContext.body().asJsonObject().getString(BACKUP_FILE))
                                .put(EventBusConstants.EVENT_TYPE, EventBusConstants.EVENT_DATABASE_RESTORE)
                                .put(EVENT_TOPIC, EventBusConstants.MOTADATA_MANAGER_TOPIC)
                                .put(VERSION, MotadataConfigUtil.getVersion()).put(REMOTE_EVENT_PROCESSOR_UUID, Bootstrap.getRegistrationId()).put(EVENT_COPY_REQUIRED, false)
                                .put(SYSTEM_BOOTSTRAP_TYPE, BootstrapType.APP.name())
                                .put(INSTALLATION_MODE, Bootstrap.getInstallationMode()));

                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(MESSAGE, "Restoration will be started"));
                    }
                    else
                    {
                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL).put(MESSAGE, String.format(RESTORE_FAILED, "Incorrect Password")));
                    }
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL).put(MESSAGE, String.format(RESTORE_FAILED, "VERSION file is not available")));
                }
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL).put(MESSAGE, String.format(RESTORE_FAILED, "Invalid ZIP File")));
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            APIUtil.sendResponse(exception, routingContext);
        }
    }

    // if upgrade.me file present in the PATCH_ARTIFACT_FILE then it is manager upgrade request
    private boolean updateAvailable(String fileName)
    {
        var result = false;

        try (var file = new ZipFile(GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.UPLOADS + GlobalConstants.PATH_SEPARATOR + fileName))
        {
            if (file.getEntry("upgrade.me") != null)
            {
                result = true;
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return !result;
    }
}
