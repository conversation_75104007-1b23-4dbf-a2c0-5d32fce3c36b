/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 */
package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.ErrorCodes;
import com.mindarray.ErrorMessageConstants;
import com.mindarray.GlobalConstants;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.integration.IntegrationConstants;
import com.mindarray.integration.ServiceOpsIntegration;
import com.mindarray.policy.PolicyEngineConstants;
import com.mindarray.store.IntegrationCacheStore;
import com.mindarray.store.IntegrationConfigStore;
import com.mindarray.store.IntegrationProfileConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.Set;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.eventbus.EventBusConstants.ChangeNotificationType;
import static com.mindarray.eventbus.EventBusConstants.EVENT_METRIC_POLICY_CLEAR;

public class Integration extends AbstractAPI
{

    public static final String INTEGRATION_TYPE = "integration.type";

    public static final String INTEGRATION_CONTEXT = "integration.context";

    public static final String INTEGRATION_ATTRIBUTES = "integration.attributes";

    public static final String INTEGRATION_ACCESS_TOKEN = "integration.access.token";

    public static final String ALERT_REOCCURRENCE_ACTION = "alert.reoccurrence.action";

    public static final String ALERT_REOCCURRENCE_STATUS = "alert.reoccurrence.status";

    public static final String AUTO_SYNC = "auto.sync";

    public static final String SYNC_INTERVAL = "sync.interval";

    // service now related constants
    public static final String REQUEST_TYPE = "request.type";

    private static final Logger LOGGER = new Logger(Integration.class, MOTADATA_API, "Integration API");

    public Integration()
    {
        super("integrations", IntegrationConfigStore.getStore(), new Logger(Integration.class, MOTADATA_API, "Integration API"));
    }

    @Override
    public void init(Router router)
    {
        super.init(router, Set.of(REQUEST_GET));

        router.get("/" + endpoint).handler(this::getAll);

        router.post("/" + endpoint + "/clear-alert").handler(this::clearAlert);
    }

    private void clearAlert(RoutingContext routingContext)
    {
        var params = routingContext.body().asJsonObject();

        var id = params.getString(ID);

        var name = params.getString("name");

        var integrationType = IntegrationCacheStore.getStore().getIntegration(name + HASH_SEPARATOR + id);

        if (IntegrationConstants.IntegrationType.SERVICEOPS.getName().equalsIgnoreCase(integrationType) && (ServiceOpsIntegration.STATUS_RESOLVED.equalsIgnoreCase(params.getString(STATUS)) || ServiceOpsIntegration.STATUS_CLOSED.equalsIgnoreCase(params.getString(STATUS))))
        {
            //key: integrationType + policy.id + object.id + metric + instance
            var policyKey = IntegrationCacheStore.getStore().getPolicy(name + HASH_SEPARATOR + id);

            if (!policyKey.equalsIgnoreCase(EMPTY_VALUE))
            {
                var tokens = policyKey.split(SEPARATOR_WITH_ESCAPE);

                LOGGER.info(String.format("Manual clear from integration: %s, ack id: %s, policy key: %s", integrationType, id, policyKey));

                IntegrationCacheStore.getStore().remove(IntegrationConstants.IntegrationType.SERVICEOPS.getName(), policyKey);

                if (tokens.length == 5) //scaler metric policy
                {
                    Bootstrap.vertx().eventBus().publish(EVENT_METRIC_POLICY_CLEAR, new JsonObject().put("action.type", "clear").put(PolicyEngineConstants.POLICY_ID, CommonUtil.getLong(tokens[1])).put("alert.type", 0).put(ENTITY_ID, CommonUtil.getLong(tokens[2])).put(METRIC, tokens[4]));
                }
                else //instance metric policy
                {
                    Bootstrap.vertx().eventBus().publish(EVENT_METRIC_POLICY_CLEAR, new JsonObject().put("action.type", "clear").put(PolicyEngineConstants.POLICY_ID, CommonUtil.getLong(tokens[1])).put("alert.type", 0).put(ENTITY_ID, CommonUtil.getLong(tokens[2])).put(METRIC, tokens[4]).put(INSTANCE, tokens[5]));
                }

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED));
            }
            else
            {
                if (CommonUtil.traceEnabled())
                {

                    LOGGER.trace(String.format("Integration: %s, Ack:%s does not have track in AIOps.", name, integrationType));
                }
            }
        }
    }

    @Override
    protected void getAll(RoutingContext routingContext)
    {
        try
        {
            var requestParameters = JsonObject.mapFrom(Json.decodeValue(routingContext.request().getParam(FILTER)));

            if (requestParameters.containsKey(INTEGRATION_TYPE))
            {
                var item = CommonUtil.removeSensitiveFields(IntegrationConfigStore.getStore().getItemByValue(INTEGRATION_TYPE, requestParameters.getString(INTEGRATION_TYPE)), true);

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(ID, item.getLong(ID)).put(GlobalConstants.RESULT, item));
            }
            else if (requestParameters.containsKey("active") && requestParameters.getString("active").equalsIgnoreCase(YES))
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, IntegrationConfigStore.getStore().getActiveIntegrations()));
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR).put(GlobalConstants.STATUS, STATUS_FAIL).put(MESSAGE, ErrorMessageConstants.INTERNAL_ERROR));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected Future<JsonObject> afterUpdate(JsonObject entity, RoutingContext routingContext)
    {
        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, routingContext.body().asJsonObject().put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_INTEGRATION.name()));

        try
        {
            var context = routingContext.body().asJsonObject();

            if (context.containsKey(Integration.INTEGRATION_TYPE)
                    && context.getString(Integration.INTEGRATION_TYPE, EMPTY_VALUE).equalsIgnoreCase(IntegrationConstants.IntegrationType.SERVICENOW.getName())
                    && IntegrationProfileConfigStore.getStore().getItem(CommonUtil.getLong(DEFAULT_ID)) == null)
            {
                // add default servicenow integration profile if the servicenow integration is updated
                var item = new JsonObject("{ \"integration.profile.name\": \"ServiceNow Event\", \"integration.category\": \"Incident\", \"integration.profile.description\": \"This is the OOTB integration profile for servicenow event\", \"integration.profile.context\": { \"auto.close.ticket.status\": \"no\" }, \"integration\": **************, \"id\": **************, \"_type\": \"0\" }");

                Bootstrap.configDBService().save(ConfigDBConstants.COLLECTION_INTEGRATION_PROFILE, item, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, result ->
                {
                    if (result.succeeded())
                    {
                        IntegrationProfileConfigStore.getStore().addItem(result.result());

                        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, item.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_INTEGRATION_PROFILE.name()));
                    }
                    else
                    {
                        LOGGER.error(result.cause());
                    }
                });
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return super.afterUpdate(entity, routingContext);
    }

}
