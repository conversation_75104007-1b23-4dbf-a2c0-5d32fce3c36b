/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*  Change Logs:
 *  Date			Author		    Notes
 *  23-Apr-2025		sankalp		    MOTADATA-4883 : passed user id in getEntityCountPreHook for data security
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.AbstractConfigStore;
import com.mindarray.util.CipherUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.http.HttpMethod;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.Set;

import static com.mindarray.ErrorMessageConstants.INTERNAL_SERVER_EXCEPTION;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.User.USER_ID;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.EVENT_AUDIT;

public abstract class AbstractAPI
{
    protected final String endpoint;
    protected final AbstractConfigStore configStore;
    private final Logger logger;
    protected CipherUtil cipherUtil;


    protected AbstractAPI(String endpoint, AbstractConfigStore configStore, Logger logger)
    {
        this.endpoint = endpoint;

        this.configStore = configStore;

        this.logger = logger;
    }

    protected AbstractAPI(String endpoint, AbstractConfigStore configStore, Logger logger, CipherUtil cipherUtil)
    {
        this.endpoint = endpoint;

        this.configStore = configStore;

        this.logger = logger;

        this.cipherUtil = cipherUtil;
    }


    public void init(Router router)
    {
        try
        {
            router.get("/" + endpoint).handler(this::getAll);

            router.get("/" + endpoint + "/:id").handler(this::validate).handler(this::get);

            router.post("/" + endpoint).handler(this::validate).handler(this::create);

            router.put("/" + endpoint + "/:id").handler(this::validate).handler(this::update);

            router.delete("/" + endpoint + "/:id").handler(this::validate).handler(this::delete);

            router.delete("/" + endpoint).handler(this::deleteAll);

        }

        catch (Exception exception)
        {
            logger.error(exception);
        }

    }

    public void init(Router router, Set<String> filters)
    {
        try
        {
            if (!filters.contains(REQUEST_GET))
            {

                router.get("/" + endpoint).handler(this::getAll);

                router.get("/" + endpoint + "/:id").handler(this::validate).handler(this::get);

            }

            if (!filters.contains(REQUEST_CREATE))
            {

                router.post("/" + endpoint).handler(this::validate).handler(this::create);

            }

            if (!filters.contains(REQUEST_UPDATE))
            {

                router.put("/" + endpoint + "/:id").handler(this::validate).handler(this::update);


            }

            if (!filters.contains(REQUEST_DELETE))
            {

                router.delete("/" + endpoint + "/:id").handler(this::validate).handler(this::delete);

                router.delete("/" + endpoint).handler(this::deleteAll);

            }


        }

        catch (Exception exception)
        {
            logger.error(exception);
        }

    }

    /**
     * @param routingContext This RoutingContext may be use for further computation in overriding classes
     */
    protected Future<Void> beforeGetAll(RoutingContext routingContext)
    {
        return Future.succeededFuture();
    }

    protected Future<Void> afterGetAll(JsonArray entities, RoutingContext routingContext)
    {
        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, entities));

        return Future.succeededFuture();
    }

    /**
     *
     */
    protected Future<Void> beforeGet()
    {
        return Future.succeededFuture();
    }

    protected Future<Void> afterGet(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, entity));

        return Future.succeededFuture();
    }

    /**
     * @param routingContext This RoutingContext may be use for further computation in overriding classes
     */
    protected Future<JsonObject> beforeDelete(RoutingContext routingContext)
    {
        return Future.succeededFuture();
    }

    protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
    {
        return handleResponse(routingContext, entity);
    }

    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        return Future.succeededFuture(routingContext.body().asJsonObject());
    }

    protected Future<Void> afterCreate(JsonObject entity, RoutingContext routingContext)
    {
        return handleResponse(routingContext, entity);
    }

    protected Future<JsonObject> beforeUpdate(RoutingContext routingContext)
    {
        return Future.succeededFuture(routingContext.body().asJsonObject());
    }

    protected Future<JsonObject> afterUpdate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        return Future.succeededFuture();
    }

    /**
     * @param routingContext This RoutingContext may be used for further computation in overriding classes
     */
    protected Future<Void> beforeDeleteAll(RoutingContext routingContext)
    {
        return Future.succeededFuture();
    }

    protected Future<Void> afterDeleteAll(JsonObject response, RoutingContext routingContext)
    {
        return handleResponse(routingContext, response);
    }

    protected void send(RoutingContext routingContext, JsonObject response)
    {
        routingContext.response().putHeader(CONTENT_TYPE, CONTENT_TYPE_APPLICATION_JSON).setStatusCode(response.getInteger(RESPONSE_CODE)).end(response.encodePrettily());
    }

    private Future<Void> handleResponse(RoutingContext routingContext, JsonObject response)
    {
        this.send(routingContext, response);

        return Future.succeededFuture();
    }

    protected Future<JsonObject> getEntityCountPreHook(JsonObject response)
    {
        return Future.succeededFuture(response);
    }

    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {
        return Future.succeededFuture(response);
    }

    protected void validate(RoutingContext routingContext)
    {
        try
        {
            var httpMethod = HttpMethod.valueOf(routingContext.request().method().name());

            if (HttpMethod.GET.equals(httpMethod) || HttpMethod.DELETE.equals(httpMethod))
            {
                /* DELETE request for archive single entity or all entity.*/

                /*For single entity, id which is numeric value so validation for numeric value requires. */

                /*For all entity, validation requires for jsonArray of multiple numeric values.*/

                /* GET request for retrieving single entity or all entity. For single entity, id which is numeric value so validation for numeric value requires.*/

                if (routingContext.request().params().contains(ID) && APIUtil.testNumericRule(routingContext.request().params().get(ID), routingContext))
                {
                    /*if validation passer than we have to move to next handler.*/

                    routingContext.next();
                }
            }
            else if (HttpMethod.PUT.equals(httpMethod))
            {
                /* PUT request for updating single entity. For single entity, id which is numeric value so validation for numeric value requires.*/

                /*Need to validate every parameter passed for that entity.*/

                if (APIUtil.testNumericRule(routingContext.request().params().get(ID), routingContext) && RequestValidator.validateRequestBody(routingContext) && !RequestValidator.validateRequest(endpoint, REQUEST_UPDATE, routingContext, routingContext.body().asJsonObject(), true, configStore))
                {
                    /*if validation passer than we have to move to next handler.*/

                    routingContext.next();
                }
            }
            else if (HttpMethod.POST.equals(httpMethod))
            {
                /* POST request for Creating single entity.*/

                /*Need to validate every parameter passed for that entity.*/

                if (RequestValidator.validateRequestBody(routingContext) && !RequestValidator.validateRequest(endpoint, REQUEST_CREATE, routingContext, routingContext.body().asJsonObject(), true, configStore))
                {
                    /*if validation passer than we have to move to next handler.*/

                    routingContext.next();
                }
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    protected void getReferences(RoutingContext routingContext)
    {
        try
        {
            this.configStore.getReferenceEntities(CommonUtil.getLong(routingContext.request().getParam(ID))).onComplete(result ->
            {
                if (result.succeeded())
                {
                    this.getReferencesPreHook(routingContext, result.result()).onComplete(asyncResult ->
                    {
                        if (asyncResult.succeeded())
                        {
                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, asyncResult.result()));
                        }
                        else
                        {
                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, asyncResult.cause().getMessage()))
                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                    .put(ERROR, CommonUtil.formatStackTrace(asyncResult.cause().getStackTrace())));
                        }
                    });
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                            .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, result.cause().getMessage()))
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                            .put(ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));
                }
            });
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    protected void getAll(RoutingContext routingContext)
    {
        try
        {
            if (routingContext.request().params().contains(FILTER))
            {
                var requestParameters = JsonObject.mapFrom(Json.decodeValue(routingContext.request().getParam(FILTER)));

                if (requestParameters != null && requestParameters.containsKey(KEY) && requestParameters.containsKey(VALUE))
                {
                    filter(routingContext, requestParameters);
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                            .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                            .put(GlobalConstants.MESSAGE, ErrorMessageConstants.API_INVALID_INPUT_PARAMETERS));
                }
            }

            else
            {
                this.beforeGetAll(routingContext).compose(handler ->

                        Future.<JsonArray>future(promise ->
                                this.configStore.getReferenceCountsByItem().onComplete(result ->
                                {
                                    if (result.succeeded())
                                    {
                                        this.getEntityCountPreHook(result.result().put(USER_ID, routingContext.user().principal().getLong(ID))).onComplete(asyncResult ->
                                        {
                                            if (asyncResult.succeeded())
                                            {
                                                var items = this.configStore.getItems();

                                                var entities = new JsonArray();

                                                for (var index = 0; index < items.size(); index++)
                                                {
                                                    var item = CommonUtil.removeSensitiveFields(items.getJsonObject(index), true);

                                                    if (asyncResult.result() != null && asyncResult.result().containsKey(CommonUtil.getString(item.getLong(ID))))
                                                    {
                                                        item.put(ENTITY_PROPERTY_COUNT, asyncResult.result().getInteger(CommonUtil.getString(item.getLong(ID))));
                                                    }

                                                    entities.add(item);
                                                }

                                                promise.complete(entities);
                                            }

                                            else
                                            {
                                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                                        .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, asyncResult.cause().getMessage()))
                                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                        .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(asyncResult.cause().getStackTrace())));

                                                promise.fail(asyncResult.cause());
                                            }
                                        });
                                    }

                                    else
                                    {
                                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                                .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, result.cause().getMessage()))
                                                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));

                                        promise.fail(result.cause());
                                    }
                                })).compose(entities -> this.afterGetAll(entities, routingContext)));
            }

        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    protected void filter(RoutingContext routingContext, JsonObject requestParameters)
    {
        try
        {
            //as per standard api require entity count only if it is available in request parameter will be fetching entity count
            var result = this.configStore.getItemsByValues(requestParameters.getString(KEY), requestParameters.getJsonArray(VALUE));

            var items = new JsonArray();

            if (result != null)
            {
                for (var index = 0; index < result.size(); index++)
                {
                    items.add(CommonUtil.removeSensitiveFields(result.getJsonObject(index), true));
                }
            }

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, items));

        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    protected void get(RoutingContext routingContext)
    {
        try
        {
            this.beforeGet().compose(handler ->

                    Future.<JsonObject>future(promise ->
                    {
                        var item = this.configStore.getItem(CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID)));

                        if (item != null && !item.isEmpty())
                        {
                            promise.complete(CommonUtil.removeSensitiveFields(item, true));
                        }

                        else
                        {
                            promise.complete(new JsonObject());
                        }
                    }).compose(entity -> this.afterGet(entity, routingContext)));
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    protected void create(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var collection = schema.getString(APIConstants.ENTITY_COLLECTION);

                this.beforeCreate(routingContext).compose(parameters ->
                        Future.<JsonObject>future(promise ->
                        {
                            parameters.put(ConfigDBConstants.FIELD_TYPE, ConfigDBConstants.ENTITY_TYPE_USER);

                            Bootstrap.configDBService().save(collection, parameters, routingContext.user().principal().getString(User.USER_NAME), routingContext.request().remoteAddress().host(),
                                    result ->
                                    {
                                        if (result.succeeded())
                                        {
                                            //why complete promise on future callback of config store add/update...bcz it sometimes creates plm when this response has a dependent api/business logic
                                            this.configStore.addItem(result.result()).onComplete(asyncResult -> promise.complete(new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                                    .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_CREATED, schema.getString(APIConstants.ENTITY_NAME)))
                                                    .put(ID, result.result())));
                                        }

                                        else
                                        {
                                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, STATUS_FAIL)
                                                    .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_CREATE_FAILED, schema.getString(APIConstants.ENTITY_NAME), result.cause().getMessage()))
                                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                    .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));

                                            promise.fail(result.cause());

                                        }
                                    });

                        }).compose(entity -> this.afterCreate(entity, routingContext))
                );
            }

            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    protected void update(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var collection = schema.getString(APIConstants.ENTITY_COLLECTION);

                this.beforeUpdate(routingContext).compose(parameters ->

                        Future.<JsonObject>future(promise ->
                        {
                            APIUtil.removeDefaultParameters(parameters);

                            var id = CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID));

                            Bootstrap.configDBService().update(collection,
                                    new JsonObject().put(ConfigDBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, id),
                                    parameters, routingContext.user().principal().getString(USER_NAME), routingContext.request().remoteAddress().host(),
                                    result ->
                                    {
                                        if (result.succeeded() && !result.result().isEmpty())
                                        {
                                            this.configStore.updateItem(id).onComplete(asyncResult -> promise.complete(new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                                    .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_UPDATED, schema.getString(APIConstants.ENTITY_NAME))).put(ID, id)));

                                            parameters.put(GlobalConstants.ID, id);

                                            CommonUtil.removeSensitiveFields(parameters, true);
                                        }

                                        else
                                        {
                                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                                                    .put(GlobalConstants.STATUS, STATUS_FAIL)
                                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                    .put(GlobalConstants.ERROR, result.failed() ? CommonUtil.formatStackTrace(result.cause().getStackTrace()) : UNKNOWN)
                                                    .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_UPDATE_FAILED, schema.getString(APIConstants.ENTITY_NAME), result.failed() ? result.cause().getMessage() : UNKNOWN)));

                                            promise.fail(result.failed() ? result.cause().getMessage() : UNKNOWN);

                                            parameters.put(GlobalConstants.ID, id);
                                        }
                                    });


                        }).compose(entity -> this.afterUpdate(entity, routingContext))
                );
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    protected void delete(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var collection = schema.getString(APIConstants.ENTITY_COLLECTION);

                this.beforeDelete(routingContext).compose(parameters ->
                        Future.<JsonObject>future(promise ->
                        {
                            var id = CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID));

                            var item = this.configStore.getItem(id);

                            if (item != null && !item.isEmpty())
                            {
                                var validRequest = true;

                                if (item.getString(ConfigDBConstants.FIELD_TYPE) != null && item.getString(ConfigDBConstants.FIELD_TYPE).equalsIgnoreCase(ConfigDBConstants.ENTITY_TYPE_SYSTEM))
                                {
                                    validRequest = false;

                                    var message = String.format(ErrorMessageConstants.ENTITY_DELETE_NOT_ALLOWED, schema.getString(APIConstants.ENTITY_NAME));

                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                            .put(GlobalConstants.MESSAGE, message).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL));

                                    promise.fail(message);

                                    Bootstrap.vertx().eventBus().send(EVENT_AUDIT,
                                            new JsonObject().put(USER_NAME, routingContext.user().principal().getString(User.USER_NAME)).put(ENTITY_COLLECTION, collection)
                                                    .put(REQUEST, APIConstants.REQUEST_CREATE).put(MESSAGE, message).put(STATUS, Boolean.FALSE));

                                }

                                if (validRequest)
                                {
                                    this.configStore.getReferenceEntities(CommonUtil.getLong(routingContext.request().getParam(ID))).onComplete(result ->
                                    {
                                        if (result.succeeded())
                                        {
                                            this.getReferencesPreHook(routingContext, result.result()).onComplete(response ->
                                            {
                                                if (response.succeeded())
                                                {
                                                    if (!response.result().isEmpty())
                                                    {
                                                        var references = new JsonObject();

                                                        response.result().getMap().keySet().forEach(key ->
                                                        {
                                                            references.put(key, new JsonArray());

                                                            for (var index = 0; index < response.result().getJsonArray(key).size(); index++)
                                                            {
                                                                var reference = response.result().getJsonArray(key).getJsonObject(index);

                                                                if (key.equalsIgnoreCase(Entity.SCHEDULER.getName()))
                                                                {
                                                                    references.getJsonArray(key).add(reference.getString(Scheduler.SCHEDULER_JOB_TYPE).equalsIgnoreCase(JobScheduler.JobType.REDISCOVER.getName())
                                                                            ? reference.getJsonObject(Scheduler.SCHEDULER_CONTEXT).getString(NMSConstants.REDISCOVER_JOB) : reference.getString(Scheduler.SCHEDULER_JOB_TYPE));
                                                                }
                                                                else
                                                                {
                                                                    references.getJsonArray(key).add(reference.getString(REF_PROPS_BY_ENTITY.get(key)));
                                                                }
                                                            }
                                                        });

                                                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                                                .put(RESULT, references) // ui will display this references in tooltip
                                                                .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED_IN_USED, schema.getString(APIConstants.ENTITY_NAME)))
                                                                .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL));

                                                        promise.fail(String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED_IN_USED, schema.getString(APIConstants.ENTITY_NAME)));
                                                    }
                                                    else
                                                    {
                                                        if (parameters != null && parameters.containsKey(SHALLOW_DELETE) && parameters.getString(SHALLOW_DELETE).equalsIgnoreCase(YES))
                                                        {
                                                            promise.complete(new JsonObject().put(ID, id));
                                                        }
                                                        else
                                                        {
                                                            Bootstrap.configDBService().delete(collection,
                                                                    new JsonObject().put(ConfigDBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, id), routingContext.user().principal().getString(User.USER_NAME), routingContext.request().remoteAddress().host(),
                                                                    asyncResult ->
                                                                    {
                                                                        if (asyncResult.succeeded() && !asyncResult.result().isEmpty())
                                                                        {
                                                                            this.configStore.deleteItem(id);

                                                                            promise.complete(new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                                                                    .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_DELETED, schema.getString(APIConstants.ENTITY_NAME))).put(ID, id));
                                                                        }
                                                                        else
                                                                        {
                                                                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                                                                                    .put(GlobalConstants.STATUS, STATUS_FAIL)
                                                                                    .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME), asyncResult.failed() ? asyncResult.cause().getMessage() : UNKNOWN))
                                                                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                                                    .put(GlobalConstants.ERROR, asyncResult.failed() ? CommonUtil.formatStackTrace(asyncResult.cause().getStackTrace()) : UNKNOWN));

                                                                            promise.fail(asyncResult.failed() ? asyncResult.cause().getMessage() : UNKNOWN);

                                                                        }
                                                                    });
                                                        }
                                                    }

                                                }
                                                else
                                                {
                                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME), response.cause().getMessage())));

                                                    promise.fail(response.cause());
                                                }
                                            });
                                        }
                                        else
                                        {
                                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                                    .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME), result.cause().getMessage())));

                                            promise.fail(result.cause());
                                        }
                                    });
                                }
                            }
                            else
                            {
                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME), String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, schema.getString(APIConstants.ENTITY_NAME)))));

                                promise.fail(String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, schema.getString(APIConstants.ENTITY_NAME)));
                            }
                        }).compose(entity -> this.afterDelete(entity, routingContext))
                );
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    protected void deleteAll(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var collection = schema.getString(APIConstants.ENTITY_COLLECTION);

                this.beforeDeleteAll(routingContext).compose(handler ->
                        Future.<JsonObject>future(promise ->
                        {
                            var ids = routingContext.body().asJsonObject().getJsonArray(REQUEST_PARAM_IDS);

                            var items = this.configStore.getItems(ids);

                            if (items != null && !items.isEmpty())
                            {
                                Bootstrap.configDBService().deleteAll(collection,
                                        new JsonObject().put(ConfigDBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, ids), routingContext.user().principal().getString(USER_NAME), routingContext.request().remoteAddress().host()
                                        , result ->
                                        {
                                            String message;

                                            if (result.succeeded())
                                            {
                                                message = String.format(InfoMessageConstants.ENTITY_DELETED, schema.getString(APIConstants.ENTITY_NAME));

                                                this.configStore.deleteItems(ids);

                                                promise.complete(new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                                        .put(REQUEST_PARAM_IDS, ids).put(GlobalConstants.MESSAGE, message));
                                            }
                                            else
                                            {
                                                message = String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME), result.cause().getMessage());

                                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                                        .put(GlobalConstants.MESSAGE, message).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                        .put(ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));

                                                logger.error(result.cause());

                                                promise.fail(result.cause());
                                            }

                                        });
                            }
                            else
                            {
                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_DELETE_FAILED, schema.getString(APIConstants.ENTITY_NAME),
                                                String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, schema.getString(APIConstants.ENTITY_NAME)))));

                                promise.fail(String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, schema.getString(APIConstants.ENTITY_NAME)));
                            }
                        }).compose(response -> this.afterDeleteAll(response, routingContext))
                );
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    protected void alterParameters(RoutingContext routingContext, JsonObject schema)
    {
        var parameters = routingContext.body().asJsonObject();

        var properties = schema.getJsonArray(APIConstants.ENTITY_PROPERTY);

        if (properties != null && !properties.isEmpty())
        {
            for (var index = 0; index < properties.size(); index++)
            {
                var context = properties.getJsonObject(index);

                if (context != null && !context.isEmpty())
                {
                    var name = context.getString(APIConstants.ENTITY_PROPERTY_NAME);

                    var type = context.getString(APIConstants.ENTITY_PROPERTY_TYPE);

                    var privateField = context.getBoolean(APIConstants.ENTITY_PROPERTY_PRIVATE);

                    if (type.equalsIgnoreCase(APIConstants.FIELD_TYPE_SECURED) &&
                            parameters.getValue(name) != null && !CommonUtil.getString(parameters.getValue(name)).isBlank())
                    {
                        parameters.put(name, cipherUtil.encrypt(CommonUtil.getString(parameters.getValue(name))));
                    }

                    if (privateField != null && privateField && parameters.getValue(name) == null)
                    {
                        parameters.put(name, context.getValue(APIConstants.ENTITY_PROPERTY_DEFAULT));
                    }
                }
            }

            routingContext.setBody(Buffer.buffer(parameters.toString()));
        }
    }

}
