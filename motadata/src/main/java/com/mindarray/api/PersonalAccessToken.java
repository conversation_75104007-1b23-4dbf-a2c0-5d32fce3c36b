/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			    Notes
 *  28-Feb-2025		Darshan Parmar		MOTADATA-5215: SonarQube Suggestions Resolution
 */

package com.mindarray.api;

import com.mindarray.GlobalConstants;
import com.mindarray.store.PersonalAccessTokenConfigStore;
import com.mindarray.util.Logger;

public class PersonalAccessToken extends AbstractAPI
{
    public static final String PERSONAL_ACCESS_TOKEN = "personal.access.token";
    public static final String PERSONAL_ACCESS_TOKEN_USER = "personal.access.token.user";
    public static final String PERSONAL_ACCESS_TOKEN_VALIDITY = "personal.access.token.validity";
    public static final String PERSONAL_ACCESS_TOKEN_NAME = "personal.access.token.name";
    public static final String PERSONAL_ACCESS_TOKEN_DESCRIPTION = "personal.access.token.description";
    private static final Logger LOGGER = new Logger(PersonalAccessToken.class, GlobalConstants.MOTADATA_API, "Personal Access Token API");

    public PersonalAccessToken()
    {
        super("personal-access-tokens", PersonalAccessTokenConfigStore.getStore(), LOGGER);
    }

}
