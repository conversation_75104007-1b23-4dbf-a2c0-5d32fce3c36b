/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;


import com.mindarray.ErrorCodes;
import com.mindarray.GlobalConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.BusinessHourConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import static com.mindarray.ErrorMessageConstants.INTERNAL_SERVER_EXCEPTION;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;

public class BusinessHour extends AbstractAPI
{
    public static final String BUSINESS_HOUR_NAME = "business.hour.name";

    public static final String BUSINESS_HOUR_CONTEXT = "business.hour.context";

    private static final Logger LOGGER = new Logger(BusinessHour.class, MOTADATA_API, "Business Hour API");

    public BusinessHour()
    {
        super("business-hours", BusinessHourConfigStore.getStore(), LOGGER);

    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);

            router.get("/" + endpoint + "/:id/references").handler(this::getReferences);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }


    @Override
    protected void getReferences(RoutingContext routingContext)
    {
        try
        {
            this.configStore.getReferenceEntities(CommonUtil.getLong(routingContext.request().getParam(ID))).onComplete(result ->
            {
                if (result.succeeded())
                {
                    var items = new JsonObject();

                    for (var entity : result.result().getMap().keySet())
                    {
                        if (entity.equalsIgnoreCase(APIConstants.Entity.OBJECT.getName())) // if entity is monitor than split agent from it
                        {
                            items.put(APIConstants.Entity.OBJECT.getName(), new JsonArray()).put(APIConstants.Entity.AGENT.getName(), new JsonArray());

                            var entities = result.result().getJsonArray(entity);

                            for (var index = 0; index < entities.size(); index++)
                            {
                                if (entities.getJsonObject(index).getString(AIOpsObject.OBJECT_CATEGORY).equalsIgnoreCase(NMSConstants.Category.SERVER.getName())
                                        && entities.getJsonObject(index).containsKey(AIOpsObject.OBJECT_AGENT))
                                {
                                    items.getJsonArray(APIConstants.Entity.AGENT.getName()).add(entities.getJsonObject(index));
                                }
                                else
                                {
                                    items.getJsonArray(APIConstants.Entity.OBJECT.getName()).add(entities.getJsonObject(index));
                                }
                            }
                        }
                        else
                        {
                            items.put(entity, result.result().getJsonArray(entity)); // in future if any other reference come then don't need to do any change in it
                        }
                    }
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, items));
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                            .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, result.cause().getMessage()))
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                            .put(ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));
                }
            });
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

}