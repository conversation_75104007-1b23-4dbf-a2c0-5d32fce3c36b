/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*

 * Change Logs:
 *   Date          Author              Notes
 *   2025-02-06    <PERSON><PERSON>      Added after update create and delete for Credential profile.
 *  23-Apr-2025		sankalp		    MOTADATA-4883 : Added data security based on user group for reference entities

 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.config.ConfigConstants;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import com.mindarray.util.OAuthUtil;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.eventbus.DeliveryOptions;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.APIConstants.SESSION_ID;
import static com.mindarray.api.User.USER_ID;
import static com.mindarray.api.User.USER_NAME;


public class CredentialProfile extends AbstractAPI
{
    public static final String CREDENTIAL_PROFILE_PROTOCOL = "credential.profile.protocol";

    public static final String CREDENTIAL_PROFILE_NAME = "credential.profile.name";

    public static final String CREDENTIAL_PROFILE_CONTEXT = "credential.profile.context";

    public static final long DEFAULT_EMAIL_CREDENTIAL_PROFILE = 10000000000002L;

    private static final Logger LOGGER = new Logger(CredentialProfile.class, MOTADATA_API, "Credential Profile API");

    public CredentialProfile()
    {
        super("credential-profiles", CredentialProfileConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);

            router.get("/" + endpoint + "/:id/references").handler(this::getReferences);

        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected void update(RoutingContext routingContext)
    {
        try
        {
            var requestParameters = routingContext.body().asJsonObject();

            try
            {
                var item = CredentialProfileConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID)));

                item.mergeIn(routingContext.body().asJsonObject(), true);

                var promise = Promise.<JsonObject>promise();

                if (item.containsKey(CREDENTIAL_PROFILE_PROTOCOL) && NMSConstants.Protocol.HTTP_HTTPS.getName().equalsIgnoreCase(item.getString(CREDENTIAL_PROFILE_PROTOCOL))
                        && item.getJsonObject(CREDENTIAL_PROFILE_CONTEXT).containsKey(OAuthUtil.AUTHENTICATION_TYPE) && NMSConstants.AuthenticationType.OAUTH.getName().equalsIgnoreCase(item.getJsonObject(CREDENTIAL_PROFILE_CONTEXT).getString(OAuthUtil.AUTHENTICATION_TYPE)) && NMSConstants.OAuthGrantType.AUTHORIZATION_CODE.getName().equalsIgnoreCase(item.getJsonObject(CREDENTIAL_PROFILE_CONTEXT).getString(OAuthUtil.GRANT_TYPE)) && item.getJsonObject(CREDENTIAL_PROFILE_CONTEXT).containsKey(SESSION_ID))
                {
                    prepareContext(item, promise);
                }
                else
                {
                    promise.complete();
                }

                promise.future().onComplete(
                        asyncResult ->
                        {
                            if (asyncResult.succeeded())
                            {
                                var updated = item.getJsonObject(CREDENTIAL_PROFILE_CONTEXT).encode().equals(requestParameters.getJsonObject(CREDENTIAL_PROFILE_CONTEXT).encode()); // if credential context change than consider it as a updated and check suspended metrics for that credential only

                                Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_CREDENTIAL_PROFILE,
                                        new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                                        item,
                                        routingContext.user().principal().getString(USER_NAME), routingContext.request().remoteAddress().host(),
                                        result ->
                                        {
                                            if (result.succeeded())
                                            {
                                                CredentialProfileConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(response ->
                                                {
                                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                                            .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.CREDENTIAL_PROFILE.getName())));

                                                    if (updated)
                                                    {
                                                        var metrics = MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_CREDENTIAL_PROFILE, item.getLong(ID));

                                                        for (var index = 0; index < metrics.size(); index++)
                                                        {
                                                            var metric = metrics.getJsonObject(index);

                                                            if (metric.getString(Metric.METRIC_STATE).equalsIgnoreCase(NMSConstants.State.INVALID.name()))
                                                            {
                                                                Bootstrap.vertx().eventBus().send(EventBusConstants.EVENT_METRIC_ENABLE, metric);
                                                            }
                                                        }
                                                    }
                                                });
                                            }
                                            else
                                            {
                                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_UPDATE_FAILED, APIConstants.Entity.CREDENTIAL_PROFILE.getName(), result.cause())));

                                            }
                                        });
                            }
                            else
                            {
                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_UPDATE_FAILED, APIConstants.Entity.CREDENTIAL_PROFILE.getName(), asyncResult.cause())));
                            }
                        });

            }
            catch (Exception exception)
            {
                APIUtil.sendResponse(exception, routingContext);
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected Future<JsonObject> afterUpdate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, entity.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.UPDATE_CREDENTIAL_PROFILE.name()));

        return Future.succeededFuture();
    }

    @Override
    protected Future<Void> afterCreate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, entity.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.ADD_CREDENTIAL_PROFILE.name()));

        return Future.succeededFuture();
    }

    @Override
    protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, entity.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, EventBusConstants.ChangeNotificationType.DELETE_CREDENTIAL_PROFILE.name()));

        return super.afterDelete(entity, routingContext);
    }

    @Override
    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {
        try
        {
            var id = CommonUtil.getLong(routingContext.request().getParam(ID));

            var userGroups = UserConfigStore.getStore().getUserGroupsById(routingContext.user().principal().getLong(ID));

            var metrics = MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_CREDENTIAL_PROFILE, id);

            for (var index = 0; index < metrics.size(); index++)
            {
                var metric = metrics.getJsonObject(index);

                if (NMSConstants.CUSTOM_METRIC_TYPES.contains(metric.getString(Metric.METRIC_TYPE))) // custom metric category
                {
                    addEntity(response, APIConstants.Entity.METRIC.getName());

                    response.getJsonArray(APIConstants.Entity.METRIC.getName()).add(enrich(metric, userGroups));
                }

                else if (NMSConstants.isBaseTypeMetricPlugin(metric.getString(Metric.METRIC_PLUGIN)) || NMSConstants.CLOUD_PLUGINS.contains(metric.getString(Metric.METRIC_PLUGIN))) // if parent metric plugin or cloud type than only send UI
                {
                    if (NMSConstants.APPLICATION_TYPES.contains(metric.getString(Metric.METRIC_TYPE)))
                    {
                        addEntity(response, NMSConstants.APPS);

                        metric = enrich(metric, userGroups);

                        if (metric.containsKey(AIOpsObject.OBJECT_HOST))
                        {
                            response.getJsonArray(NMSConstants.APPS).add(metric);
                        }
                    }

                    else
                    {
                        addEntity(response, APIConstants.Entity.OBJECT.getName());

                        metric = enrich(metric, userGroups);

                        if (metric.containsKey(AIOpsObject.OBJECT_HOST))
                        {
                            response.getJsonArray(APIConstants.Entity.OBJECT.getName()).add(metric);
                        }
                    }
                }
            }

            //Now as we have introduced OAuth credentials that will be used in integrations like teams,slack,GCP so adding integrations profile used count in it
            var items = IntegrationConfigStore.getStore().getItemsByMapValueField(Integration.INTEGRATION_CONTEXT, IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, id);

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                item.remove(Integration.INTEGRATION_ATTRIBUTES);

                addEntity(response, APIConstants.Entity.INTEGRATION.getName());

                response.getJsonArray(APIConstants.Entity.INTEGRATION.getName()).add(item);
            }


            if (response != null && !response.isEmpty() && response.containsKey(APIConstants.Entity.CONFIGURATION.getName()))
            {
                var entities = response.getJsonArray(APIConstants.Entity.CONFIGURATION.getName());

                for (var index = 0; index < entities.size(); index++)
                {
                    var item = entities.getJsonObject(index);

                    var object = ObjectConfigStore.getStore().getItemByValue(AIOpsObject.OBJECT_ID, item.getInteger(Configuration.CONFIG_OBJECT));

                    ConfigConstants.removeGarbageFields(object);

                    item.mergeIn(object);
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
        return Future.succeededFuture(response);
    }

    private void addEntity(JsonObject response, String entity)
    {
        if (!response.containsKey(entity))
        {
            response.put(entity, new JsonArray());
        }
    }

    /*Method will now first check if object is part of groups allowed to user or not
     */
    private JsonObject enrich(JsonObject item, JsonArray userGroups)
    {
        var items = ObjectConfigStore.getStore().getIdsByGroups(userGroups, new JsonArray().add(item.getLong(Metric.METRIC_OBJECT)));

        var object = items.isEmpty() ? null : ObjectConfigStore.getStore().getItem(items.getLong(0));

        if (object != null)
        {
            item.mergeIn(object);
        }

        return item;
    }

    @Override
    protected Future<JsonObject> getEntityCountPreHook(JsonObject response)
    {
        try
        {
            var ids = CredentialProfileConfigStore.getStore().getIds();

            var userGroups = new JsonArray();

            if (response.containsKey(USER_ID))
            {
                userGroups = UserConfigStore.getStore().getUserGroupsById(response.getLong(USER_ID));
            }

            for (var count = 0; count < ids.size(); count++)
            {
                var metrics = MetricConfigStore.getStore().getItemsByValue(Metric.METRIC_CREDENTIAL_PROFILE, ids.getLong(count));

                var references = 0;

                for (var index = 0; index < metrics.size(); index++)
                {
                    var metric = metrics.getJsonObject(index);

                    // 1. custom metric category
                    // 2. if parent metric plugin than only send UI
                    //3 . for cloud category qualify instances reference
                    if (NMSConstants.CUSTOM_METRIC_TYPES.contains(metric.getString(Metric.METRIC_TYPE)))
                    {
                        references++;
                    }

                    if (NMSConstants.isBaseTypeMetricPlugin(metric.getString(Metric.METRIC_PLUGIN))
                            || NMSConstants.CLOUD_PLUGINS.contains(metric.getString(Metric.METRIC_PLUGIN)))
                    {
                        if (enrich(metric, userGroups).containsKey(AIOpsObject.OBJECT_HOST))
                        {
                            references++;
                        }
                    }
                }

                references += IntegrationConfigStore.getStore().getItemsByMapValueField(Integration.INTEGRATION_CONTEXT, IntegrationProfile.INTEGRATION_CREDENTIAL_PROFILE, ids.getLong(count)).size();

                response.put(CommonUtil.getString(ids.getLong(count)),
                        response.containsKey(CommonUtil.getString(ids.getLong(count))) ? response.getInteger(CommonUtil.getString(ids.getLong(count))) + references : references);
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return Future.succeededFuture(response);
    }

    @Override
    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        var item = routingContext.body().asJsonObject();

        var promise = Promise.<JsonObject>promise();

        if (item.containsKey(CREDENTIAL_PROFILE_PROTOCOL) && NMSConstants.Protocol.HTTP_HTTPS.getName().equalsIgnoreCase(item.getString(CREDENTIAL_PROFILE_PROTOCOL)))
        {

            if (item.getJsonObject(CREDENTIAL_PROFILE_CONTEXT).containsKey(OAuthUtil.AUTHENTICATION_TYPE) && NMSConstants.AuthenticationType.OAUTH.getName().equalsIgnoreCase(item.getJsonObject(CREDENTIAL_PROFILE_CONTEXT).getString(OAuthUtil.AUTHENTICATION_TYPE)) && item.getJsonObject(CREDENTIAL_PROFILE_CONTEXT).containsKey(SESSION_ID) && NMSConstants.OAuthGrantType.AUTHORIZATION_CODE.getName().equalsIgnoreCase(item.getJsonObject(CREDENTIAL_PROFILE_CONTEXT).getString(OAuthUtil.GRANT_TYPE)))
            {
                prepareContext(item, promise);
            }
            else
            {
                promise.complete(item);
            }
        }
        else
        {
            promise.complete(item);
        }

        return promise.future();
    }

    private void prepareContext(JsonObject item, Promise<JsonObject> promise)
    {
        try
        {
            var context = item.getJsonObject(CREDENTIAL_PROFILE_CONTEXT);

            Bootstrap.vertx().eventBus().<JsonObject>request(EventBusConstants.EVENT_OAUTH_CONTEXT_FETCH, context,
                    new DeliveryOptions().setSendTimeout(2 * 1000L),
                    reply ->
                    {
                        if (reply.succeeded())
                        {
                            context.mergeIn(reply.result().body());

                            context.remove(SESSION_ID);

                            promise.complete(item);
                        }
                        else
                        {
                            promise.fail(reply.cause());
                        }
                    });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }
    }
}
