/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *     Change Logs:
 *     Date            Author          Notes
 *     21-Mar-2025     Vismit          MOTADATA-5094: Added Config Upgrade Ops job type in create(), fetching discovered devices and setting scheduler context
 *     25-Mar-2025     Vismit          MOTADATA-5094: Added case to handle firmware upgrade file name and size while scheduling firmware upgrade
 */
package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.config.ConfigConstants;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.job.JobScheduler;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.ConfigurationConfigStore;
import com.mindarray.store.SchedulerCacheStore;
import com.mindarray.store.SchedulerConfigStore;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.CronExpressionUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import static com.mindarray.ErrorMessageConstants.INTERNAL_SERVER_EXCEPTION;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.User.USER_NAME;

public class Scheduler extends AbstractAPI
{
    public static final String SCHEDULER_TIMELINE = "scheduler.timeline";
    public static final String SCHEDULER_STATE = "scheduler.state";
    public static final String SCHEDULER_START_DATE = "scheduler.start.date";
    public static final String SCHEDULER_JOB_TYPE = "scheduler.job.type";
    public static final String SCHEDULER_TIMES = "scheduler.times";
    public static final String SCHEDULER_MONTHS = "scheduler.months";
    public static final String SCHEDULER_DATES = "scheduler.dates";
    public static final String SCHEDULER_DAYS = "scheduler.days";
    public static final String SCHEDULER_CONTEXT = "scheduler.context";
    public static final String SCHEDULER_EMAIL_RECIPIENTS = "scheduler.email.recipients";
    public static final String SCHEDULER_SMS_RECIPIENTS = "scheduler.sms.recipients";
    private static final Logger LOGGER = new Logger(Scheduler.class, MOTADATA_API, "Scheduler API");
    private static final String SCHEDULER_DATE_TIME = "scheduler.date.time";

    public Scheduler()
    {
        super("schedulers", SchedulerConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            router.get("/" + endpoint + "/:id").handler(this::get);

            router.get("/" + endpoint).handler(this::getAll);

            router.post("/" + endpoint).handler(this::create);

            router.put("/" + endpoint + "/:id").handler(this::update);

            router.delete("/" + endpoint + "/:id").handler(this::delete);

            router.put("/" + endpoint + "/:id/state").handler(this::updateState);

        }

        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

    }

    @Override
    protected void get(RoutingContext routingContext)
    {
        try
        {
            var item = SchedulerConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID)));

            if (item != null)
            {
                var schedulerContext = item.getJsonObject(SCHEDULER_CONTEXT);

                if (item.getString(SCHEDULER_JOB_TYPE).equalsIgnoreCase(JobScheduler.JobType.MAINTENANCE.getName()))
                {
                    var rows = new JsonArray();

                    var eventContexts = schedulerContext.getJsonArray(EventBusConstants.EVENT_CONTEXT);

                    for (var index = 0; index < eventContexts.size(); index++)
                    {
                        var context = eventContexts.getJsonObject(index);

                        buildSchedulerDateTimeContext(item.mergeIn(context));

                        rows.add(context.put(Scheduler.SCHEDULER_DATE_TIME, item.getValue(Scheduler.SCHEDULER_DATE_TIME)));
                    }

                    item.getJsonObject(SCHEDULER_CONTEXT).put(EventBusConstants.EVENT_CONTEXT, rows);
                }
                else
                {

                    buildSchedulerDateTimeContext(item);
                }


                if (schedulerContext.getJsonArray(NMSConstants.OBJECTS) != null && !schedulerContext.getJsonArray(NMSConstants.OBJECTS).isEmpty())
                {
                    item.put(NMSConstants.OBJECTS, schedulerContext.getJsonArray(NMSConstants.OBJECTS));
                }

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, item));
            }

            else
            {

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, new JsonObject()));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected void create(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var collection = schema.getString(APIConstants.ENTITY_COLLECTION);

                var parameter = routingContext.body().asJsonObject().put(ConfigDBConstants.FIELD_TYPE, ConfigDBConstants.ENTITY_TYPE_USER).put(SCHEDULER_STATE, YES);

                var context = new JsonArray();

                if (parameter.getString(SCHEDULER_JOB_TYPE).equalsIgnoreCase(JobScheduler.JobType.MAINTENANCE.getName()) ||
                        parameter.getString(SCHEDULER_JOB_TYPE).equalsIgnoreCase(JobScheduler.JobType.CONFIG_BACKUP_OPERATION.getName()) ||
                        parameter.getString(SCHEDULER_JOB_TYPE).equalsIgnoreCase(JobScheduler.JobType.CONFIG_UPGRADE_OPERATION.getName()))
                {
                    var schedulerContext = JsonObject.mapFrom(parameter.remove(SCHEDULER_CONTEXT));

                    var objects = schedulerContext.getJsonArray(NMSConstants.OBJECTS);

                    var jobType = JobScheduler.JobType.valueOfName(parameter.getString(SCHEDULER_JOB_TYPE));

                    for (var index = 0; index < objects.size(); index++)
                    {
                        var object = new JsonObject();

                        if (jobType == JobScheduler.JobType.MAINTENANCE)
                        {
                            object.mergeIn(parameter).put(SCHEDULER_CONTEXT, new JsonObject().put(EventBusConstants.EVENT_CONTEXT, schedulerContext.getJsonArray(EventBusConstants.EVENT_CONTEXT)).put(NMSConstants.OBJECTS, new JsonArray().add(objects.getLong(index))));
                        }
                        else if (jobType == JobScheduler.JobType.CONFIG_BACKUP_OPERATION || jobType == JobScheduler.JobType.CONFIG_UPGRADE_OPERATION)
                        {
                            var item = ConfigurationConfigStore.getStore().getItem(objects.getLong(index));

                            // Create Config Backup or Upgrade scheduler only if device have successfully discovered
                            if (item != null && item.containsKey(Configuration.CONFIG_LAST_DISCOVERY_STATUS))
                            {
                                object.mergeIn(parameter).put(SCHEDULER_CONTEXT, jobType == JobScheduler.JobType.CONFIG_BACKUP_OPERATION ? new JsonObject().put(NMSConstants.OBJECTS, new JsonArray().add(objects.getLong(index))) : (new JsonObject().put(NMSConstants.OBJECTS, new JsonArray().add(objects.getLong(index)))
                                        .put(ConfigConstants.CONFIG_FIRMWARE_IMAGE_FILE_NAME, schedulerContext.getString(ConfigConstants.CONFIG_FIRMWARE_IMAGE_FILE_NAME))
                                        .put(ConfigConstants.CONFIG_FIRMWARE_IMAGE_FILE_SIZE, schedulerContext.getString(ConfigConstants.CONFIG_FIRMWARE_IMAGE_FILE_SIZE))));
                            }
                        }

                        context.add(object);
                    }
                }
                else
                {
                    context.add(parameter);
                }

                Bootstrap.configDBService().saveAll(collection, context, routingContext.user().principal().getString(User.USER_NAME), routingContext.request().remoteAddress().host(),
                        result ->
                        {
                            if (result.succeeded())
                            {
                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                        .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_CREATED, schema.getString(APIConstants.ENTITY_NAME)))
                                        .put(ID, result.result()));

                                Bootstrap.vertx().executeBlocking(future -> this.configStore.updateItems(result.result()).onComplete(asyncResult ->
                                {
                                    for (var index = 0; index < result.result().size(); index++)
                                    {
                                        JobScheduler.scheduleCustomJob(SchedulerConfigStore.getStore().getItem(result.result().getLong(index)).put(USER_NAME, routingContext.user().principal().getString(User.USER_NAME)).put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()));
                                    }

                                    future.complete();

                                }), false, asyncResult ->
                                {
                                });
                            }
                            else
                            {
                                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, STATUS_FAIL)
                                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_CREATE_FAILED, schema.getString(APIConstants.ENTITY_NAME), result.cause().getMessage()))
                                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                        .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));
                            }
                        });
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    @Override
    protected Future<JsonObject> afterUpdate(JsonObject response, RoutingContext routingContext)
    {
        this.send(routingContext, response);

        JobScheduler.scheduleCustomJob(SchedulerConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID))).put(USER_NAME, routingContext.user().principal().getString(User.USER_NAME)).put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()));

        return Future.succeededFuture();
    }

    @Override
    protected Future<Void> afterDelete(JsonObject response, RoutingContext routingContext)
    {
        JobScheduler.removeJob(CommonUtil.getLong(routingContext.request().getParam(ID)));

        return super.afterDelete(response, routingContext);
    }

    @Override
    protected void getAll(RoutingContext routingContext)
    {
        try
        {
            var requestParameters = JsonObject.mapFrom(Json.decodeValue(routingContext.request().getParam(FILTER)));

            if (requestParameters != null && requestParameters.containsKey(SCHEDULER_JOB_TYPE))
            {
                var jobType = requestParameters.getString(SCHEDULER_JOB_TYPE);

                var items = new JsonArray();

                JsonArray schedulers;

                switch (JobScheduler.JobType.valueOfName(jobType))
                {
                    case DISCOVERY, RUNBOOK, REPORT, CONFIG_BACKUP_OPERATION, CONFIG_UPGRADE_OPERATION,
                         DATABASE_BACKUP ->
                    {
                        schedulers = SchedulerConfigStore.getStore().getItemsByValue(SCHEDULER_JOB_TYPE, jobType);

                        for (var index = 0; index < schedulers.size(); index++)
                        {
                            var item = schedulers.getJsonObject(index);

                            if (requestParameters.getLong(ID).equals(item.getJsonObject(SCHEDULER_CONTEXT).getJsonArray(NMSConstants.OBJECTS).getLong(0)))
                            {
                                buildSchedulerDateTimeContext(item);

                                items.add(item);
                            }
                        }
                    }

                    case COMPLIANCE_POLICY ->
                    {
                        schedulers = SchedulerConfigStore.getStore().getItemsByValue(SCHEDULER_JOB_TYPE, jobType);

                        for (var index = 0; index < schedulers.size(); index++)
                        {
                            var item = schedulers.getJsonObject(index);

                            item.put(NMSConstants.STATE, SchedulerCacheStore.getStore().getSchedulerEvents(item.getLong(ID)) != null ? NMSConstants.STATE_RUNNING : NMSConstants.STATE_NOT_RUNNING);

                            if (item.getJsonObject(SCHEDULER_CONTEXT).containsKey(CompliancePolicy.COMPLIANCE_POLICY_ENTITIES) && requestParameters.getLong(ID).equals(item.getJsonObject(SCHEDULER_CONTEXT).getJsonArray(CompliancePolicy.COMPLIANCE_POLICY_ENTITIES).getLong(0)))
                            {
                                buildSchedulerDateTimeContext(item);

                                items.add(item);
                            }
                        }
                    }

                    case TOPOLOGY ->
                    {
                        schedulers = SchedulerConfigStore.getStore().getItemsByValue(SCHEDULER_JOB_TYPE, jobType);

                        for (var index = 0; index < schedulers.size(); index++)
                        {
                            var item = schedulers.getJsonObject(index);

                            item.remove(RESULT);

                            item.put(NMSConstants.STATE, SchedulerCacheStore.getStore().getSchedulerEvents(item.getLong(ID)) != null ? NMSConstants.STATE_RUNNING : NMSConstants.STATE_NOT_RUNNING);

                            buildSchedulerDateTimeContext(item);

                            items.add(item);
                        }
                    }

                    case MAINTENANCE ->
                    {
                        schedulers = SchedulerConfigStore.getStore().getItemsByValue(SCHEDULER_JOB_TYPE, jobType);

                        for (var index = 0; index < schedulers.size(); index++)
                        {
                            var item = schedulers.getJsonObject(index);

                            if (requestParameters.getLong(ID).equals(item.getJsonObject(SCHEDULER_CONTEXT).getJsonArray(NMSConstants.OBJECTS).getLong(0)))
                            {
                                var rows = new JsonArray();

                                var eventContexts = item.getJsonObject(SCHEDULER_CONTEXT).getJsonArray(EventBusConstants.EVENT_CONTEXT);

                                for (var contextIndex = 0; contextIndex < eventContexts.size(); contextIndex++)
                                {
                                    var context = eventContexts.getJsonObject(contextIndex);

                                    buildSchedulerDateTimeContext(item.mergeIn(context));

                                    rows.add(context.put(Scheduler.SCHEDULER_DATE_TIME, item.getValue(Scheduler.SCHEDULER_DATE_TIME)));
                                }

                                item.getJsonObject(SCHEDULER_CONTEXT).put(EventBusConstants.EVENT_CONTEXT, rows);

                                items.add(item);
                            }
                        }
                    }

                    case REDISCOVER ->
                    {
                        var rediscoverJob = requestParameters.getString(NMSConstants.REDISCOVER_JOB);

                        schedulers = SchedulerConfigStore.getStore().getItemsByMapValueField(Scheduler.SCHEDULER_CONTEXT, NMSConstants.REDISCOVER_JOB, rediscoverJob);

                        for (var index = 0; index < schedulers.size(); index++)
                        {
                            var item = schedulers.getJsonObject(index);

                            item.remove(RESULT);

                            var schedulerContext = item.getJsonObject(SCHEDULER_CONTEXT);

                            item.put(NMSConstants.STATE, SchedulerCacheStore.getStore().getSchedulerEvents(item.getLong(ID)) != null ? NMSConstants.STATE_RUNNING : NMSConstants.STATE_NOT_RUNNING);

                            if (rediscoverJob.equalsIgnoreCase(schedulerContext.getString(NMSConstants.REDISCOVER_JOB)))
                            {
                                buildSchedulerDateTimeContext(item);

                                if (schedulerContext.getJsonArray(NMSConstants.OBJECTS) != null && !schedulerContext.getJsonArray(NMSConstants.OBJECTS).isEmpty())
                                {
                                    item.put(NMSConstants.OBJECTS, schedulerContext.getJsonArray(NMSConstants.OBJECTS));
                                }

                                items.add(item);
                            }
                        }
                    }

                    default -> LOGGER.warn(String.format("invalid job scheduler type %s", jobType));
                }

                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, items));

            }

            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                        .put(GlobalConstants.MESSAGE, ErrorMessageConstants.API_INVALID_INPUT_PARAMETERS));


            }
        }

        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void buildSchedulerDateTimeContext(JsonObject context)
    {
        try
        {
            var dateTimeContext = new JsonArray();

            var timeContext = new JsonArray();

            var monthContext = new JsonArray();

            switch (context.getString(Scheduler.SCHEDULER_TIMELINE))
            {
                case CronExpressionUtil.CRON_DAILY ->
                {
                    for (var time : context.getJsonArray(Scheduler.SCHEDULER_TIMES))
                    {
                        dateTimeContext.add(time.toString());
                    }
                }

                case CronExpressionUtil.CRON_WEEKLY ->
                {
                    for (var time : context.getJsonArray(Scheduler.SCHEDULER_TIMES))
                    {
                        timeContext.add("@@ " + time.toString());
                    }

                    for (var day : context.getJsonArray(Scheduler.SCHEDULER_DAYS))
                    {
                        for (var time : timeContext)
                        {
                            dateTimeContext.add(day.toString() + " " + time.toString().split("@@ ")[1]);
                        }
                    }
                }

                case CronExpressionUtil.CRON_MONTHLY ->
                {
                    for (var time : context.getJsonArray(Scheduler.SCHEDULER_TIMES))
                    {
                        timeContext.add("@@ ## " + time.toString());
                    }

                    for (var month : context.getJsonArray(Scheduler.SCHEDULER_MONTHS))
                    {
                        for (var time : timeContext)
                        {
                            monthContext.add("@@ " + month.toString() + " " + time.toString().split("@@ ## ")[1]);
                        }
                    }

                    for (var date : context.getJsonArray(Scheduler.SCHEDULER_DATES))
                    {
                        for (var monthTime : monthContext)
                        {
                            dateTimeContext.add(date.toString() + " " + monthTime.toString().split("@@ ")[1]);
                        }
                    }
                }

                case CronExpressionUtil.CRON_ONCE ->
                {
                    for (var time : context.getJsonArray(Scheduler.SCHEDULER_TIMES))
                    {
                        dateTimeContext.add(context.getString(Scheduler.SCHEDULER_START_DATE) + " " + time.toString());
                    }
                }

                default ->
                {
                }
            }

            context.put(Scheduler.SCHEDULER_DATE_TIME, dateTimeContext);

        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void updateState(RoutingContext routingContext)
    {
        var state = routingContext.body().asJsonObject().getString(SCHEDULER_STATE);

        var schedulerId = CommonUtil.getLong(routingContext.request().getParam(ID));

        try
        {
            Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_SCHEDULER, new JsonObject().put(ConfigDBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, schedulerId),
                    new JsonObject().put(Scheduler.SCHEDULER_STATE, state), routingContext.user().principal().getString(USER_NAME)
                    , routingContext.request().remoteAddress().host(), result ->
                    {
                        if (result.succeeded())
                        {
                            SchedulerConfigStore.getStore().updateItem(schedulerId);

                            this.send(routingContext, new JsonObject().put(MESSAGE, "Scheduler " + (state.equalsIgnoreCase(YES) ? "Enabled" : "Disabled") + " successfully...").put(RESPONSE_CODE, HttpStatus.SC_OK)
                                    .put(GlobalConstants.STATUS, STATUS_SUCCEED).put(ID, schedulerId));
                        }

                        else
                        {

                            this.send(routingContext, new JsonObject().put(MESSAGE, "Failed to " + (state.equalsIgnoreCase(YES) ? "Enable" : "Disable") + " scheduler").put(RESPONSE_CODE, HttpStatus.SC_OK)
                                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, result.cause().getMessage()))
                                    .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace()))
                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                    .put(GlobalConstants.STATUS, STATUS_FAIL).put(ID, schedulerId));
                        }
                    });
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }
}
