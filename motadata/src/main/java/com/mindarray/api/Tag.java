/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *	Change Logs:
 *	Date			Author			        Notes
 *  20-Feb-2025		Pruthviraj Jadeja		NetRoute Tag added in getAll
 */

package com.mindarray.api;

import com.mindarray.Bootstrap;
import com.mindarray.GlobalConstants;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.store.*;
import com.mindarray.util.CipherUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.Json;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.nms.NMSConstants.OBJECTS;

public class Tag extends AbstractAPI
{
    public static final String TAG = "tag";
    public static final String TAG_TYPE = "tag.type";
    private static final Logger LOGGER = new Logger(Tag.class, MOTADATA_API, "Tag API");

    public Tag()
    {
        super("tags", MiscConfigStore.getStore(), LOGGER, new CipherUtil());
    }

    @Override
    public void init(Router router)
    {
        router.get("/" + endpoint).handler(this::getTags);

        router.get("/" + endpoint + "/" + "instances" + "/:id").handler(this::getInstances);//will be fetching instances for each monitor if for instance tagging

        router.put("/" + endpoint + "/" + "instance-tags" + "/:id").handler(this::updateInstanceTags); //will be fetching instances for each monitor if for instance tagging
    }

    private void getTags(RoutingContext routingContext)
    {
        if (routingContext.request().getParam(FILTER) != null && JsonObject.mapFrom(Json.decodeValue(routingContext.request().getParam(FILTER))).containsKey(Tag.TAG_TYPE))
        {
            var requestParameters = JsonObject.mapFrom(Json.decodeValue(routingContext.request().getParam(FILTER)));

            if (TagType.INSTANCE.getName().equalsIgnoreCase(requestParameters.getString(Tag.TAG_TYPE)))
            {
                //send instance-tags if received counter has ~
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                        .put(GlobalConstants.RESULT, TagCacheStore.getStore().getInstanceTags()));
            }
            else if (TagType.OBJECT.getName().equalsIgnoreCase(requestParameters.getString(Tag.TAG_TYPE)))
            {
                //send object user tags if received type
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                        .put(GlobalConstants.RESULT, ObjectConfigStore.getStore().getTags(TagType.OBJECT.getName(), requestParameters.getString(ConfigDBConstants.FIELD_TYPE, EMPTY_VALUE))));

            }
            else if (TagType.COMPLIANCE.getName().equalsIgnoreCase(requestParameters.getString(TAG_TYPE)))
            {
                //send compliance tags if received type
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                        .put(GlobalConstants.RESULT, TagConfigStore.getStore().getItemsByTagType(TagType.COMPLIANCE)));
            }
            else if (TagType.NETROUTE.getName().equalsIgnoreCase(requestParameters.getString(TAG_TYPE)))
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                        .put(GlobalConstants.RESULT, TagConfigStore.getStore().getItemsByTagType(TagType.NETROUTE)));
            }
        }

        else
        {
            //send all tags
            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                    .put(GlobalConstants.STATUS, GlobalConstants.STATUS_SUCCEED)
                    .put(GlobalConstants.RESULT, ObjectConfigStore.getStore().getTags(TagType.OBJECT.getName(), EMPTY_VALUE)));
        }
    }

    private void updateInstanceTags(RoutingContext routingContext)
    {
        try
        {
            var context = routingContext.body().asJsonObject();

            var ids = new JsonArray();

            var futures = new ArrayList<Future<Void>>();

            for (var entry : context.getMap().entrySet()) //At a time, always one metric group's tags will be updated.
            {
                var promise = Promise.<Void>promise();

                var tokens = entry.getKey().split(KEY_SEPARATOR); //key = metric.id # instance.type

                var item = MetricConfigStore.getStore().getItem(CommonUtil.getLong(tokens[0]));

                item.getJsonObject(Metric.METRIC_CONTEXT).put(OBJECTS, entry.getValue());

                ids.add(CommonUtil.getLong(tokens[0]));

                futures.add(promise.future());

                var objects = item.getJsonObject(Metric.METRIC_CONTEXT).getJsonArray(OBJECTS);

                for (var index = 0; index < objects.size(); index++)
                {
                    var object = objects.getJsonObject(index);

                    if (object.containsKey(INSTANCE_TAGS))
                    {
                        object.put(INSTANCE_TAGS, TagConfigStore.getStore().addItems(object.getJsonArray(INSTANCE_TAGS), TagType.INSTANCE.getName(), ConfigDBConstants.ENTITY_TYPE_USER));
                    }

                }

                item.getJsonObject(Metric.METRIC_CONTEXT).put(OBJECTS, objects);

                TagCacheStore.getStore().updateInstanceTags(CommonUtil.getLong(tokens[0]), context.getJsonArray(entry.getKey()), NMSConstants.INSTANCE_TYPES.getOrDefault(item.getString(Metric.METRIC_PLUGIN), tokens[1]));

                Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_METRIC,
                        new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                        item,
                        routingContext.user().principal().getString(User.USER_NAME), routingContext.request().remoteAddress().host(),
                        result ->
                        {
                            if (result.succeeded())
                            {
                                promise.complete();
                            }
                            else
                            {
                                promise.fail(result.cause().getMessage());
                            }
                        });


            }

            Future.join(futures).onComplete(result ->
            {
                if (!ids.isEmpty())
                {
                    MetricConfigStore.getStore().updateItems(ids).onComplete(asyncResult -> this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, result)));
                }
            });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            APIUtil.sendResponse(exception, routingContext);
        }
    }


    private void getInstances(RoutingContext routingContext)
    {
        try
        {
            var items = MetricConfigStore.getStore().getItemsByObject(CommonUtil.getLong(routingContext.request().getParam(ID)));

            var instances = new JsonObject();

            for (var item : items)
            {
                var context = item.getJsonObject(Metric.METRIC_CONTEXT);

                var objects = context.getJsonArray(OBJECTS);

                if (objects != null && !objects.isEmpty())
                {
                    for (var index = 0; index < objects.size(); index++)
                    {
                        var object = objects.getJsonObject(index);

                        if (object.containsKey(INSTANCE_TAGS))
                        {
                            object.put(INSTANCE_TAGS, TagConfigStore.getStore().getItems(object.getJsonArray(INSTANCE_TAGS)));
                        }
                    }

                    instances.put(item.getLong(ID) + KEY_SEPARATOR + context.getJsonArray(OBJECTS).getJsonObject(0).getString(AIOpsObject.OBJECT_TYPE), context.getJsonArray(OBJECTS));
                }
            }

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.RESULT, instances));
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            APIUtil.sendResponse(exception, routingContext);
        }
    }

    public enum TagType
    {
        OBJECT("Object"),
        INSTANCE("Instance"),
        COMPLIANCE("Compliance"),
        NETROUTE("NetRoute");

        private static final Map<String, TagType> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(TagType::getName, e -> e)));
        private final String name;

        TagType(String name)
        {
            this.name = name;
        }

        public static TagType valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }
    }
}
