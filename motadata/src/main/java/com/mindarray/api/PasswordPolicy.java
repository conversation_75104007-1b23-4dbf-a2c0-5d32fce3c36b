/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.store.PasswordPolicyConfigStore;
import com.mindarray.util.Logger;

import static com.mindarray.GlobalConstants.MOTADATA_API;

public class PasswordPolicy extends AbstractAPI
{
    public static final String PASSWORD_POLICY_EXPIRY = "password.policy.password.expiry";

    public static final String PASSWORD_POLICY_EXPIRY_DAYS = "password.policy.password.expiry.days";

    public static final String PASSWORD_POLICY_UPPERCASE_CHECK = "password.policy.uppercase.check";

    public static final String PASSWORD_POLICY_LOWERCASE_CHECK = "password.policy.lowercase.check";

    public static final String PASSWORD_POLICY_SPECIAL_CHARACTER_CHECK = "password.policy.special.character.check";

    public static final String PASSWORD_POLICY_NUMBER_CHECK = "password.policy.number.check";

    public static final String PASSWORD_POLICY_MINIMUM_LENGTH = "password.policy.password.minimum.length";

    public PasswordPolicy()
    {
        super("password-policy", PasswordPolicyConfigStore.getStore(), new Logger(PasswordPolicy.class, MOTADATA_API, "Password Policy API"));
    }
}
