/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *  Change Logs:
 *   Date			Author			Notes
 *   5-Feb-2025		<PERSON><PERSON>		MOTADATA-5007: added the support to update script variables in metric plugin
 *   23-Apr-2025		sankalp		    MOTADATA-4883 : Added data security based on user group for reference entities
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.nms.NMSConstants;
import com.mindarray.plugin.PluginEngineConstants;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.http.HttpStatus;

import java.util.ArrayList;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.api.User.USER_ID;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.db.ConfigDBConstants.COLLECTION_METRIC_PLUGIN;
import static com.mindarray.db.ConfigDBConstants.FIELD_NAME;

public class MetricPlugin extends AbstractAPI
{
    public static final String METRIC_PLUGIN_NAME = "metric.plugin.name";
    public static final String METRIC_PLUGIN_CREDENTIAL_PROFILE = "metric.plugin.credential.profile";
    public static final String METRIC_PLUGIN_CONTEXT = "metric.plugin.context";
    public static final String METRIC_PLUGIN_PROTOCOL = "metric.plugin.protocol";
    public static final String METRIC_PLUGIN_VARIABLES = "metric.plugin.variables";
    public static final String METRIC_PLUGIN_ENTITY_TYPE = "metric.plugin.entity.type";
    public static final String METRIC_PLUGIN_VENDOR = "metric.plugin.vendor";
    public static final String METRIC_PLUGIN_ENTITIES = "metric.plugin.entities";
    public static final String METRIC_PLUGIN_TYPE = "metric.plugin.type";
    private static final Logger LOGGER = new Logger(MetricPlugin.class, MOTADATA_API, "Metric Plugin API");

    public MetricPlugin()
    {
        super("metric-plugins", MetricPluginConfigStore.getStore(), LOGGER);
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);

            router.get("/" + endpoint + "/:id/references").handler(this::getReferences);

            router.put("/" + endpoint + "/:id/assign").handler(this::assign);

            router.post("/" + endpoint + "/provision").handler(this::provision);

            router.put("/" + endpoint + "/:id/unassign").handler(this::unassign);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        var promise = Promise.<JsonObject>promise();

        try
        {
            var params = routingContext.body().asJsonObject();

            var context = params.getJsonObject(METRIC_PLUGIN_CONTEXT);

            context.put(Metric.METRIC_PLUGIN, NMSConstants.MetricPlugin.valueOfName(!params.getString(MetricPlugin.METRIC_PLUGIN_PROTOCOL).equalsIgnoreCase(NMSConstants.TopologyPluginType.SNMP.getName()) ? params.getString(MetricPlugin.METRIC_PLUGIN_PROTOCOL) : NMSConstants.MetricPlugin.SNMP_METRIC.getName()).getName());

            params.put(MetricPlugin.METRIC_PLUGIN_CONTEXT, context);

            if (DATABASE.equalsIgnoreCase(params.getString(METRIC_PLUGIN_PROTOCOL)))
            {
                var object = ObjectConfigStore.getStore().getItem(params.getJsonArray(METRIC_PLUGIN_ENTITIES).getLong(0));

                var metric = MetricConfigStore.getStore().getItem(MetricConfigStore.getStore().getItemByMetricPlugin(object.getLong(ID),
                        NMSConstants.getMetricPlugin(NMSConstants.Type.valueOfName(params.getString(MetricPlugin.METRIC_PLUGIN_TYPE, object.getString(AIOpsObject.OBJECT_TYPE))))));

                if (metric != null && metric.containsKey(Metric.METRIC_CONTEXT))
                {
                    params.getJsonObject(METRIC_PLUGIN_CONTEXT).put(DATABASE, metric.getJsonObject(Metric.METRIC_CONTEXT).getString(DATABASE));

                    promise.complete(params);
                }
                else
                {
                    promise.fail(String.format(ErrorMessageConstants.ENTITY_CREATE_FAILED, COLLECTION_METRIC_PLUGIN, "does not have database instance"));
                }
            }
            else
            {
                promise.complete(params);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception.getMessage());
        }

        return promise.future();
    }

    @Override
    protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
    {
        try
        {
            //list down all metrics that contains deleted metric plugin id in metric.context : {"entity.id":"metric.plugin.id"} and send it to unprovision
            var items = MetricConfigStore.getStore().getItemsByMapValueField(Metric.METRIC_CONTEXT, APIConstants.ENTITY_ID,
                    CommonUtil.getLong(routingContext.request().getParam(GlobalConstants.ID)));

            var metrics = new ArrayList<JsonObject>();

            for (var index = 0; index < items.size(); index++)
            {
                metrics.add(new JsonObject().put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host())
                        .put(ID, items.getJsonObject(index).getLong(ID)).put(USER_NAME, routingContext.user().principal().getString(USER_NAME)));
            }

            NMSConstants.updateMetrics(metrics, EventBusConstants.EVENT_METRIC_UNPROVISION);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return super.afterDelete(entity, routingContext);
    }

    @Override
    protected Future<Void> afterCreate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        var promise = Promise.<Void>promise();

        var item = MetricPluginConfigStore.getStore().getItem(entity.getLong(ID));

        provisionMetric(item.getJsonArray(METRIC_PLUGIN_ENTITIES), NOT_AVAILABLE, item, false, promise);

        return Future.succeededFuture();
    }

    @Override
    protected void update(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var id = CommonUtil.getLong(routingContext.request().getParam(ID));

                var context = routingContext.body().asJsonObject();

                if (!context.containsKey(MetricPlugin.METRIC_PLUGIN_VARIABLES))
                {
                    context.put(ConfigDBConstants.GARBAGE_FIELDS, new JsonArray().add(MetricPlugin.METRIC_PLUGIN_VARIABLES));
                }

                // First Metric Plugin Config Store will be updated then metrics that uses metric plugin will be updated
                Bootstrap.configDBService().update(COLLECTION_METRIC_PLUGIN,
                        new JsonObject().put(FIELD_NAME, ID).put(VALUE, id), context, DEFAULT_USER, SYSTEM_REMOTE_ADDRESS, asyncResult ->
                        {
                            try
                            {
                                if (asyncResult.succeeded())
                                {
                                    MetricPluginConfigStore.getStore().updateItem(id);

                                    var futures = new ArrayList<Future<Void>>();

                                    var items = MetricConfigStore.getStore().getItemsByMapValueField(Metric.METRIC_CONTEXT, APIConstants.ENTITY_ID, id);

                                    for (var index = 0; index < items.size(); index++)
                                    {
                                        var item = items.getJsonObject(index);

                                        var promise = Promise.<Void>promise();

                                        futures.add(promise.future());

                                        var metricContext = item.getJsonObject(Metric.METRIC_CONTEXT);

                                        if (context.containsKey(METRIC_PLUGIN_VARIABLES))
                                        {
                                            metricContext.put(METRIC_PLUGIN_VARIABLES, context.getJsonObject(METRIC_PLUGIN_VARIABLES));
                                        }
                                        else
                                        {
                                            metricContext.remove(METRIC_PLUGIN_VARIABLES);
                                        }

                                        item.put(Metric.METRIC_CONTEXT, metricContext.mergeIn(context.getJsonObject(MetricPlugin.METRIC_PLUGIN_CONTEXT)));

                                        Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_METRIC,
                                                new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                                                item,
                                                routingContext.user().principal().getString(User.USER_NAME), routingContext.request().remoteAddress().host(),
                                                result ->
                                                {
                                                    if (result.succeeded())
                                                    {
                                                        MetricConfigStore.getStore().updateItem(item.getLong(ID));

                                                        promise.complete();
                                                    }
                                                    else
                                                    {
                                                        promise.fail(result.cause().getMessage());
                                                    }
                                                });
                                    }

                                    Future.join(futures).onComplete(result ->
                                    {
                                        if (result.succeeded())
                                        {
                                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(ID, id).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ENTITY_UPDATED, APIConstants.Entity.METRIC_PLUGIN.getName())));
                                        }
                                        else
                                        {
                                            this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, STATUS_FAIL)
                                                    .put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                                    .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.METRIC_PLUGIN_UPDATE_FAILED, result.cause().getMessage())));
                                        }
                                    });
                                }
                                else
                                {
                                    this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, STATUS_FAIL)
                                            .put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.METRIC_PLUGIN_UPDATE_FAILED, asyncResult.cause().getMessage())));
                                }
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);
                            }
                        });
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void unassign(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var updatedIds = routingContext.body().asJsonObject().getJsonArray(APIConstants.REQUEST_PARAM_IDS);

                var item = MetricPluginConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID)));

                // based on entity.type find assigned metrics from collections

                var objects = item.getString(METRIC_PLUGIN_ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.OBJECT.getName()) ? updatedIds : ObjectConfigStore.getStore().getItemsByGroups(updatedIds);

                var metrics = MetricConfigStore.getStore().getItemsByMapMultiValueField(Metric.METRIC_CONTEXT, APIConstants.ENTITY_ID, new JsonArray(new ArrayList(1)).add(CommonUtil.getLong(routingContext.request().getParam(ID))));

                var updatedMetrics = new ArrayList<JsonObject>();

                for (var index = 0; index < metrics.size(); index++)
                {
                    var metric = metrics.getJsonObject(index);

                    if (objects.contains(metric.getLong(Metric.METRIC_OBJECT)))
                    {
                        updatedMetrics.add(new JsonObject().put(ID, metric.getLong(ID))
                                .put(REMOTE_ADDRESS, routingContext.request().remoteAddress().host()).put(USER_NAME, routingContext.user().principal().getString(USER_NAME)));
                    }
                }

                NMSConstants.updateMetrics(updatedMetrics, EventBusConstants.EVENT_METRIC_UNPROVISION).onComplete(asyncResult ->
                {

                    var promise = Promise.<Void>promise();

                    if (asyncResult.succeeded())
                    {
                        // find updated entity ids and update in collection

                        var entities = item.getJsonArray(METRIC_PLUGIN_ENTITIES);

                        var changed = false;

                        for (var index = 0; index < updatedIds.size(); index++)
                        {
                            var id = updatedIds.getLong(index);

                            if (entities.contains(id))
                            {
                                changed = true;

                                entities.remove(id);
                            }
                        }

                        if (changed)
                        {
                            Bootstrap.configDBService().update(COLLECTION_METRIC_PLUGIN,
                                    new JsonObject().put(FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                                    new JsonObject().put(METRIC_PLUGIN_ENTITIES, entities),
                                    DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                                    response -> MetricPluginConfigStore.getStore().updateItem(item.getLong(ID)).onComplete(result -> promise.complete()));
                        }
                        else
                        {
                            promise.complete();
                        }
                    }
                    else
                    {
                        promise.fail(asyncResult.cause());
                    }

                    promise.future().onComplete(result ->
                    {

                        if (result.succeeded())
                        {
                            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_SUCCEED).put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.UNASSIGNED_SUCCEEDED, APIConstants.Entity.METRIC_PLUGIN.getName())));
                        }
                        else
                        {
                            this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                    .put(GlobalConstants.ERROR, result.failed() ? CommonUtil.formatStackTrace(result.cause().getStackTrace()) : UNKNOWN)
                                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                    .put(GlobalConstants.MESSAGE, String.format("Failed to un-assign %s, Possible reason: %s", APIConstants.Entity.METRIC_PLUGIN.getName(), result.failed() ? result.cause().getMessage() : UNKNOWN)));
                        }
                    });
                });
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void assign(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var promise = Promise.<Void>promise();

                var context = routingContext.body().asJsonObject();

                provisionMetric(context.getJsonArray(APIConstants.REQUEST_PARAM_IDS), context.getLong(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE, CommonUtil.getLong(GlobalConstants.NOT_AVAILABLE)),
                        MetricPluginConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID))), true, promise);

                promise.future().onComplete(result ->
                {

                    if (result.succeeded())
                    {
                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(STATUS, STATUS_SUCCEED)
                                .put(MESSAGE, String.format(InfoMessageConstants.ASSIGN_SUCCEEDED, APIConstants.Entity.METRIC_PLUGIN.getName())));
                    }
                    else
                    {
                        this.send(routingContext, new JsonObject().put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL).put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                                .put(GlobalConstants.ERROR, result.failed() ? CommonUtil.formatStackTrace(result.cause().getStackTrace()) : UNKNOWN)
                                .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                .put(GlobalConstants.MESSAGE, String.format("Failed to assign %s, Possible reason: %s", APIConstants.Entity.METRIC_PLUGIN.getName(), result.failed() ? result.cause().getMessage() : UNKNOWN)));
                    }
                });
            }
            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(STATUS, STATUS_FAIL).put(MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void provision(RoutingContext routingContext)
    {
        try
        {
            var futures = new ArrayList<Future<Void>>();

            var context = routingContext.body().asJsonObject();

            var metricPlugins = MetricPluginConfigStore.getStore().getItemsByValue(MetricPlugin.METRIC_PLUGIN_PROTOCOL, NMSConstants.Protocol.CUSTOM.getName());

            var protocol = CredentialProfileConfigStore.getStore().getItem(context.getLong(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE)).getString(CredentialProfile.CREDENTIAL_PROFILE_PROTOCOL);

            for (var index = 0; index < metricPlugins.size(); index++)
            {
                var metricPlugin = metricPlugins.getJsonObject(index);

                if (metricPlugin.containsKey(METRIC_PLUGIN_VENDOR) && metricPlugin.getString(METRIC_PLUGIN_VENDOR).equalsIgnoreCase(context.getString(AIOpsObject.OBJECT_VENDOR))
                        && metricPlugin.getString(METRIC_PLUGIN_TYPE).equalsIgnoreCase(context.getString(AIOpsObject.OBJECT_TYPE))
                        && metricPlugin.getJsonObject(METRIC_PLUGIN_CONTEXT).containsKey(PluginEngineConstants.SCRIPT_PROTOCOL) && metricPlugin.getJsonObject(METRIC_PLUGIN_CONTEXT).getString(PluginEngineConstants.SCRIPT_PROTOCOL).equalsIgnoreCase(protocol))
                {
                    var promise = Promise.<Void>promise();

                    futures.add(promise.future());

                    metricPlugin.put(METRIC_PLUGIN_CREDENTIAL_PROFILE, context.getLong(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE));

                    metricPlugin.getJsonObject(MetricPlugin.METRIC_PLUGIN_CONTEXT).put(PORT, context.getValue(PORT));

                    provisionMetric(context.getJsonArray(APIConstants.REQUEST_PARAM_IDS), NOT_AVAILABLE, metricPlugin, true, promise);
                }
            }

            Future.join(futures).onComplete(result ->
            {
                if (result.succeeded())
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(STATUS, STATUS_SUCCEED)
                            .put(MESSAGE, String.format(InfoMessageConstants.ASSIGN_SUCCEEDED, APIConstants.Entity.METRIC_PLUGIN.getName())));
                }
                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(STATUS, STATUS_FAIL).put(MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
                }
            });
        }
        catch (Exception exception)
        {
            APIUtil.sendResponse(exception, routingContext);
        }
    }

    private void provisionMetric(JsonArray updatedIds, long credential, JsonObject item, boolean update, Promise<Void> promise)
    {
        try
        {
            if (item.containsKey(METRIC_PLUGIN_CONTEXT))
            {
                item.mergeIn(item.getJsonObject(METRIC_PLUGIN_CONTEXT));

                item.remove(METRIC_PLUGIN_CONTEXT);
            }

            if (credential != NOT_AVAILABLE)
            {
                item.put(MetricPlugin.METRIC_PLUGIN_CREDENTIAL_PROFILE, credential);
            }

            item.put(APIConstants.ENTITY_ID, item.getLong(ID));

            var metrics = new ArrayList<JsonObject>();

            var entities = item.getJsonArray(METRIC_PLUGIN_ENTITIES);

            var objects = item.getString(METRIC_PLUGIN_ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.GROUP.getName()) ?
                    ObjectConfigStore.getStore().getItemsByGroups(updatedIds) : updatedIds;

            for (var index = 0; index < objects.size(); index++)
            {
                var object = ObjectConfigStore.getStore().getItem(objects.getLong(index));

                if (object != null && !exists(object, item))
                {
                    NMSConstants.setMetricPluginContext(item, object, metrics);
                }
            }

            if (!metrics.isEmpty())
            {
                NMSConstants.updateMetrics(metrics, EventBusConstants.EVENT_METRIC_PROVISION);
            }

            if (update && (!metrics.isEmpty() || item.getString(METRIC_PLUGIN_ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.GROUP.getName())))
            {
                var ids = new JsonArray();

                for (var index = 0; index < updatedIds.size(); index++)
                {
                    var id = updatedIds.getLong(index);

                    if (!entities.contains(id))
                    {
                        ids.add(id);
                    }
                }

                if (!ids.isEmpty())
                {
                    Bootstrap.configDBService().update(COLLECTION_METRIC_PLUGIN,
                            new JsonObject().put(FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                            new JsonObject().put(METRIC_PLUGIN_ENTITIES, entities.addAll(ids)),
                            DEFAULT_USER, SYSTEM_REMOTE_ADDRESS,
                            result ->
                            {
                                MetricPluginConfigStore.getStore().updateItem(item.getLong(ID));

                                promise.complete();

                            });
                }

                else
                {
                    promise.complete();
                }
            }

            else
            {
                promise.complete();
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private boolean exists(JsonObject object, JsonObject item)
    {
        var exist = false;

        var metrics = MetricConfigStore.getStore().getItemsByObject(object.getLong(ID));

        for (var metric : metrics)
        {
            var metricContext = metric.getJsonObject(Metric.METRIC_CONTEXT);

            if (metricContext != null && metricContext.containsKey(APIConstants.ENTITY_ID)
                    && metricContext.getLong(APIConstants.ENTITY_ID).equals(item.getLong(APIConstants.ENTITY_ID)))
            {
                exist = true;

                break;
            }
        }

        return exist;
    }

    @Override
    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {
        try
        {
            var item = MetricPluginConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID)));

            var userGroups = UserConfigStore.getStore().getUserGroupsById(routingContext.user().principal().getLong(ID));

            if (item != null && item.getJsonArray(METRIC_PLUGIN_ENTITIES) != null && !item.getJsonArray(METRIC_PLUGIN_ENTITIES).isEmpty())
            {
                if (item.getString(METRIC_PLUGIN_ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.OBJECT.getName()))
                {
                    response.put(APIConstants.Entity.OBJECT.getName(), ObjectConfigStore.getStore().getItems(ObjectConfigStore.getStore().getIdsByGroups(userGroups, item.getJsonArray(METRIC_PLUGIN_ENTITIES))));
                }

                else
                {
                    response.put(APIConstants.Entity.GROUP.getName(), GroupConfigStore.getStore().getItems(GroupConfigStore.getStore().getQualifiedGroups(item.getJsonArray(METRIC_PLUGIN_ENTITIES), userGroups)));
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
        return Future.succeededFuture(response);
    }

    @Override
    protected Future<JsonObject> getEntityCountPreHook(JsonObject response)
    {
        try
        {
            var items = MetricPluginConfigStore.getStore().getItems();

            var userGroups = new JsonArray();

            if (response.containsKey(USER_ID))
            {
                userGroups = UserConfigStore.getStore().getUserGroupsById(response.getLong(USER_ID));
            }

            for (var index = 0; index < items.size(); index++)
            {
                var item = items.getJsonObject(index);

                var id = CommonUtil.getString(item.getLong(ID));

                var references = 0;

                if (item.getJsonArray(METRIC_PLUGIN_ENTITIES) != null && !item.getJsonArray(METRIC_PLUGIN_ENTITIES).isEmpty())
                {
                    if (item.getString(METRIC_PLUGIN_ENTITY_TYPE).equalsIgnoreCase(APIConstants.Entity.OBJECT.getName()))
                    {
                        references = ObjectConfigStore.getStore().getIdsByGroups(userGroups, item.getJsonArray(METRIC_PLUGIN_ENTITIES)).size();
                    }

                    else
                    {
                        references = GroupConfigStore.getStore().getQualifiedGroups(item.getJsonArray(METRIC_PLUGIN_ENTITIES), userGroups).size();
                    }
                }

                response.put(id, response.containsKey(id) ? response.getInteger(id) + references : references);
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return Future.succeededFuture(response);
    }
}
