/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

package com.mindarray.api;

import com.mindarray.*;
import com.mindarray.db.ConfigDBConstants;
import com.mindarray.eventbus.EventBusConstants;
import com.mindarray.log.LogEngineConstants;
import com.mindarray.log.LogPatternDetector;
import com.mindarray.store.*;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.DateTimeUtil;
import com.mindarray.util.LicenseUtil;
import com.mindarray.util.Logger;
import io.vertx.core.Future;
import io.vertx.core.Promise;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.ext.web.Router;
import io.vertx.ext.web.RoutingContext;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.http.HttpStatus;

import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Map;
import java.util.stream.Collectors;

import static com.mindarray.ErrorMessageConstants.INTERNAL_SERVER_EXCEPTION;
import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.RESPONSE_CODE;
import static com.mindarray.eventbus.EventBusConstants.*;
import static com.mindarray.eventbus.EventBusConstants.ChangeNotificationType.UPDATE_EVENT_SOURCE;
import static com.mindarray.log.LogEngineConstants.*;
import static org.apache.http.HttpStatus.SC_BAD_REQUEST;

public class LogParser extends AbstractAPI
{
    public static final String LOG_PARSER_NAME = "log.parser.name";
    public static final String LOG_PARSER_TYPE = "log.parser.type";
    public static final String LOG_PARSER_PLUGIN = "log.parser.plugin";
    public static final String LOG_PARSER_EVENT = "log.parser.event";
    public static final String LOG_PARSER_CONDITION = "log.parser.condition";
    public static final String LOG_PARSER_CONDITION_KEYWORDS = "log.parser.condition.keywords";
    public static final String LOG_PARSER_DATE_TIME_FORMAT = "log.parser.date.time.format";
    public static final String LOG_PARSER_DATE_TIME_FORMATTER_TYPE = "log.parser.date.time.formatter.type";
    public static final String LOG_PARSER_SOURCE_TYPE = "log.parser.source.type";
    public static final String LOG_PARSER_DELIMITER = "log.parser.delimiter";
    public static final String LOG_PARSER_UPLOAD = "log.parser.upload";
    public static final String LOG_PARSER_LOG_POSITIONS = "log.parser.log.positions";
    public static final String LOG_PARSER_ENTITIES = "log.parser.entities";
    public static final String LOG_PARSER_FIELDS = "log.parser.fields";
    public static final String LOG_PARSER_SOURCE_VENDOR = "log.parser.source.vendor";
    private static final Logger LOGGER = new Logger(LogParser.class, MOTADATA_API, "Log Parser API");

    public LogParser()
    {
        super("log-parsers", LogParserConfigStore.getStore(), new Logger(LogParser.class, MOTADATA_API, "Log Parser API"));
    }

    @Override
    public void init(Router router)
    {
        try
        {
            super.init(router);

            router.put("/" + endpoint + "/:id/assign").handler(this::assign);

            router.put("/" + endpoint + "/:id/unassign").handler(this::unassign);

            router.get("/" + endpoint + "/:id/references").handler(this::getReferences);
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    @Override
    protected Future<JsonObject> beforeCreate(RoutingContext routingContext)
    {
        var promise = Promise.<JsonObject>promise();

        var requestParameters = routingContext.body().asJsonObject();

        var items = LogParserPluginConfigStore.getStore().getItems();

        var valid = true;

        // check if unique
        for (var index = 0; index < items.size(); index++)
        {
            if (items.getJsonObject(index).getString(LogParserPlugin.LOG_PARSER_PLUGIN_NAME).equalsIgnoreCase(requestParameters.getString(LOG_PARSER_NAME)))
            {
                valid = false;

                send(routingContext, new JsonObject().put(STATUS, STATUS_FAIL).put(ERROR_CODE, ErrorCodes.ERROR_CODE_BAD_REQUEST)
                        .put(RESPONSE_CODE, SC_BAD_REQUEST).put(MESSAGE, String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, APIConstants.Entity.LOG_PARSER.getName())));

                promise.fail(String.format(ErrorMessageConstants.API_FIELD_UNIQUE_RULE, APIConstants.Entity.LOG_PARSER.getName()));

                break;
            }
        }

        if (valid)
        {
            if (LogParserType.CUSTOM.getName().equals(requestParameters.getString(LOG_PARSER_TYPE)))
            {
                requestParameters.put(LOG_PARSER_DATE_TIME_FORMATTER_TYPE, LogDateTimeFormatterType.NUMERIC.getName());

                requestParameters.put(LOG_PARSER_DATE_TIME_FORMAT, LogDateTimeUnit.SECONDS.getName());
            }

            else    // try to resolve datetime formatter for other parser type
            {
                var fields = requestParameters.getJsonArray(LOG_PARSER_FIELDS);

                if (!requestParameters.containsKey(LOG_PARSER_DATE_TIME_FORMAT) && fields != null && !fields.isEmpty())
                {
                    // add datetime format if not exist
                    for (var index = 0; index < fields.size(); index++)
                    {
                        var field = fields.getJsonObject(index);

                        if (field.getString(LogParserField.TYPE.getName()).equalsIgnoreCase(GlobalConstants.TIME_STAMP))
                        {
                            var timestamp = LogPatternDetector.detectTimePattern(field.getString(LogParserField.VALUE.getName()));

                            if (!timestamp.isEmpty())
                            {
                                requestParameters.put(LOG_PARSER_DATE_TIME_FORMATTER_TYPE, LogDateTimeFormatterType.FORMATTER.getName());

                                requestParameters.put(LOG_PARSER_DATE_TIME_FORMAT, timestamp.getString(LogPatternDetector.FORMAT));
                            }

                            else if (NumberUtils.isParsable(field.getString(LogParserField.VALUE.getName())))   // if date formatter not found check if timestamp is in seconds
                            {
                                requestParameters.put(LOG_PARSER_DATE_TIME_FORMATTER_TYPE, LogDateTimeFormatterType.NUMERIC.getName());

                                if (field.getString(LogParserField.VALUE.getName()).length() > 10)
                                {
                                    requestParameters.put(LOG_PARSER_DATE_TIME_FORMAT, LogDateTimeUnit.MILLIS.getName());
                                }

                                else
                                {
                                    requestParameters.put(LOG_PARSER_DATE_TIME_FORMAT, LogDateTimeUnit.SECONDS.getName());
                                }
                            }

                            break;
                        }
                    }
                }
            }


            // add other params
            requestParameters.put(PLUGIN_ID, LogParserConfigStore.getStore().generatePluginId())
                    .put(LOG_PARSER_ENTITIES, new JsonArray(new ArrayList<Long>(1)));

            promise.complete(requestParameters);
        }

        return promise.future();
    }

    @Override
    protected Future<Void> afterDelete(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, entity.put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.DELETE_LOG_PARSER.name()));

        return Future.succeededFuture();
    }

    @Override
    protected Future<Void> afterCreate(JsonObject entity, RoutingContext routingContext)
    {
        this.send(routingContext, entity);

        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, LogParserConfigStore.getStore().getItem(entity.getLong(ID)).put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_LOG_PARSER.name()));

        assignSource(routingContext, entity);

        if (routingContext.body().asJsonObject().containsKey(LOG_PARSER_UPLOAD) && routingContext.body().asJsonObject().getString(LOG_PARSER_UPLOAD).equalsIgnoreCase(YES))
        {
            upload(routingContext.body().asJsonObject().put(ID, entity.getLong(ID)));
        }

        return Future.succeededFuture();
    }

    private void assignSource(RoutingContext routingContext, JsonObject context)
    {
        try
        {
            var item = this.configStore.getItem(context.getLong(ID));

            if (!item.isEmpty() && item.containsKey(EVENT_SOURCE))
            {
                var updatedItem = new JsonObject();

                updatedItem.put(LOG_PARSER_ENTITIES, new JsonArray().add(item.getString(EVENT_SOURCE)));

                // Register Event to Event Object Manager
                Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION,
                        new JsonObject().put(EVENT_SOURCE, item.getString(EVENT_SOURCE))
                                .put(EventBusConstants.EVENT, EVENT_LOG)
                                .put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_EVENT_SOURCE)
                                .put(PLUGIN_ID, item.getInteger(PLUGIN_ID)));

                Bootstrap.configDBService().update(ConfigDBConstants.COLLECTION_LOG_PARSER,
                        new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, item.getLong(ID)),
                        updatedItem,
                        routingContext.user().principal().getString(User.USER_NAME), routingContext.request().remoteAddress().host(),
                        result ->
                        {
                            if (result.succeeded())
                            {
                                this.configStore.updateItem(item.getLong(ID));
                            }
                        });
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    private void assign(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var id = CommonUtil.getLong(routingContext.request().getParam(ID));

                var item = this.configStore.getItem(id);

                if (item != null)
                {
                    var requestParameters = routingContext.body().asJsonObject().getJsonArray(APIConstants.REQUEST_PARAMS);

                    var entities = item.getJsonArray(LOG_PARSER_ENTITIES);

                    if (entities != null && !entities.isEmpty())
                    {
                        var iterator = requestParameters.iterator();

                        while (iterator.hasNext())
                        {
                            var entity = CommonUtil.getString(iterator.next());

                            if (entities.contains(entity))
                            {
                                iterator.remove();
                            }
                        }

                        requestParameters.addAll(entities);
                    }

                    for (var index = 0; index < requestParameters.size(); index++)
                    {
                        Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION,
                                new JsonObject().put(EVENT_SOURCE, requestParameters.getString(index))
                                        .put(EventBusConstants.EVENT, EVENT_LOG)
                                        .put(CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.ADD_EVENT_SOURCE)
                                        .put(LogEngineConstants.SOURCE_GROUPS, GroupConfigStore.getStore().flatItemsByValueField(Group.FIELD_GROUP_NAME, item.getString(LogParser.LOG_PARSER_SOURCE_TYPE), GlobalConstants.ID))
                                        .put(PLUGIN_ID, item.getInteger(PLUGIN_ID)));
                    }

                    Bootstrap.configDBService().update(schema.getString(APIConstants.ENTITY_COLLECTION),
                            new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, id),
                            new JsonObject().put(LOG_PARSER_ENTITIES, requestParameters),
                            routingContext.user().principal().getString(User.USER_NAME), routingContext.request().remoteAddress().host(),
                            result ->
                            {
                                if (result.succeeded())
                                {
                                    this.configStore.updateItem(id).onComplete(asyncResult ->
                                    {
                                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                                                .put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                                .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.ASSIGN_SUCCEEDED, schema.getString(APIConstants.ENTITY_NAME)))
                                                .put(ID, id));

                                        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject().put(ID, id).put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_LOG_PARSER.name()));
                                    });
                                }

                                else
                                {
                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                                            .put(GlobalConstants.STATUS, STATUS_FAIL)
                                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_ASSIGN_FAILED, schema.getString(APIConstants.ENTITY_NAME), result.cause().getMessage()))
                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                            .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));
                                }
                            });
                }

                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                            .put(GlobalConstants.STATUS, STATUS_FAIL)
                            .put(MESSAGE, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, schema.getString(APIConstants.ENTITY_NAME)))
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND));
                }
            }

            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST)
                        .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                    .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
        }
    }

    private void unassign(RoutingContext routingContext)
    {
        try
        {
            var schema = CommonUtil.getEntitySchema(endpoint);

            if (schema != null)
            {
                var id = CommonUtil.getLong(routingContext.request().getParam(ID));

                var item = this.configStore.getItem(id);

                if (item != null)
                {
                    var requestParameters = routingContext.body().asJsonObject().getJsonArray(APIConstants.REQUEST_PARAMS);

                    var entities = item.getJsonArray(LOG_PARSER_ENTITIES);

                    var iterator = entities.iterator();

                    var removedEntities = new JsonArray();

                    while (iterator.hasNext())
                    {
                        var entity = CommonUtil.getString(iterator.next());

                        if (requestParameters.contains(entity))
                        {
                            removedEntities.add(entity);

                            iterator.remove();
                        }
                    }

                    Bootstrap.vertx().eventBus().send(EVENT_CHANGE_LOCAL_NOTIFICATION,
                            new JsonObject().put(PLUGIN_ID, item.getInteger(PLUGIN_ID))
                                    .put(GlobalConstants.ENTITIES, removedEntities)
                                    .put(CHANGE_NOTIFICATION_TYPE, UPDATE_EVENT_SOURCE));

                    Bootstrap.configDBService().update(schema.getString(APIConstants.ENTITY_COLLECTION),
                            new JsonObject().put(ConfigDBConstants.FIELD_NAME, ID).put(VALUE, id),
                            new JsonObject().put(LOG_PARSER_ENTITIES, entities),
                            routingContext.user().principal().getString(User.USER_NAME), routingContext.request().remoteAddress().host(),
                            result ->
                            {
                                if (result.succeeded())
                                {
                                    this.configStore.updateItem(id).onComplete(asyncResult ->
                                    {
                                        this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK)
                                                .put(GlobalConstants.STATUS, STATUS_SUCCEED)
                                                .put(GlobalConstants.MESSAGE, String.format(InfoMessageConstants.UNASSIGNED_SUCCEEDED, schema.getString(APIConstants.ENTITY_NAME)))
                                                .put(ID, id));

                                        Bootstrap.vertx().eventBus().publish(EventBusConstants.EVENT_CHANGE_NOTIFICATION, new JsonObject().put(ID, id).put(GlobalConstants.ENTITIES, removedEntities).put(EventBusConstants.CHANGE_NOTIFICATION_TYPE, ChangeNotificationType.UPDATE_LOG_PARSER.name()));
                                    });

                                }

                                else
                                {
                                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR).put(GlobalConstants.STATUS, STATUS_FAIL)
                                            .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.ENTITY_UNASSIGN_FAILED, schema.getString(APIConstants.ENTITY_NAME), result.cause().getMessage()))
                                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                                            .put(GlobalConstants.ERROR, CommonUtil.formatStackTrace(result.cause().getStackTrace())));
                                }
                            });
                }

                else
                {
                    this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_OK).put(GlobalConstants.STATUS, STATUS_FAIL)
                            .put(MESSAGE, String.format(ErrorMessageConstants.ITEM_NOT_FOUND_IN_STORE, schema.getString(APIConstants.ENTITY_NAME)))
                            .put(ERROR_CODE, ErrorCodes.ERROR_CODE_NO_ITEM_FOUND));
                }

            }

            else
            {
                this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_BAD_REQUEST).put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                        .put(GlobalConstants.MESSAGE, String.format(ErrorMessageConstants.SCHEMA_FILE_NOT_FOUND, endpoint)));
            }
        }

        catch (Exception exception)
        {
            LOGGER.error(exception);

            this.send(routingContext, new JsonObject().put(RESPONSE_CODE, HttpStatus.SC_INTERNAL_SERVER_ERROR)
                    .put(GlobalConstants.STATUS, GlobalConstants.STATUS_FAIL)
                    .put(ERROR, CommonUtil.formatStackTrace(exception.getStackTrace()))
                    .put(ERROR_CODE, ErrorCodes.ERROR_CODE_INTERNAL_ERROR)
                    .put(MESSAGE, String.format(INTERNAL_SERVER_EXCEPTION, exception.getMessage())));
        }
    }

    @Override
    protected Future<JsonObject> getReferencesPreHook(RoutingContext routingContext, JsonObject response)
    {
        try
        {
            response = new JsonObject();

            var item = LogParserConfigStore.getStore().getItem(CommonUtil.getLong(routingContext.request().getParam(ID)));

            var items = new JsonArray();

            var sources = new JsonArray();

            if (item.getJsonArray(LOG_PARSER_ENTITIES) != null && !item.getJsonArray(LOG_PARSER_ENTITIES).isEmpty())
            {
                item.getJsonArray(LOG_PARSER_ENTITIES).forEach(entity ->
                {
                    var ids = ObjectConfigStore.getStore().flatItemsByValueField(AIOpsObject.OBJECT_IP, CommonUtil.getString(entity), ID);

                    if (!ids.isEmpty())
                    {
                        var objects = ObjectConfigStore.getStore().getItems(ids);

                        for (var index = 0; index < objects.size(); index++)
                        {
                            if (LogEngineConstants.Category.valueOfName(objects.getJsonObject(index).getString(AIOpsObject.OBJECT_CATEGORY)) != null)
                            {
                                items.add(objects.getJsonObject(index));
                            }
                        }
                    }

                    else if (EventSourceConfigStore.getStore().getItemByValue(EVENT_SOURCE, entity.toString()) != null)
                    {
                        var object = EventSourceConfigStore.getStore().getItemByValue(EVENT_SOURCE, entity.toString());

                        sources.add(new JsonObject().put(EVENT_SOURCE, object.getString(EVENT_SOURCE)).put(LogEngineConstants.SOURCE_GROUPS, object.getJsonArray(LogEngineConstants.SOURCE_GROUPS)));
                    }

                    else
                    {
                        items.add(new JsonObject().put(AIOpsObject.OBJECT_IP, entity));
                    }
                });
            }

            if (!items.isEmpty())
            {
                response.put(APIConstants.Entity.OBJECT.getName(), items);
            }
            if (!sources.isEmpty())
            {
                response.put(APIConstants.Entity.EVENT_SOURCE.getName(), sources);  // In case of unprovisioned event.source, we will categorize into EVENT_SOURCE instead of Monitor
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return Future.succeededFuture(response);
    }

    @Override
    protected Future<JsonObject> getEntityCountPreHook(JsonObject response)
    {
        try
        {
            response = new JsonObject();

            var ids = LogParserConfigStore.getStore().getIds();

            for (var index = 0; index < ids.size(); index++)
            {
                var context = LogParserConfigStore.getStore().getItem(ids.getLong(index));

                if (context.getJsonArray(LOG_PARSER_ENTITIES) != null && !context.getJsonArray(LOG_PARSER_ENTITIES).isEmpty())
                {
                    response.put(CommonUtil.getString(ids.getLong(index)), context.getJsonArray(LOG_PARSER_ENTITIES).size());
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }

        return Future.succeededFuture(response);
    }

    protected void upload(JsonObject context)
    {
        try
        {
            if (context.containsKey(LogEngineConstants.SAMPLE_FILE_NAME))
            {
                var logFile = GlobalConstants.CURRENT_DIR + GlobalConstants.PATH_SEPARATOR + GlobalConstants.UPLOADS + GlobalConstants.PATH_SEPARATOR + context.getString(LogEngineConstants.SAMPLE_FILE_NAME);

                if (Bootstrap.vertx().fileSystem().existsBlocking(logFile))
                {
                    try (var lines = Files.lines(Paths.get(logFile)))
                    {
                        lines.forEach(line ->
                        {
                            if (LicenseUtil.updateUsedLogQuota(line.getBytes().length))
                            {
                                Bootstrap.vertx().eventBus().send(EVENT_LOG, new JsonObject()
                                        .put(EventBusConstants.EVENT_VOLUME_BYTES, line.getBytes().length)
                                        .put(EventBusConstants.EVENT, line)
                                        .put(ID, context.getValue(ID))
                                        .put(EVENT_SOURCE, "127.0.0.1")
                                        .put(RECEIVED_TIMESTAMP, DateTimeUtil.currentSeconds())
                                        .put(EVENT_TYPE, EVENT_LOG));
                            }
                        });
                    }
                }
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);
        }
    }

    public enum LogParserField
    {
        VALUE("log.parser.field.value"),
        NAME("log.parser.field.name"),
        TYPE("log.parser.field.type"),
        INDEX("log.parser.field.index");

        private static final Map<String, LogParserField> VALUES = Collections.unmodifiableMap(Arrays.stream(values())
                .collect(Collectors.toMap(LogParserField::getName, e -> e)));
        private final String name;

        LogParserField(String name)
        {
            this.name = name;
        }

        public static LogParserField valueOfName(String name)
        {
            return VALUES.get(name);
        }

        public String getName()
        {
            return name;
        }

    }
}
