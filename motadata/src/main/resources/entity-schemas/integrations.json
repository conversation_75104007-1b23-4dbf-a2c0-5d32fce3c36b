{"entity": "Integration", "collection": "integration", "version": "1.0", "author": "<PERSON><PERSON>", "props": [{"name": "integration.type", "title": "Integration Type", "type": "string", "rules": ["required"]}, {"name": "integration.context", "title": "Integration Context", "type": "map", "rules": ["required"]}, {"name": "integration.attributes", "title": "Integration Attributes", "type": "map"}], "entries": [{"type": "inline", "records": [{"integration.type": "ServiceOps", "integration.context": {"timeout": 60, "alert.reoccurrence.action": "create", "auto.sync": "off", "sync.interval": 2}, "integration.attributes": {}, "id": 10000000000001, "_type": "0"}, {"integration.type": "ServiceNow", "integration.context": {"timeout": 60, "request.type": "event", "alert.reoccurrence.action": "create", "auto.sync": "off", "sync.interval": 2}, "integration.attributes": {}, "id": 10000000000002, "_type": "0"}], "version": "1.1"}, {"type": "inline", "records": [{"integration.type": "Microsoft Teams", "integration.context": {"target": "https://graph.microsoft.com/v1.0/", "timeout": 60, "auto.sync": "off", "sync.interval": 2}, "integration.attributes": {}, "id": 10000000000003, "_type": "0"}], "version": "1.2"}, {"type": "inline", "records": [{"integration.type": "Atlassian Jira", "integration.context": {"timeout": 60, "alert.reoccurrence.action": "create", "auto.sync": "off", "proxy.enabled": "no", "sync.interval": 2}, "integration.attributes": {}, "id": 10000000000004, "_type": "0"}], "version": "1.3"}]}